/* 创建旅行计划页面样式 - 革新设计 */

/* 页面容器 */
.create-plan-container {
  min-height: 100vh;
  position: relative;
  overflow: hidden;
}

/* 渐变背景 */
.gradient-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #FF6B6B 0%, #4ECDC4 50%, #45B7D1 100%);
  z-index: -1;
}

/* 内容滚动区域 */
.content-scroll {
  height: 100vh;
  padding: 0 24rpx;
  box-sizing: border-box;
}

/* 页面标题 */
.page-header {
  padding: 88rpx 0 48rpx;
  text-align: center;
}

.header-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24rpx;
}

.header-icon {
  width: 120rpx;
  height: 120rpx;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20rpx);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.1);
}

.header-text {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.header-title {
  font-size: 48rpx;
  font-weight: 600;
  color: white;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.header-subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.2);
}

.weui-label_optional {
  margin-left: 8rpx;
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.6);
  background: rgba(255, 255, 255, 0.1);
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
  backdrop-filter: blur(10rpx);
}

.weui-input {
  color: rgba(255, 255, 255, 0.9);
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12rpx;
  padding: 16rpx 20rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10rpx);
}

.weui-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
  font-style: italic;
}

.weui-form__title {
  color: rgba(255, 255, 255, 0.95);
  font-weight: 600;
  text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.2);
  margin: 48rpx 24rpx 16rpx;
  font-size: 32rpx;
}

.weui-form__desc {
  color: rgba(255, 255, 255, 0.7);
  margin: 0 24rpx 16rpx;
  font-size: 24rpx;
}

.weui-btn-area {
  margin: 32rpx 24rpx;
  display: flex;
  gap: 16rpx;
}

.weui-btn {
  border-radius: 16rpx;
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
}

.weui-btn_primary {
  background: linear-gradient(135deg, #52c41a, #73d13d);
  border: none;
  box-shadow: 0 8rpx 24rpx rgba(82, 196, 26, 0.3);
}

.weui-btn_default {
  background: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.9);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

/* 背景装饰 */
.bg-decoration {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.gradient-orb {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(40rpx);
  animation: float 6s ease-in-out infinite;
}

.orb-1 {
  width: 200rpx;
  height: 200rpx;
  top: 10%;
  right: -50rpx;
  animation-delay: 0s;
}

.orb-2 {
  width: 150rpx;
  height: 150rpx;
  top: 60%;
  left: -30rpx;
  animation-delay: 2s;
}

.orb-3 {
  width: 120rpx;
  height: 120rpx;
  top: 80%;
  right: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

/* 内容滚动区域 */
.content-scroll {
  height: 100vh;
  position: relative;
  z-index: 1;
}

/* 页面头部 */
.page-header {
  padding: 40rpx 48rpx 32rpx;
  text-align: center;
}

.header-icon {
  margin-bottom: 16rpx;
}

.header-title {
  display: block;
  font-size: 40rpx;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.95);
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
  margin-bottom: 8rpx;
  letter-spacing: 2rpx;
}

.header-subtitle {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.2);
}

/* 表单卡片 */
.form-card {
  margin: 0 24rpx 120rpx;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20rpx) saturate(180%);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 32rpx;
  padding: 48rpx 32rpx;
  box-shadow:
    0 16rpx 64rpx rgba(0, 0, 0, 0.1),
    0 2rpx 0 rgba(255, 255, 255, 0.3) inset,
    0 -2rpx 0 rgba(0, 0, 0, 0.1) inset;
}

/* 表单分组 */
.form-section {
  margin-bottom: 48rpx;
}

.form-section:last-child {
  margin-bottom: 0;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.2);
}

.section-title text {
  margin-left: 12rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.95);
  text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.2);
}

/* 表单项 */
.form-item {
  margin-bottom: 32rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.item-label {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.label-text {
  font-size: 26rpx;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.2);
}

.required-mark {
  margin-left: 8rpx;
  width: 12rpx;
  height: 12rpx;
  background: linear-gradient(45deg, #FF6B6B, #FF8E8E);
  border-radius: 50%;
  font-size: 0;
  box-shadow: 0 2rpx 4rpx rgba(255, 107, 107, 0.3);
}

.optional-mark {
  margin-left: 8rpx;
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.6);
  background: rgba(255, 255, 255, 0.1);
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
  backdrop-filter: blur(10rpx);
}

/* 输入框样式 */
.form-input {
  width: 100%;
  height: 88rpx;
  background: rgba(255, 255, 255, 0.9);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 16rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #2d3436;
  box-shadow:
    0 4rpx 16rpx rgba(0, 0, 0, 0.05),
    0 1rpx 0 rgba(255, 255, 255, 0.5) inset;
  transition: all 0.3s ease;
}

.form-input:focus {
  border-color: #4ECDC4;
  box-shadow:
    0 4rpx 16rpx rgba(78, 205, 196, 0.2),
    0 1rpx 0 rgba(255, 255, 255, 0.5) inset;
}

.form-input::placeholder {
  color: #b2bec3;
  font-style: italic;
}

.input-counter {
  text-align: right;
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.6);
  margin-top: 8rpx;
}

/* 目的地选择器 - WeUI适配 */
.destination-selector {
  width: 100%;
}

.destination-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 20rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
}

.destination-display:active {
  background: rgba(255, 255, 255, 0.15);
  transform: scale(0.98);
}

.destination-text {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  flex: 1;
}

.destination-text:empty::before {
  content: '点击选择目的地';
  color: rgba(255, 255, 255, 0.5);
  font-style: italic;
}

/* WeUI按钮样式覆盖 */
.destination-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  background: rgba(87, 107, 149, 0.2) !important;
  border: 1rpx solid rgba(87, 107, 149, 0.3) !important;
  color: rgba(255, 255, 255, 0.9) !important;
}

.destination-btn:active {
  background: rgba(87, 107, 149, 0.3) !important;
}

/* 预算输入样式 */
.budget-input-wrapper {
  display: flex;
  align-items: center;
  width: 100%;
}

.currency-symbol {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 600;
  margin-right: 8rpx;
}

.budget-input {
  flex: 1;
}

/* 预算分配区域 */
.budget-allocation-section {
  margin-top: 32rpx;
}

.budget-item-wrapper {
  margin-bottom: 16rpx;
}

.budget-item-title {
  width: 100%;
}

.item-name-input {
  width: 100%;
}

.budget-item-footer {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.budget-amount-input {
  display: flex;
  align-items: center;
  flex: 1;
}

.amount-input {
  flex: 1;
}

.remove-btn {
  width: 48rpx !important;
  height: 48rpx !important;
  min-width: 48rpx !important;
  padding: 0 !important;
  background: rgba(255, 77, 79, 0.1) !important;
  border: 1rpx solid rgba(255, 77, 79, 0.2) !important;
}

.add-budget-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  background: rgba(87, 107, 149, 0.1) !important;
  border: 1rpx dashed rgba(87, 107, 149, 0.3) !important;
  color: rgba(255, 255, 255, 0.8) !important;
}

/* 预算统计 */
.budget-summary {
  background: rgba(255, 255, 255, 0.05) !important;
  border: 1rpx solid rgba(255, 255, 255, 0.1) !important;
}

.summary-amount {
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
}

.summary-amount.negative {
  color: #ff4d4f;
}

/* 参与人员步进器 */
.participant-stepper {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.participant-unit {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 时间显示 */
.duration-info {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.duration-text {
  font-size: 24rpx;
  color: #52c41a;
  font-weight: 500;
}

/* 备注文本域 */
.notes-textarea {
  width: 100%;
  min-height: 120rpx;
}



/* 日期选择器 */
.date-selector {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.date-item {
  flex: 1;
}

.date-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  background: rgba(255, 255, 255, 0.9);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 16rpx;
  padding: 0 24rpx;
  box-shadow:
    0 4rpx 16rpx rgba(0, 0, 0, 0.05),
    0 1rpx 0 rgba(255, 255, 255, 0.5) inset;
}

.date-text {
  font-size: 28rpx;
  color: #2d3436;
}

.date-separator {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40rpx;
  height: 40rpx;
}

.duration-display {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 16rpx;
  padding: 12rpx 20rpx;
  background: rgba(82, 196, 26, 0.1);
  border: 1rpx solid rgba(82, 196, 26, 0.2);
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
}

.duration-display text {
  margin-left: 8rpx;
  font-size: 24rpx;
  color: #52c41a;
  font-weight: 500;
}

/* 预算输入 */
.budget-input {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.9);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 16rpx;
  padding: 0 24rpx;
  box-shadow:
    0 4rpx 16rpx rgba(0, 0, 0, 0.05),
    0 1rpx 0 rgba(255, 255, 255, 0.5) inset;
}

.currency-symbol {
  font-size: 28rpx;
  color: #45B7D1;
  font-weight: 600;
  margin-right: 8rpx;
}

.budget-number {
  flex: 1;
  border: none;
  background: transparent;
  padding: 0;
  height: 88rpx;
  line-height: 88rpx;
}

/* 自定义预算分配 */
.budget-allocation {
  margin-top: 24rpx;
  padding: 24rpx;
  background: rgba(255, 255, 255, 0.05);
  border: 1rpx solid rgba(255, 255, 255, 0.1);
  border-radius: 16rpx;
  backdrop-filter: blur(10rpx);
}

.allocation-title {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 16rpx;
  font-weight: 500;
}

.allocation-items {
  margin-bottom: 16rpx;
}

.allocation-item {
  margin-bottom: 16rpx;
  padding: 16rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.item-header {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.item-name-input {
  flex: 1;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.9);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 8rpx;
  padding: 0 16rpx;
  font-size: 24rpx;
  color: #2d3436;
}

.remove-item-btn {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 77, 79, 0.1);
  border: 1rpx solid rgba(255, 77, 79, 0.2);
  border-radius: 50%;
  margin-left: 12rpx;
  padding: 0;
  font-size: 0;
}

.item-amount {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.9);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 8rpx;
  padding: 0 16rpx;
}

.amount-input {
  flex: 1;
  height: 60rpx;
  border: none;
  background: transparent;
  font-size: 24rpx;
  color: #2d3436;
  padding: 0;
}

.add-budget-item-btn {
  width: 100%;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  background: rgba(69, 183, 209, 0.1);
  border: 1rpx dashed rgba(69, 183, 209, 0.3);
  border-radius: 12rpx;
  font-size: 24rpx;
  color: #45B7D1;
  font-weight: 500;
  margin: 0 0 16rpx 0;
  padding: 0;
}

.budget-summary {
  padding-top: 16rpx;
  border-top: 1rpx solid rgba(255, 255, 255, 0.2);
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.summary-label {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

.summary-amount {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 600;
}

.summary-amount.negative {
  color: #ff4d4f;
}

/* 参与人员选择器 */
.participant-selector {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.participant-counter {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.counter-btn {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.9);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  padding: 0;
  margin: 0;
  font-size: 0;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.counter-btn:not([disabled]):active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.counter-btn[disabled] {
  opacity: 0.5;
}

.counter-number {
  font-size: 32rpx;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.95);
  text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.2);
  min-width: 60rpx;
  text-align: center;
}

.participant-unit {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
}



/* 文本域 */
.form-textarea {
  width: 100%;
  min-height: 120rpx;
  background: rgba(255, 255, 255, 0.9);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 16rpx;
  padding: 20rpx 24rpx;
  font-size: 26rpx;
  color: #2d3436;
  box-shadow:
    0 4rpx 16rpx rgba(0, 0, 0, 0.05),
    0 1rpx 0 rgba(255, 255, 255, 0.5) inset;
}

.textarea-counter {
  text-align: right;
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.6);
  margin-top: 8rpx;
}

/* WeUI底部按钮区域 */
.cancel-btn {
  flex: 1;
  background: rgba(255, 255, 255, 0.2) !important;
  color: rgba(255, 255, 255, 0.8) !important;
  border: 1rpx solid rgba(255, 255, 255, 0.3) !important;
}

.save-btn {
  flex: 2;
  background: linear-gradient(135deg, #52c41a, #73d13d) !important;
  border: none !important;
  box-shadow: 0 8rpx 24rpx rgba(82, 196, 26, 0.3);
}

.save-btn[disabled] {
  background: rgba(255, 255, 255, 0.2) !important;
  color: rgba(255, 255, 255, 0.5) !important;
  box-shadow: none !important;
}

.safe-bottom {
  height: calc(120rpx + env(safe-area-inset-bottom));
}

/* WeUI加载和提示组件样式覆盖 */
.weui-loading {
  backdrop-filter: blur(20rpx);
}

.weui-toast {
  backdrop-filter: blur(20rpx);
}

/* ==================== 革新设计新增样式 ==================== */

/* 进度指示器 */
.progress-indicator {
  margin: 0 0 48rpx;
  padding: 0 32rpx;
}

.progress-bar {
  height: 8rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4rpx;
  overflow: hidden;
  margin-bottom: 16rpx;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4ECDC4 0%, #45B7D1 100%);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  display: block;
}

/* 毛玻璃卡片 */
.glass-card {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20rpx) saturate(180%);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 32rpx;
  box-shadow:
    0 16rpx 64rpx rgba(0, 0, 0, 0.1),
    0 2rpx 0 rgba(255, 255, 255, 0.3) inset;
  overflow: hidden;
  margin-bottom: 32rpx;
}

/* 卡片标题 */
.card-header {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 32rpx 32rpx 24rpx;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: white;
  text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.2);
}

/* 表单内容 */
.form-content {
  padding: 32rpx;
}

/* 表单项 */
.form-item {
  margin-bottom: 32rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

/* 标签 */
.item-label {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 16rpx;
}

.label-text {
  font-size: 28rpx;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.2);
}

.required-star {
  color: #FF6B6B;
  font-weight: bold;
  font-size: 28rpx;
}

.optional-tag {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.6);
  background: rgba(255, 255, 255, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  backdrop-filter: blur(10rpx);
}

/* 输入框容器 */
.input-container {
  position: relative;
}

.text-input {
  width: 100%;
  min-height: 80rpx;
  background: rgba(255, 255, 255, 0.95);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 16rpx;
  padding: 24rpx 80rpx 24rpx 20rpx;
  font-size: 28rpx;
  color: #2d3436;
  backdrop-filter: blur(10rpx);
  box-sizing: border-box;
  line-height: 1.4;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.text-input::placeholder {
  color: #b2bec3;
}

.char-counter {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 20rpx;
  color: #636e72;
  pointer-events: none;
  background: rgba(255, 255, 255, 0.8);
  padding: 2rpx 6rpx;
  border-radius: 8rpx;
}

/* 选择器容器 */
.selector-container {
  background: rgba(255, 255, 255, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 16rpx;
  backdrop-filter: blur(10rpx);
  overflow: hidden;
}

.selector-display {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 24rpx 20rpx;
}

.selector-text {
  flex: 1;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
}

.selector-text.placeholder {
  color: rgba(255, 255, 255, 0.5);
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 16rpx;
  padding: 48rpx 32rpx 32rpx;
}

.btn-secondary {
  flex: 1;
  background: rgba(255, 255, 255, 0.15);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 16rpx;
  color: rgba(255, 255, 255, 0.9);
  font-size: 28rpx;
  font-weight: 500;
  padding: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  backdrop-filter: blur(10rpx);
}

.btn-secondary:active {
  background: rgba(255, 255, 255, 0.25);
  transform: scale(0.98);
}

.btn-primary {
  flex: 2;
  background: linear-gradient(135deg, #4ECDC4 0%, #44A08D 100%);
  border: none;
  border-radius: 16rpx;
  color: white;
  font-size: 28rpx;
  font-weight: 500;
  padding: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  box-shadow: 0 8rpx 24rpx rgba(78, 205, 196, 0.3);
}

.btn-primary:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 12rpx rgba(78, 205, 196, 0.4);
}

.btn-primary.create-btn {
  background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
  box-shadow: 0 8rpx 24rpx rgba(82, 196, 26, 0.3);
}

.btn-primary.create-btn:active {
  box-shadow: 0 4rpx 12rpx rgba(82, 196, 26, 0.4);
}

/* 邀请好友共建样式 */
.invite-section {
  background: rgba(255, 120, 117, 0.1);
  border: 1rpx solid rgba(255, 120, 117, 0.2);
  border-radius: 20rpx;
  padding: 32rpx;
  margin-top: 24rpx;
}

.invite-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.invite-label {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.invite-description {
  margin-top: 16rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid rgba(255, 120, 117, 0.2);
}

/* {{ AURA-X: Add - 邀请按钮样式. Approval: 寸止(ID:1738056000). }} */
.invite-btn {
  background: rgba(78, 205, 196, 0.1);
  border: 2rpx solid #4ECDC4;
  border-radius: 48rpx;
  padding: 12rpx 24rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 26rpx;
  color: #4ECDC4;
  transition: all 0.3s ease;
}

.invite-btn:active {
  background: rgba(78, 205, 196, 0.2);
  transform: scale(0.95);
}

.description-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
}

