<!--创建旅行计划页面 - 革新设计-->
<view class="create-plan-container">
  <!-- 渐变背景 -->
  <view class="gradient-background"></view>

  <!-- 页面内容 -->
  <scroll-view class="content-scroll" scroll-y="true" enhanced="true" show-scrollbar="false">

    <!-- 简洁页面标题 -->
    <view class="page-header">
      <view class="header-content">
        <view class="header-icon">
          <custom-icon name="airplane" size="54" color="#4ECDC4" />
        </view>
        <view class="header-text">
          <text class="header-title">创建旅行计划</text>
          <text class="header-subtitle">开始规划你的精彩旅程</text>
        </view>
      </view>
    </view>

    <!-- 简化的进度指示器 -->
    <view class="progress-indicator">
      <view class="progress-bar">
        <view class="progress-fill" style="width: {{(currentStep / 3) * 100}}%"></view>
      </view>
      <text class="progress-text">第 {{currentStep}} 步，共 3 步</text>
    </view>

    <!-- 表单内容 -->
    <form bindsubmit="savePlan" class="plan-form">

      <!-- 步骤1：基本信息 -->
      <view wx:if="{{currentStep === 1}}" class="form-step">
        <view class="glass-card">
          <view class="card-header">
            <custom-icon name="edit" size="30" color="#4ECDC4" />
            <text class="card-title">基本信息</text>
          </view>

          <view class="form-content">
            <!-- 旅行标题 -->
            <view class="form-item">
              <view class="item-label">
                <text class="label-text">旅行标题</text>
                <text class="required-star">*</text>
              </view>
              <view class="input-container">
                <input
                  class="text-input"
                  placeholder="给你的旅行起个名字吧"
                  value="{{formData.title}}"
                  bindinput="onTitleInput"
                  maxlength="30"
                />
                <view class="char-counter">{{formData.title.length}}/30</view>
              </view>
            </view>

            <!-- 目的地选择 -->
            <view class="form-item">
              <view class="item-label">
                <text class="label-text">目的地</text>
                <text class="required-star">*</text>
              </view>
              <view class="selector-container" bindtap="selectDestination">
                <view class="selector-display">
                  <custom-icon name="map-pin" size="26" color="#4ECDC4" />
                  <text class="selector-text {{!formData.destination ? 'placeholder' : ''}}">
                    {{formData.destination || '点击选择目的地'}}
                  </text>
                  <custom-icon name="arrow-right" size="22" color="#b2bec3" />
                </view>
              </view>
            </view>

            <!-- 参与人数 - 使用微信官方picker -->
            <view class="form-item">
              <view class="item-label">
                <text class="label-text">参与人数</text>
              </view>
              <picker
                mode="selector"
                range="{{participantOptions}}"
                value="{{participantIndex}}"
                bindchange="onParticipantChange"
              >
                <view class="selector-container">
                  <view class="selector-display">
                    <custom-icon name="user" size="26" color="#4ECDC4" />
                    <text class="selector-text">{{participantOptions[participantIndex]}}</text>
                    <custom-icon name="arrow-right" size="22" color="#b2bec3" />
                  </view>
                </view>
              </picker>
            </view>

            <!-- {{ AURA-X: Modify - 将开关改为按钮，点击直接显示邀请弹窗. Approval: 寸止(ID:1738056000). }} -->
            <!-- 邀请好友共建 -->
            <view class="form-item invite-section">
              <view class="invite-header">
                <view class="invite-label">
                  <custom-icon name="heart" size="24" color="#ff7875" />
                  <text class="label-text">邀请好友共建</text>
                </view>
                <button
                  class="invite-btn"
                  bindtap="showInviteOption"
                  size="mini">
                  <custom-icon name="friends" size="16" color="#4ECDC4" />
                  <text>邀请协作</text>
                </button>
              </view>
              <view class="invite-description">
                <text class="description-text">创建计划后可以邀请好友一起规划旅行，共同编辑行程和记录</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 步骤2：时间和预算 -->
      <view wx:if="{{currentStep === 2}}" class="form-step">
        <!-- 时间安排卡片 -->
        <view class="glass-card">
          <view class="card-header">
            <custom-icon name="calendar" size="30" color="#FF6B6B" />
            <text class="card-title">时间安排</text>
          </view>

          <view class="form-content">
            <!-- 日期选择区域 -->
            <view class="date-selection">
              <!-- 开始日期 -->
              <view class="form-item">
                <view class="item-label">
                  <text class="label-text">开始日期</text>
                  <text class="required-star">*</text>
                </view>
                <picker
                  mode="date"
                  value="{{formData.startDate}}"
                  start="{{today}}"
                  bindchange="onStartDateChange"
                >
                  <view class="selector-container">
                    <view class="selector-display">
                      <custom-icon name="calendar" size="26" color="#4ECDC4" />
                      <text class="selector-text {{!formData.startDate ? 'placeholder' : ''}}">
                        {{formData.startDate || '选择开始日期'}}
                      </text>
                      <custom-icon name="arrow-right" size="22" color="#b2bec3" />
                    </view>
                  </view>
                </picker>
              </view>

              <!-- 结束日期 -->
              <view class="form-item">
                <view class="item-label">
                  <text class="label-text">结束日期</text>
                  <text class="required-star">*</text>
                </view>
                <picker
                  mode="date"
                  value="{{formData.endDate}}"
                  start="{{formData.startDate || today}}"
                  bindchange="onEndDateChange"
                >
                  <view class="selector-container">
                    <view class="selector-display">
                      <custom-icon name="calendar" size="26" color="#4ECDC4" />
                      <text class="selector-text {{!formData.endDate ? 'placeholder' : ''}}">
                        {{formData.endDate || '选择结束日期'}}
                      </text>
                      <custom-icon name="arrow-right" size="22" color="#b2bec3" />
                    </view>
                  </view>
                </picker>
              </view>
            </view>

            <!-- 旅行天数显示 -->
            <view wx:if="{{duration > 0}}" class="duration-highlight">
              <view class="duration-icon">
                <custom-icon name="clock" size="26" color="#52c41a" />
              </view>
              <view class="duration-info">
                <text class="duration-number">{{duration}}</text>
                <text class="duration-unit">天精彩旅程</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 预算设置卡片 -->
        <view class="glass-card">
          <view class="card-header">
            <custom-icon name="wallet" size="30" color="#faad14" />
            <text class="card-title">预算设置</text>
          </view>

          <view class="form-content">
            <!-- 总预算 -->
            <view class="form-item">
              <view class="item-label">
                <text class="label-text">总预算</text>
                <text class="optional-tag">选填</text>
              </view>
              <view class="budget-input-container">
                <text class="currency-symbol">¥</text>
                <input
                  class="budget-input"
                  type="digit"
                  placeholder="设置旅行预算"
                  value="{{formData.budget}}"
                  bindinput="onBudgetInput"
                />
              </view>
            </view>

            <!-- 备注说明 -->
            <view class="form-item">
              <view class="item-label">
                <text class="label-text">备注说明</text>
                <text class="optional-tag">选填</text>
              </view>
              <view class="textarea-container">
                <textarea
                  class="notes-textarea"
                  placeholder="添加一些备注信息，比如特殊需求、注意事项等"
                  value="{{formData.notes}}"
                  bindinput="onNotesInput"
                  maxlength="200"
                  auto-height
                />
                <view class="char-counter">{{formData.notes.length}}/200</view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 步骤3：确认创建 -->
      <view wx:if="{{currentStep === 3}}" class="form-step">
        <view class="glass-card">
          <view class="card-header">
            <custom-icon name="check" size="30" color="#52c41a" />
            <text class="card-title">确认信息</text>
          </view>

          <view class="form-content">
            <!-- 计划预览卡片 -->
            <view class="plan-preview">
              <view class="preview-header">
                <view class="plan-title">{{formData.title}}</view>
                <view class="plan-destination">
                  <custom-icon name="map-pin" size="22" color="#4ECDC4" />
                  <text>{{formData.destination}}</text>
                </view>
              </view>

              <view class="preview-details">
                <view class="detail-row">
                  <view class="detail-item">
                    <custom-icon name="calendar" size="22" color="#FF6B6B" />
                    <view class="detail-text">
                      <text class="detail-label">出行时间</text>
                      <text class="detail-value">{{formData.startDate}} 至 {{formData.endDate}}</text>
                    </view>
                  </view>
                </view>

                <view class="detail-row">
                  <view class="detail-item">
                    <custom-icon name="clock" size="22" color="#52c41a" />
                    <view class="detail-text">
                      <text class="detail-label">旅行天数</text>
                      <text class="detail-value">{{duration}} 天</text>
                    </view>
                  </view>

                  <view class="detail-item">
                    <custom-icon name="user" size="22" color="#4ECDC4" />
                    <view class="detail-text">
                      <text class="detail-label">参与人数</text>
                      <text class="detail-value">{{participantOptions[participantIndex]}}</text>
                    </view>
                  </view>
                </view>

                <view wx:if="{{formData.budget > 0}}" class="detail-row">
                  <view class="detail-item">
                    <custom-icon name="wallet" size="22" color="#faad14" />
                    <view class="detail-text">
                      <text class="detail-label">预算</text>
                      <text class="detail-value">¥{{formData.budget}}</text>
                    </view>
                  </view>
                </view>

                <view wx:if="{{formData.notes}}" class="detail-row full-width">
                  <view class="detail-item">
                    <custom-icon name="edit" size="22" color="#636e72" />
                    <view class="detail-text">
                      <text class="detail-label">备注</text>
                      <text class="detail-value notes">{{formData.notes}}</text>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 底部操作按钮 -->
      <view class="action-buttons">
        <button wx:if="{{currentStep > 1}}" class="btn-secondary" bindtap="prevStep">
          <custom-icon name="arrow-left" size="22" color="#636e72" />
          <text>上一步</text>
        </button>

        <button wx:if="{{currentStep < 3}}" class="btn-primary" bindtap="nextStep" disabled="{{!canNextStep}}">
          <text>下一步</text>
          <custom-icon name="arrow-right" size="22" color="#fff" />
        </button>

        <button wx:if="{{currentStep === 3}}" class="btn-primary create-btn" form-type="submit" disabled="{{saving}}" loading="{{saving}}">
          <custom-icon name="check" size="22" color="#fff" wx:if="{{!saving}}" />
          <text>{{saving ? '创建中...' : '创建计划'}}</text>
        </button>
      </view>

    </form>

    <!-- 底部安全距离 -->
    <view class="safe-area-bottom"></view>

  </scroll-view>

  <!-- 加载遮罩 -->
  <view wx:if="{{loading}}" class="loading-mask">
    <view class="loading-container">
      <view class="loading-spinner">
        <view class="spinner-dot"></view>
        <view class="spinner-dot"></view>
        <view class="spinner-dot"></view>
      </view>
      <text class="loading-text">{{loadingText}}</text>
    </view>
  </view>

  <!-- Toast提示 -->
  <van-toast id="van-toast" />

</view>