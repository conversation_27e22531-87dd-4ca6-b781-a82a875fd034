/* subpackages/travel-planning/index.wxss */

.travel-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #FF6B6B 0%, #4ECDC4 50%, #45B7D1 100%);
  position: relative;
}

/* 背景装饰 */
.bg-decoration {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.gradient-orb {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20rpx);
  animation: float 6s ease-in-out infinite;
}

.orb-1 {
  width: 200rpx;
  height: 200rpx;
  top: 10%;
  right: 10%;
  animation-delay: 0s;
}

.orb-2 {
  width: 150rpx;
  height: 150rpx;
  bottom: 20%;
  left: 15%;
  animation-delay: 2s;
}

.orb-3 {
  width: 100rpx;
  height: 100rpx;
  top: 50%;
  right: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20rpx) rotate(180deg); }
}

/* 内容滚动区域 */
.content-scroll {
  height: 100vh;
  position: relative;
  z-index: 1;
}

/* 欢迎区域 */
.welcome-section {
  padding: 32rpx 32rpx 24rpx;
}

.welcome-card {
  padding: 32rpx;
}

.welcome-content {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.welcome-icon {
  width: 80rpx;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}

.welcome-text {
  flex: 1;
}

.welcome-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: white;
  margin-bottom: 8rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.welcome-subtitle {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

.welcome-stats {
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-number {
  font-size: 28rpx;
  font-weight: 600;
  color: white;
  margin-bottom: 4rpx;
}

.stat-label {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.7);
}

.stat-divider {
  width: 1rpx;
  height: 40rpx;
  background: rgba(255, 255, 255, 0.2);
}

/* 通用区域样式 */
.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx 16rpx;
  margin-top: 24rpx;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.section-title text {
  font-size: 28rpx;
  font-weight: 600;
  color: white;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.section-action {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

.section-action:active {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(0.95);
}

/* 当前计划卡片 */
.current-plan-section {
  padding: 0 32rpx 24rpx;
}

.current-plan-card {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
  padding: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.plan-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.plan-info {
  flex: 1;
}

.plan-title {
  font-size: 28rpx;
  font-weight: 600;
  color: white;
  margin-bottom: 8rpx;
  display: block;
}

.plan-meta {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.plan-destination {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.8);
}

.plan-status {
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 500;
}

.status-planning {
  background: rgba(250, 173, 20, 0.2);
  color: #faad14;
  border: 1rpx solid rgba(250, 173, 20, 0.3);
}

.status-ongoing {
  background: rgba(82, 196, 26, 0.2);
  color: #52c41a;
  border: 1rpx solid rgba(82, 196, 26, 0.3);
}

.status-completed {
  background: rgba(24, 144, 255, 0.2);
  color: #1890ff;
  border: 1rpx solid rgba(24, 144, 255, 0.3);
}

.plan-progress {
  margin-bottom: 20rpx;
}

.progress-item {
  margin-bottom: 16rpx;
}

.progress-item:last-child {
  margin-bottom: 0;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.progress-label {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.8);
}

.progress-value {
  font-size: 22rpx;
  font-weight: 500;
  color: white;
}

.progress-bar {
  height: 6rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #52c41a, #73d13d);
  border-radius: 3rpx;
  transition: width 0.3s ease;
}

.budget-fill {
  background: linear-gradient(90deg, #1890ff, #40a9ff);
}

.plan-actions {
  display: flex;
  gap: 12rpx;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  padding: 16rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  font-weight: 500;
  border: none;
}

.action-btn.primary {
  background: linear-gradient(135deg, #FF6B6B, #FF8E8E);
  color: white;
}

.action-btn.secondary {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.action-btn:active {
  transform: scale(0.95);
}

/* 快速操作网格 */
.quick-actions-section {
  padding: 0 32rpx 24rpx;
}

/* {{ AURA-X: Modify - 改为3列布局，更加对称美观. Approval: 寸止(ID:1738056000). }} */
.quick-actions-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 12rpx;
}

.quick-action-item {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 16rpx;
  padding: 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.quick-action-item:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.2);
}

/* {{ AURA-X: Modify - 第二行按钮不跨列但要对齐，创建计划与探索目的地对齐，快速记录与邀请协作对齐. Approval: 寸止(ID:1738056000). }} */
.quick-action-item.large-item {
  padding: 32rpx 24rpx;
  min-height: 140rpx;
}

.quick-action-item.large-item:nth-child(4) {
  grid-column: 1;
  grid-column-start: 1;
}

.quick-action-item.large-item:nth-child(5) {
  grid-column: 3;
  grid-column-start: 3;
}

.action-icon {
  width: 56rpx;
  height: 56rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12rpx;
}

.action-icon.create {
  background: linear-gradient(135deg, #52c41a, #73d13d);
}

.action-icon.join {
  background: linear-gradient(135deg, #4ECDC4, #45B7D1);
}

.action-icon.invite {
  background: linear-gradient(135deg, #ff7875, #ff9c6e);
}

.action-icon.record {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
}

.action-icon.explore {
  background: linear-gradient(135deg, #722ed1, #b37feb);
}

.action-icon.stats {
  background: linear-gradient(135deg, #faad14, #ffd666);
}

.action-title {
  font-size: 24rpx;
  font-weight: 500;
  color: white;
  margin-bottom: 4rpx;
  display: block;
}

.action-desc {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.7);
  display: block;
}

/* 我的计划列表 */
.my-plans-section {
  padding: 0 32rpx 24rpx;
}

.empty-state {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
  padding: 48rpx 32rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.empty-icon {
  margin-bottom: 24rpx;
  opacity: 0.6;
}

.empty-title {
  font-size: 28rpx;
  font-weight: 500;
  color: white;
  margin-bottom: 8rpx;
  display: block;
}

.empty-desc {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 24rpx;
  display: block;
}

.empty-action {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 24rpx;
  background: linear-gradient(135deg, #4ECDC4, #44A08D);
  color: white;
  border-radius: 24rpx;
  font-size: 24rpx;
  font-weight: 500;
  border: none;
}

.plans-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.plan-card {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.plan-card:active {
  transform: scale(0.98);
  background: rgba(255, 255, 255, 0.2);
}

.plan-card-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.plan-card-info {
  flex: 1;
}

.plan-card-title {
  font-size: 26rpx;
  font-weight: 500;
  color: white;
  margin-bottom: 8rpx;
  display: block;
}

.plan-card-meta {
  display: flex;
  align-items: center;
  gap: 8rpx;
  flex-wrap: wrap;
}

.plan-card-destination,
.plan-card-date {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.7);
}

/* {{ AURA-X: Add - 添加角色标识样式. Approval: 寸止(ID:1738056000). }} */
.plan-card-badges {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 6rpx;
}

.plan-card-status {
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  font-size: 18rpx;
  font-weight: 500;
}

.plan-card-role {
  padding: 3rpx 6rpx;
  border-radius: 6rpx;
  font-size: 16rpx;
  font-weight: 400;
}

.role-collaborator {
  background: rgba(255, 107, 107, 0.2);
  color: #FF6B6B;
  border: 1rpx solid rgba(255, 107, 107, 0.3);
}

.plan-card-content {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.plan-card-stats {
  display: flex;
  gap: 16rpx;
}

.card-stat-item {
  display: flex;
  align-items: center;
  gap: 4rpx;
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.7);
}

.plan-card-progress {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.card-progress-bar {
  flex: 1;
  height: 4rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2rpx;
  overflow: hidden;
}

.card-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #52c41a, #73d13d);
  border-radius: 2rpx;
  transition: width 0.3s ease;
}

.card-progress-text {
  font-size: 18rpx;
  color: rgba(255, 255, 255, 0.7);
  white-space: nowrap;
}



/* 底部安全距离 */
.safe-bottom {
  height: 120rpx;
}

/* 计划选择弹窗 */
.plan-selector-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(10rpx);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.plan-selector-modal {
  width: 600rpx;
  max-height: 70vh;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 32rpx;
  box-shadow: 0 16rpx 64rpx rgba(0, 0, 0, 0.2);
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2d3436;
}

.modal-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 50%;
}

.modal-content {
  max-height: 400rpx;
  overflow-y: auto;
}

.plan-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  transition: background 0.3s ease;
}

.plan-option:last-child {
  border-bottom: none;
}

.plan-option:active {
  background: rgba(78, 205, 196, 0.1);
}

.plan-option-info {
  flex: 1;
}

.plan-option-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #2d3436;
  display: block;
  margin-bottom: 8rpx;
}

.plan-option-meta {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.plan-option-destination {
  font-size: 24rpx;
  color: #636e72;
}

.plan-option-arrow {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
