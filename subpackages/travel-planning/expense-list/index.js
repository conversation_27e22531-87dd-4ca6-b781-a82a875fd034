// subpackages/travel-planning/expense-list/index.js
const dataManager = require('../../../utils/data-manager.js').default
const performanceHelper = require('../../../utils/performance-helper.js').default

Page({
  data: {
    planId: '',
    planTitle: '',
    expenseRecords: [],
    loading: false,
    totalAmount: 0,
    recordCount: 0,
    isEmpty: false
  },

  onLoad(options) {
    if (options.planId) {
      this.setData({
        planId: options.planId,
        planTitle: options.planTitle || '旅行费用记录'
      })
      this.loadExpenseRecords()
    } else {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
  },

  /**
   * 加载费用记录
   */
  async loadExpenseRecords() {
    try {
      this.setData({ loading: true })

      const result = await dataManager.getExpenseRecords({
        mode: 'travel',
        planId: this.data.planId,
        limit: 100, // 加载更多记录
        orderBy: 'createTime',
        orderDirection: 'desc'
      })

      if (result.success && result.data) {
        // 格式化数据
        const formattedRecords = result.data.map(record => ({
          id: record._id,
          amount: record.amount,
          category: record.category?.main || '其他',
          description: record.description || record.category?.main || '支出',
          date: this.formatDate(record.createTime),
          fullDate: this.formatFullDate(record.createTime),
          location: record.location?.name || '',
          createTime: record.createTime
        }))

        // 计算总金额
        const totalAmount = formattedRecords.reduce((sum, record) => sum + record.amount, 0)

        this.setData({
          expenseRecords: formattedRecords,
          totalAmount: totalAmount,
          recordCount: formattedRecords.length,
          isEmpty: formattedRecords.length === 0
        })
      } else {
        this.setData({
          expenseRecords: [],
          totalAmount: 0,
          recordCount: 0,
          isEmpty: true
        })
      }
    } catch (error) {
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
      this.setData({
        expenseRecords: [],
        isEmpty: true
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 格式化日期显示 (MM-DD)
   */
  formatDate(dateInput) {
    try {
      let date
      if (dateInput instanceof Date) {
        date = dateInput
      } else if (typeof dateInput === 'string') {
        date = new Date(dateInput)
      } else if (dateInput && dateInput.$date) {
        date = new Date(dateInput.$date)
      } else {
        date = new Date()
      }

      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${month}-${day}`
    } catch (error) {
      return '今天'
    }
  },

  /**
   * 格式化完整日期显示 (YYYY-MM-DD HH:mm)
   */
  formatFullDate(dateInput) {
    try {
      let date
      if (dateInput instanceof Date) {
        date = dateInput
      } else if (typeof dateInput === 'string') {
        date = new Date(dateInput)
      } else if (dateInput && dateInput.$date) {
        date = new Date(dateInput.$date)
      } else {
        date = new Date()
      }

      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      return `${year}-${month}-${day} ${hours}:${minutes}`
    } catch (error) {
      return '未知时间'
    }
  },

  /**
   * 查看费用详情
   */
  viewExpenseDetail(event) {
    const { record } = event.currentTarget.dataset
    // 这里可以跳转到费用详情页面或显示详情弹窗
    wx.showModal({
      title: '费用详情',
      content: `描述：${record.description}\n分类：${record.category}\n金额：¥${record.amount}\n时间：${record.fullDate}${record.location ? '\n地点：' + record.location : ''}`,
      showCancel: false,
      confirmText: '知道了'
    })
  },

  /**
   * 删除费用记录
   */
  async deleteExpenseRecord(event) {
    const { record } = event.currentTarget.dataset
    if (!record || !record.id) {
      wx.showToast({
        title: '记录信息错误',
        icon: 'none'
      })
      return
    }

    // 确认删除
    const result = await new Promise((resolve) => {
      wx.showModal({
        title: '确认删除',
        content: `确定要删除这条费用记录吗？\n${record.description} ¥${record.amount}`,
        confirmText: '删除',
        confirmColor: '#ff4d4f',
        success: (res) => resolve(res.confirm),
        fail: () => resolve(false)
      })
    })

    if (!result) return

    try {
      wx.showLoading({ title: '删除中...' })

      const deleteResult = await dataManager.deleteExpenseRecord(record.id)

      if (deleteResult.success) {
        wx.showToast({
          title: '删除成功',
          icon: 'success'
        })

        // 重新加载费用记录
        this.loadExpenseRecords()
      } else {
        throw new Error(deleteResult.message || '删除失败')
      }
    } catch (error) {
      wx.showToast({
        title: error.message || '删除失败',
        icon: 'none'
      })
    } finally {
      wx.hideLoading()
    }
  },

  /**
   * 添加新费用
   */
  addExpense() {
    wx.navigateTo({
      url: `/subpackages/account/travel-expense/index?mode=travel&planId=${this.data.planId}`
    })
  },

  /**
   * 返回上一页
   */
  navigateBack() {
    wx.navigateBack()
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.loadExpenseRecords().finally(() => {
      wx.stopPullDownRefresh()
    })
  }
})
