<!--subpackages/travel-planning/plan-detail/index.wxml-->
<view class="plan-detail-container">
  <!-- 加载状态 -->
  <van-loading wx:if="{{loading}}" type="spinner" color="#4ECDC4" size="48rpx" vertical>
    加载中...
  </van-loading>

  <!-- 错误状态 -->
  <van-empty wx:elif="{{error}}"
    image="error"
    description="{{error}}"
    custom-class="error-empty">
    <van-button type="primary" size="small" bind:click="loadPlanDetail">
      重新加载
    </van-button>
  </van-empty>

  <!-- 计划详情内容 -->
  <view wx:else class="plan-content">
    <!-- 实时编辑状态提示 -->
    <view wx:if="{{editingStatus.isEditing}}" class="editing-status">
      <view class="status-indicator">
        <view class="pulse-dot"></view>
        <text class="status-text">{{editingStatus.userName}} 正在编辑{{editingStatus.field}}</text>
      </view>
    </view>

    <!-- 计划概览卡片 -->
    <view class="glass-card plan-overview">
      <view class="plan-header">
        <view class="plan-title-row">
          <text class="plan-title">{{planData.title}}</text>

          <!-- 协作者头像列表 -->
          <view wx:if="{{collaboratorsList.length > 1}}" class="collaborators-avatars">
            <view wx:for="{{collaboratorsList}}" wx:key="openid" class="avatar-item" wx:if="{{index < 4}}">
              <image
                wx:if="{{item.tempAvatarUrl}}"
                class="collaborator-avatar {{item.isCreator ? 'creator-avatar' : ''}}"
                src="{{item.tempAvatarUrl}}"
                mode="aspectFill" />
              <view
                wx:else
                class="collaborator-avatar {{item.isCreator ? 'creator-avatar' : ''}}"
                style="background: linear-gradient(45deg, #FF6B6B, #4ECDC4); display: flex; align-items: center; justify-content: center; color: white; font-size: 20rpx; font-weight: bold;">
                {{item.nickname ? item.nickname.charAt(0) : '用'}}
              </view>
              <view wx:if="{{item.isCreator}}" class="creator-badge">
                <view class="creator-ring"></view>
              </view>
            </view>
            <view wx:if="{{collaboratorsList.length > 4}}" class="more-collaborators">
              <text class="more-count">+{{collaboratorsList.length - 4}}</text>
            </view>
          </view>

          <view class="status-container" bindtap="showStatusSelector">
            <van-tag
              type="primary"
              color="{{statusMap[planData.status].color}}"
              custom-class="status-tag clickable">
              {{statusMap[planData.status].text}}
            </van-tag>
            <custom-icon name="arrow-down" size="16" color="#999" class="status-arrow" />
          </view>
        </view>
        <view class="plan-subtitle">
          <custom-icon name="location" size="30" color="#666" />
          <text class="destination">{{planData.destinationDetail.name || planData.destination}}</text>
        </view>

        <!-- 协作信息显示 -->
        <view wx:if="{{collaboratorsList.length > 1}}" class="collaboration-info">
          <view class="collaboration-text">
            <custom-icon name="friends" size="24" color="#4ECDC4" />
            <text class="collab-label">协作者 {{collaboratorsList.length}}/{{planData.collaboration.maxCollaborators || 5}}</text>
          </view>
        </view>
      </view>

      <view class="plan-info">
        <view class="info-item">
          <custom-icon name="calendar" size="38" color="#4ECDC4" />
          <view class="info-content">
            <text class="info-label">出行日期</text>
            <text class="info-value">{{planData.dates.startDate || planData.startDate}} 至 {{planData.dates.endDate || planData.endDate}}</text>
            <text class="info-extra">共 {{planData.dates.duration || planData.duration}} 天</text>
          </view>
        </view>

        <view class="info-item">
          <custom-icon name="user" size="38" color="#4ECDC4" />
          <view class="info-content">
            <text class="info-label">参与人数</text>
            <text class="info-value">{{planData.participants.count || planData.participantCount}} 人</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 预算管理卡片 -->
    <view class="glass-card budget-card">
      <view class="card-header">
        <custom-icon name="wallet" size="38" color="#FF6B6B" />
        <text class="card-title">预算管理</text>
      </view>

      <view class="budget-overview">
        <view class="budget-item">
          <text class="budget-label">总预算</text>
          <text class="budget-value total">¥{{planData.budgetDetail.total || planData.budget}}</text>
        </view>
        <view class="budget-item">
          <text class="budget-label">已花费</text>
          <text class="budget-value spent">¥{{planData.budgetDetail.spent || 0}}</text>
        </view>
        <view class="budget-item">
          <text class="budget-label">剩余</text>
          <text class="budget-value remaining">¥{{remainingBudget}}</text>
        </view>
      </view>

      <view class="budget-progress">
        <van-progress
          percentage="{{budgetProgress}}"
          stroke-width="8rpx"
          color="#FF6B6B"
          track-color="#f5f5f5"
          show-pivot="{{false}}"
          custom-class="progress-bar" />
        <text class="progress-text">已使用 {{budgetProgress}}%</text>
      </view>

      <!-- 预算项目 -->
      <view class="budget-items">
        <view class="section-header">
          <text class="section-title">预算分配</text>
          <view class="section-action" bind:tap="manageBudget">
            <text class="action-text">管理</text>
            <custom-icon name="edit" size="30" color="#1890ff" />
          </view>
        </view>

        <view wx:if="{{(planData.budgetDetail.items && planData.budgetDetail.items.length > 0) || (planData.budget && planData.budget.items && planData.budget.items.length > 0)}}" class="budget-item-list">
          <view wx:for="{{planData.budgetDetail.items || planData.budget.items}}" wx:key="id" class="budget-item-row">
            <text class="item-name">{{item.name}}</text>
            <text class="item-amount">¥{{item.amount}}</text>
          </view>
        </view>

        <view wx:else class="budget-empty">
          <custom-icon name="wallet" size="40" color="#c8c9cc" />
          <text class="empty-text">暂无预算分配</text>
          <text class="empty-desc">点击管理按钮添加预算项目</text>
        </view>
      </view>
    </view>

    <!-- 快捷操作 -->
    <view class="glass-card quick-actions">
      <view class="card-header">
        <custom-icon name="settings" size="32" color="#45B7D1" />
        <text class="card-title">快捷操作</text>
      </view>

      <view class="action-grid">
        <view class="action-item" bind:tap="recordExpense">
          <view class="action-icon">
            <custom-icon name="plus" size="32" color="#52c41a" />
          </view>
          <text class="action-text">记录费用</text>
        </view>

        <view class="action-item" bind:tap="editPlan">
          <view class="action-icon">
            <custom-icon name="edit" size="32" color="#1890ff" />
          </view>
          <text class="action-text">编辑计划</text>
        </view>

        <view class="action-item" bind:tap="viewItinerary">
          <view class="action-icon">
            <custom-icon name="map" size="32" color="#722ed1" />
          </view>
          <text class="action-text">查看行程</text>
        </view>

        <view class="action-item" bind:tap="showActionMenu">
          <view class="action-icon">
            <custom-icon name="heart" size="32" color="#ff4d4f" />
          </view>
          <text class="action-text">更多</text>
        </view>
      </view>
    </view>

    <!-- 费用记录 -->
    <view class="glass-card expense-records">
      <view class="card-header">
        <custom-icon name="chart" size="32" color="#faad14" />
        <text class="card-title">最近费用</text>
        <text class="view-all" bind:tap="viewAllExpenses">查看全部</text>
      </view>

      <view wx:if="{{expenseRecords.length > 0}}" class="expense-list">
        <view wx:for="{{expenseRecords}}" wx:key="id" class="expense-item-wrapper">
          <view class="expense-item"
            bind:tap="viewExpenseDetail"
            data-record="{{item}}">
            <view class="expense-info">
              <text class="expense-desc">{{item.description}}</text>
              <text class="expense-category">{{item.category}}</text>
              <text class="expense-date">{{item.date}}</text>
            </view>
            <view class="expense-amount">
              <text class="amount">¥{{item.amount}}</text>
              <custom-icon name="arrow-right" size="24" color="rgba(255,255,255,0.6)" />
            </view>
          </view>
          <view class="expense-actions">
            <view class="delete-btn"
              catch:tap="deleteExpenseRecord"
              data-record="{{item}}">
              <custom-icon name="delete" size="24" color="#ffffff" />
            </view>
          </view>
        </view>
      </view>

      <van-empty wx:else
        image="search"
        description="暂无费用记录"
        custom-class="expense-empty">
        <van-button type="primary" size="small" bind:click="recordExpense">
          记录第一笔费用
        </van-button>
      </van-empty>
    </view>
  </view>

  <!-- 操作菜单 -->
  <van-action-sheet
    show="{{showActionSheet}}"
    actions="{{actionSheetActions}}"
    bind:close="closeActionSheet"
    bind:select="onActionSelect"
    cancel-text="取消"
    custom-class="action-sheet" />

  <!-- 费用详情弹窗 -->
  <view class="expense-detail-modal {{showExpenseDetail ? 'show' : ''}}" bind:tap="closeExpenseDetail">
    <view class="expense-detail-content" catch:tap="stopPropagation">
      <view class="detail-header">
        <text class="detail-title">费用详情</text>
        <view class="detail-close" bind:tap="closeExpenseDetail">
          <custom-icon name="close" size="28" color="#666" />
        </view>
      </view>

      <view class="detail-body" wx:if="{{selectedExpense}}">
        <view class="expense-amount-display">
          <text class="amount-label">支出金额</text>
          <text class="amount-value">¥{{selectedExpense.amount}}</text>
        </view>

        <view class="expense-info-list">
          <view class="info-row">
            <text class="info-label">分类</text>
            <text class="info-value">{{selectedExpense.category}}</text>
          </view>
          <view class="info-row">
            <text class="info-label">日期</text>
            <text class="info-value">{{selectedExpense.date}}</text>
          </view>
          <view class="info-row" wx:if="{{selectedExpense.location}}">
            <text class="info-label">地点</text>
            <text class="info-value">{{selectedExpense.location}}</text>
          </view>
          <view class="info-row" wx:if="{{selectedExpense.description}}">
            <text class="info-label">备注</text>
            <text class="info-value">{{selectedExpense.description}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 预算管理弹窗 -->
  <view class="budget-modal {{showBudgetModal ? 'show' : ''}}" bind:tap="closeBudgetModal">
    <view class="budget-modal-content" catch:tap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">预算管理</text>
        <view class="modal-close" bind:tap="closeBudgetModal">
          <custom-icon name="close" size="28" color="#666" />
        </view>
      </view>

      <view class="modal-body">
        <!-- 总预算设置 -->
        <view class="total-budget-section">
          <text class="budget-label">总预算</text>
          <view class="budget-input-row">
            <text class="currency">¥</text>
            <input
              class="budget-input"
              type="digit"
              placeholder="请输入总预算"
              value="{{editingBudget.total}}"
              bind:input="onTotalBudgetInput" />
          </view>
        </view>

        <!-- 预算项目列表 -->
        <view class="budget-items-section">
          <view class="items-header">
            <text class="items-title">预算项目</text>
            <view class="add-item-btn" bind:tap="addBudgetItem">
              <custom-icon name="plus" size="24" color="#52c41a" />
              <text>添加项目</text>
            </view>
          </view>

          <view class="budget-items-list">
            <view wx:for="{{editingBudget.items}}" wx:key="index" class="budget-item-edit">
              <input
                class="item-name-input"
                placeholder="项目名称"
                value="{{item.name}}"
                bind:input="onItemNameInput"
                data-index="{{index}}" />
              <view class="item-amount-input">
                <text class="currency">¥</text>
                <input
                  type="digit"
                  placeholder="金额"
                  value="{{item.amount}}"
                  bind:input="onItemAmountInput"
                  data-index="{{index}}" />
              </view>
              <view class="item-delete" bind:tap="deleteBudgetItem" data-index="{{index}}">
                <custom-icon name="close" size="24" color="#ff4d4f" />
              </view>
            </view>
          </view>

          <view wx:if="{{editingBudget.items.length === 0}}" class="no-items">
            <text>暂无预算项目，点击上方按钮添加</text>
          </view>
        </view>
      </view>

      <view class="modal-footer">
        <view class="modal-btn cancel" bind:tap="closeBudgetModal">取消</view>
        <view class="modal-btn confirm" bind:tap="saveBudget">保存</view>
      </view>
    </view>
  </view>

  <!-- 状态选择器 -->
  <view wx:if="{{showStatusSelector}}" class="status-selector-overlay" bindtap="closeStatusSelector">
    <view class="status-selector-modal" catchtap="">
      <view class="modal-header">
        <text class="modal-title">选择计划状态</text>
        <view class="modal-close" bindtap="closeStatusSelector">
          <custom-icon name="close" size="20" color="#636e72" />
        </view>
      </view>

      <view class="modal-content">
        <view
          wx:for="{{statusOptions}}"
          wx:key="value"
          class="status-option {{planData.status === item.value ? 'active' : ''}}"
          bindtap="selectStatus"
          data-status="{{item.value}}"
        >
          <view class="status-option-info">
            <view class="status-option-icon" style="background-color: {{item.color}};">
              <custom-icon name="{{item.icon}}" size="20" color="#fff" />
            </view>
            <view class="status-option-text">
              <text class="status-option-title">{{item.text}}</text>
              <text class="status-option-desc">{{item.description}}</text>
            </view>
          </view>
          <view wx:if="{{planData.status === item.value}}" class="status-option-check">
            <custom-icon name="check" size="16" color="#52c41a" />
          </view>
        </view>
      </view>
    </view>
  </view>



  <!-- 邀请协作弹窗 -->
  <van-popup
    show="{{showInviteModal}}"
    position="bottom"
    round
    custom-class="invite-popup">
    <view class="invite-modal">
      <view class="modal-header">
        <text class="modal-title">邀请好友协作</text>
        <custom-icon name="close" size="32" color="#999" bind:tap="closeInviteModal" />
      </view>

      <view class="invite-content">
        <view class="invite-code-section">
          <view class="code-display">
            <view class="code-left">
              <text class="code-label">邀请码</text>
              <text class="invite-code">{{inviteCode}}</text>
            </view>
            <view class="copy-btn" bind:tap="copyInviteCode">
              <custom-icon name="copy" size="28" color="white" />
              <text class="copy-text">复制</text>
            </view>
          </view>
          <text class="code-hint">邀请码30天内有效</text>
        </view>

        <view class="invite-actions">
          <view class="action-description">
            <custom-icon name="info" size="24" color="#666" />
            <text class="description-text">将邀请码或链接发送给好友，好友即可加入协作</text>
          </view>

          <van-button
            type="primary"
            size="large"
            bind:click="copyInviteLink"
            custom-class="link-btn">
            <custom-icon name="link" size="36" color="white" />
            复制邀请链接
          </van-button>
        </view>

        <!-- 当前协作者 -->
        <view wx:if="{{collaboratorsList.length > 0}}" class="current-collaborators">
          <view class="section-title">
            <custom-icon name="user" size="24" color="#999" />
            <text class="title-text">当前协作者</text>
          </view>

          <view class="collaborators-list">
            <view wx:for="{{collaboratorsList}}" wx:key="openid" class="collaborator-item">
              <image
                wx:if="{{item.tempAvatarUrl}}"
                class="collaborator-avatar"
                src="{{item.tempAvatarUrl}}"
                mode="aspectFill" />
              <view
                wx:else
                class="collaborator-avatar"
                style="background: linear-gradient(45deg, #FF6B6B, #4ECDC4); display: flex; align-items: center; justify-content: center; color: white; font-size: 28rpx; font-weight: bold;">
                {{item.nickname ? item.nickname.charAt(0) : '用'}}
              </view>
              <view class="collaborator-info">
                <text class="collaborator-name">{{item.nickname}}</text>
                <text class="collaborator-role">{{item.isCreator ? '创建者' : '协作者'}}</text>
              </view>
              <view wx:if="{{item.isCreator}}" class="creator-crown">
                <custom-icon name="crown" size="24" color="#faad14" />
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </van-popup>

  <!-- Toast 组件 -->
  <van-toast id="van-toast" />

  <!-- Dialog 组件 -->
  <van-dialog id="van-dialog" />
</view>