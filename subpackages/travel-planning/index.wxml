<!--subpackages/travel-planning/index.wxml-->
<view class="travel-container">
  <!-- 背景装饰 -->
  <view class="bg-decoration">
    <view class="gradient-orb orb-1"></view>
    <view class="gradient-orb orb-2"></view>
    <view class="gradient-orb orb-3"></view>
  </view>

  <!-- 页面内容 -->
  <scroll-view class="content-scroll" scroll-y="true" enhanced="true" show-scrollbar="false">
    
    <!-- 欢迎区域 -->
    <view class="welcome-section">
      <view class="welcome-card glass-readable-normal">
        <view class="welcome-content">
          <view class="welcome-icon">
            <custom-icon name="airplane" size="48" color="#4ECDC4" />
          </view>
          <view class="welcome-text">
            <text class="welcome-title glass-text-primary">开启精彩旅程</text>
            <text class="welcome-subtitle glass-text-secondary">规划你的完美旅行</text>
          </view>
        </view>
        <view class="welcome-stats" bindlongpress="onLongPressStats" bindtap="manualRefresh">
          <view class="stat-item">
            <text class="stat-number">{{analytics.overview.totalPlans}}</text>
            <text class="stat-label">总旅行</text>
          </view>
          <view class="stat-divider"></view>
          <view class="stat-item">
            <text class="stat-number">¥{{analytics.overview.totalExpenses}}</text>
            <text class="stat-label">总花费</text>
          </view>
          <view class="stat-divider"></view>
          <view class="stat-item">
            <text class="stat-number">{{analytics.overview.budgetUsage}}%</text>
            <text class="stat-label">预算使用</text>
          </view>
          <!-- 刷新指示器 -->
          <view class="refresh-indicator" wx:if="{{loading}}">
            <custom-icon name="loading" size="16" color="#4ECDC4" />
          </view>
        </view>
      </view>
    </view>

    <!-- 当前计划 -->
    <view wx:if="{{currentPlan}}" class="current-plan-section">
      <view class="section-header">
        <view class="section-title">
          <custom-icon name="map" size="20" color="#FF6B6B" />
          <text>当前旅行</text>
        </view>
        <view class="section-action" bindtap="viewCurrentPlan">
          <text>查看详情</text>
          <custom-icon name="arrow-right" size="16" color="#636e72" />
        </view>
      </view>
      
      <view class="current-plan-card" bindtap="viewCurrentPlan">
        <view class="plan-header">
          <view class="plan-info">
            <text class="plan-title">{{currentPlan.title}}</text>
            <view class="plan-meta">
              <custom-icon name="map-pin" size="14" color="#4ECDC4" />
              <text class="plan-destination">{{currentPlan.destination}}</text>
            </view>
          </view>
          <view class="plan-status status-{{currentPlan.status}}">
            <text>{{currentPlan.statusText}}</text>
          </view>
        </view>
        
        <view class="plan-progress">
          <view class="progress-item">
            <view class="progress-info">
              <text class="progress-label">行程进度</text>
              <text class="progress-value">{{currentPlan.dayProgress}}/{{currentPlan.totalDays}}天</text>
            </view>
            <view class="progress-bar">
              <view class="progress-fill" style="width: {{currentPlan.progressPercent}}%"></view>
            </view>
          </view>
          
          <view class="progress-item">
            <view class="progress-info">
              <text class="progress-label">预算使用</text>
              <text class="progress-value">¥{{currentPlan.usedBudget}}/¥{{currentPlan.totalBudget}}</text>
            </view>
            <view class="progress-bar">
              <view class="progress-fill budget-fill" style="width: {{currentPlan.budgetPercent}}%"></view>
            </view>
          </view>
        </view>

        <view class="plan-actions">
          <button class="action-btn primary" bindtap="recordExpense" data-plan="{{currentPlan}}">
            <custom-icon name="wallet" size="16" color="#fff" />
            <text>记录花费</text>
          </button>
          <button class="action-btn secondary" bindtap="viewItinerary" data-plan="{{currentPlan}}">
            <custom-icon name="calendar" size="16" color="#4ECDC4" />
            <text>查看行程</text>
          </button>
        </view>
      </view>
    </view>


    <!-- 快速操作 -->
    <view class="quick-actions-section">
      <view class="section-header">
        <view class="section-title">
          <custom-icon name="plus" size="20" color="#52c41a" />
          <text>快速操作</text>
        </view>
      </view>
      
      <!-- {{ AURA-X: Modify - 调整按钮位置和大小，第二行按钮放大. Approval: 寸止(ID:1738056000). }} -->
      <view class="quick-actions-grid">
        <!-- 第一行：3个核心功能 -->
        <view class="quick-action-item glass-readable-subtle glass-btn-interactive" bindtap="exploreDestinations">
          <view class="action-icon explore">
            <custom-icon name="search" size="24" color="#fff" />
          </view>
          <text class="action-title glass-text-primary">探索目的地</text>
          <text class="action-desc glass-text-secondary">发现热门景点</text>
        </view>

        <view class="quick-action-item glass-readable-subtle glass-btn-interactive" bindtap="joinPlan">
          <view class="action-icon join">
            <custom-icon name="friends" size="24" color="#fff" />
          </view>
          <text class="action-title glass-text-primary">加入计划</text>
          <text class="action-desc glass-text-secondary">通过邀请码加入协作</text>
        </view>

        <view class="quick-action-item glass-readable-subtle glass-btn-interactive" bindtap="inviteCollaboration">
          <view class="action-icon invite">
            <custom-icon name="heart" size="24" color="#fff" />
          </view>
          <text class="action-title glass-text-primary">邀请协作</text>
          <text class="action-desc glass-text-secondary">邀请好友一起规划</text>
        </view>

        <!-- 第二行：2个重要功能，放大显示 -->
        <view class="quick-action-item large-item glass-readable-normal glass-btn-interactive" bindtap="createNewPlan">
          <view class="action-icon create">
            <custom-icon name="plus" size="24" color="#fff" />
          </view>
          <text class="action-title glass-text-primary">创建计划</text>
          <text class="action-desc glass-text-secondary">开始新的旅行规划</text>
        </view>

        <view class="quick-action-item large-item glass-readable-normal glass-btn-interactive" bindtap="quickRecord">
          <view class="action-icon record">
            <custom-icon name="edit" size="24" color="#fff" />
          </view>
          <text class="action-title glass-text-primary">快速记录</text>
          <text class="action-desc glass-text-secondary">记录旅行花费</text>
        </view>
      </view>
    </view>

    <!-- 我的计划 -->
    <view class="my-plans-section">
      <view class="section-header">
        <view class="section-title">
          <custom-icon name="bookmark" size="20" color="#722ed1" />
          <text>我的计划</text>
        </view>
        <view class="section-action" bindtap="viewAllPlans">
          <text>查看全部</text>
          <custom-icon name="arrow-right" size="16" color="#636e72" />
        </view>
      </view>
      
      <view wx:if="{{plans.length === 0}}" class="empty-state">
        <view class="empty-icon">
          <custom-icon name="suitcase" size="64" color="#b2bec3" />
        </view>
        <text class="empty-title">还没有旅行计划</text>
        <text class="empty-desc">创建你的第一个旅行计划吧</text>
        <button class="empty-action" bindtap="createNewPlan">
          <custom-icon name="plus" size="16" color="#fff" />
          <text>创建计划</text>
        </button>
      </view>
      
      <view wx:else class="plans-list">
        <view 
          wx:for="{{plans}}" 
          wx:key="id" 
          class="plan-card" 
          bindtap="viewPlanDetail" 
          data-plan="{{item}}"
        >
          <view class="plan-card-header">
            <view class="plan-card-info">
              <text class="plan-card-title">{{item.title}}</text>
              <view class="plan-card-meta">
                <custom-icon name="map-pin" size="12" color="#4ECDC4" />
                <text class="plan-card-destination">{{item.destination}}</text>
                <text class="plan-card-date">{{item.dateRange}}</text>
              </view>
            </view>
            <!-- {{ AURA-X: Add - 添加用户角色显示. Approval: 寸止(ID:1738056000). }} -->
            <view class="plan-card-badges">
              <view class="plan-card-status status-{{item.status}}">
                <text>{{item.statusText}}</text>
              </view>
              <view wx:if="{{item.userRole === 'collaborator'}}" class="plan-card-role role-collaborator">
                <text>协作者</text>
              </view>
            </view>
          </view>
          
          <view class="plan-card-content">
            <view class="plan-card-stats">
              <view class="card-stat-item">
                <custom-icon name="calendar" size="14" color="#636e72" />
                <text>{{item.duration}}天</text>
              </view>
              <view class="card-stat-item">
                <custom-icon name="wallet" size="14" color="#636e72" />
                <text>¥{{item.budget}}</text>
              </view>
              <view class="card-stat-item">
                <custom-icon name="user" size="14" color="#636e72" />
                <text>{{item.participants}}人</text>
              </view>
            </view>
            
            <view wx:if="{{item.status === 'ongoing'}}" class="plan-card-progress">
              <view class="card-progress-bar">
                <view class="card-progress-fill" style="width: {{item.progressPercent}}%"></view>
              </view>
              <text class="card-progress-text">进度 {{item.progressPercent}}%</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部安全距离 -->
    <view class="safe-bottom"></view>
  </scroll-view>

  <!-- 计划选择弹窗 -->
  <view wx:if="{{showPlanSelector}}" class="plan-selector-overlay" bindtap="closePlanSelector">
    <view class="plan-selector-modal" catchtap="">
      <view class="modal-header">
        <text class="modal-title">选择旅行计划</text>
        <view class="modal-close" bindtap="closePlanSelector">
          <custom-icon name="close" size="20" color="#636e72" />
        </view>
      </view>

      <view class="modal-content">
        <view
          wx:for="{{availablePlans}}"
          wx:key="id"
          class="plan-option"
          bindtap="selectPlanForRecord"
          data-plan-id="{{item.id}}"
        >
          <view class="plan-option-info">
            <text class="plan-option-title">{{item.title}}</text>
            <view class="plan-option-meta">
              <custom-icon name="map-pin" size="14" color="#4ECDC4" />
              <text class="plan-option-destination">{{item.destination}}</text>
            </view>
          </view>
          <view class="plan-option-arrow">
            <custom-icon name="arrow-right" size="16" color="#b2bec3" />
          </view>
        </view>
      </view>
    </view>
  </view>

</view>
