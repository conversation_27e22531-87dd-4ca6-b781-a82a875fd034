// subpackages/settings/badges/index.js
import badgeManager from '../../../utils/badge-manager.js'
import Toast from '@vant/weapp/toast/toast'

Page({
  data: {
    statistics: {
      totalBadges: 0,
      unlockedBadges: 0,
      completionRate: 0,
      totalPoints: 0
    },
    categories: [
      { id: 'all', name: '全部', icon: 'badge' },
      { id: 'accounting', name: '记账', icon: 'wallet' },
      { id: 'travel', name: '旅行', icon: 'airplane' },
      { id: 'usage', name: '使用', icon: 'clock' },
      { id: 'special', name: '特殊', icon: 'star' }
    ],
    filters: [
      { id: 'all', name: '全部', count: 0 },
      { id: 'unlocked', name: '已获得', count: 0 },
      { id: 'locked', name: '未获得', count: 0 }
    ],
    currentCategory: 'all',
    currentFilter: 'all',
    allBadges: [],
    displayBadges: [],
    showBadgeDetail: false,
    selectedBadge: {},
    loading: true
  },

  async onLoad() {
    await this.initBadgesData()
  },

  /**
   * 初始化徽章数据
   */
  async initBadgesData() {
    try {
      wx.showLoading({ title: '加载中...' })

      // 初始化徽章管理器
      await badgeManager.initialize()

      // 获取用户徽章数据
      const userBadges = badgeManager.getUserBadges()
      const badgeDefinitions = badgeManager.getBadgeDefinitions()
      const categoryConfig = badgeManager.getCategoryConfig()
      const rarityConfig = badgeManager.getRarityConfig()

      // 更新统计信息
      this.setData({
        statistics: userBadges.statistics
      })

      // 准备徽章数据
      const allBadges = Object.keys(badgeDefinitions).map(badgeId => {
        const definition = badgeDefinitions[badgeId]
        const userBadge = userBadges.badges[badgeId] || { progress: 0, unlocked: false }
        
        return {
          id: badgeId,
          name: definition.name,
          description: definition.description,
          icon: definition.icon,
          category: definition.category,
          categoryName: categoryConfig[definition.category]?.name || '未知',
          rarity: definition.rarity,
          rarityName: rarityConfig[definition.rarity]?.name || '普通',
          points: definition.points,
          unlocked: userBadge.unlocked,
          progress: userBadge.progress || 0,
          target: definition.condition.target,
          unlockedAt: userBadge.unlockedAt,
          unlockedTimeText: userBadge.unlockedAt ? this.formatTime(userBadge.unlockedAt) : '',
          isNew: this.isNewBadge(userBadge.unlockedAt),
          progressHint: this.getProgressHint(definition.condition)
        }
      })

      // 排序：已解锁的在前，按稀有度和解锁时间排序
      allBadges.sort((a, b) => {
        if (a.unlocked && !b.unlocked) return -1
        if (!a.unlocked && b.unlocked) return 1
        
        if (a.unlocked && b.unlocked) {
          return (b.unlockedAt || 0) - (a.unlockedAt || 0)
        }
        
        // 未解锁的按进度排序
        const aProgress = a.progress / a.target
        const bProgress = b.progress / b.target
        return bProgress - aProgress
      })

      this.setData({
        allBadges,
        loading: false
      })

      // 更新筛选计数
      this.updateFilterCounts()
      
      // 应用当前筛选
      this.applyFilters()

    } catch (error) {
      console.error('初始化徽章数据失败:', error)
      Toast.fail('加载失败')
    } finally {
      wx.hideLoading()
    }
  },

  /**
   * 判断是否为新徽章
   */
  isNewBadge(unlockedAt) {
    if (!unlockedAt) return false
    const threeDaysAgo = Date.now() - (3 * 24 * 60 * 60 * 1000)
    return unlockedAt > threeDaysAgo
  },

  /**
   * 格式化时间
   */
  formatTime(timestamp) {
    const date = new Date(timestamp)
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString()
  },

  /**
   * 获取进度提示
   */
  getProgressHint(condition) {
    switch (condition.type) {
      case 'record_count':
        return '完成更多记账来解锁'
      case 'consecutive_days':
        return '保持连续记账来解锁'
      case 'travel_plan_count':
        return '创建更多旅行计划来解锁'
      case 'voice_record_count':
        return '使用语音记账来解锁'
      default:
        return '继续使用应用来解锁'
    }
  },

  /**
   * 更新筛选计数
   */
  updateFilterCounts() {
    const allCount = this.data.allBadges.length
    const unlockedCount = this.data.allBadges.filter(badge => badge.unlocked).length
    const lockedCount = allCount - unlockedCount

    const filters = this.data.filters.map(filter => ({
      ...filter,
      count: filter.id === 'all' ? allCount : 
             filter.id === 'unlocked' ? unlockedCount : lockedCount
    }))

    this.setData({ filters })
  },

  /**
   * 分类切换
   */
  onCategoryChange(e) {
    const category = e.currentTarget.dataset.category
    this.setData({ currentCategory: category })
    this.applyFilters()
  },

  /**
   * 筛选切换
   */
  onFilterChange(e) {
    const filter = e.currentTarget.dataset.filter
    this.setData({ currentFilter: filter })
    this.applyFilters()
  },

  /**
   * 应用筛选
   */
  applyFilters() {
    let filteredBadges = [...this.data.allBadges]

    // 按分类筛选
    if (this.data.currentCategory !== 'all') {
      filteredBadges = filteredBadges.filter(badge => 
        badge.category === this.data.currentCategory
      )
    }

    // 按状态筛选
    if (this.data.currentFilter === 'unlocked') {
      filteredBadges = filteredBadges.filter(badge => badge.unlocked)
    } else if (this.data.currentFilter === 'locked') {
      filteredBadges = filteredBadges.filter(badge => !badge.unlocked)
    }

    this.setData({ displayBadges: filteredBadges })
  },

  /**
   * 点击徽章
   */
  onBadgeClick(e) {
    const badge = e.currentTarget.dataset.badge
    
    // 触觉反馈
    if (wx.canIUse('vibrateShort')) {
      wx.vibrateShort({ type: 'light' })
    }

    this.setData({
      selectedBadge: badge,
      showBadgeDetail: true
    })
  },

  /**
   * 关闭详情弹窗
   */
  onCloseDetail() {
    this.setData({
      showBadgeDetail: false,
      selectedBadge: {}
    })
  },

  /**
   * 分享徽章
   */
  onShareBadge() {
    const badge = this.data.selectedBadge
    
    // 关闭弹窗
    this.onCloseDetail()
    
    // 显示分享提示
    Toast.success('徽章分享功能开发中')
  },

  /**
   * 下拉刷新
   */
  async onPullDownRefresh() {
    await this.initBadgesData()
    wx.stopPullDownRefresh()
  },

  /**
   * 分享页面
   */
  onShareAppMessage() {
    const { unlockedBadges, totalBadges } = this.data.statistics
    return {
      title: `我已获得 ${unlockedBadges}/${totalBadges} 个成就徽章！`,
      path: '/subpackages/settings/badges/index'
    }
  }
})
