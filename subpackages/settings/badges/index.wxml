<!--subpackages/settings/badges/index.wxml-->
<view class="badges-container">
  <!-- 顶部统计 -->
  <view class="stats-header glass-card">
    <view class="stats-grid">
      <view class="stat-item">
        <text class="stat-number">{{statistics.unlockedBadges}}</text>
        <text class="stat-label">已获得</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{statistics.totalBadges}}</text>
        <text class="stat-label">总徽章</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{Math.round(statistics.completionRate * 100)}}%</text>
        <text class="stat-label">完成度</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{statistics.totalPoints}}</text>
        <text class="stat-label">总积分</text>
      </view>
    </view>
    
    <!-- 整体进度条 -->
    <view class="overall-progress">
      <view class="progress-bar">
        <view 
          class="progress-fill" 
          style="width: {{statistics.completionRate * 100}}%"
        ></view>
      </view>
      <text class="progress-text">{{statistics.unlockedBadges}}/{{statistics.totalBadges}} 个徽章</text>
    </view>
  </view>

  <!-- 分类标签 -->
  <view class="category-tabs">
    <scroll-view class="tabs-scroll" scroll-x="true">
      <view class="tabs-container">
        <view 
          wx:for="{{categories}}" 
          wx:key="id"
          class="tab-item {{currentCategory === item.id ? 'active' : ''}}"
          bindtap="onCategoryChange"
          data-category="{{item.id}}"
        >
          <image class="tab-icon" src="/images/{{item.icon || 'badge'}}.svg" mode="aspectFit"></image>
          <text class="tab-text">{{item.name}}</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 筛选状态 -->
  <view class="filter-tabs">
    <view 
      wx:for="{{filters}}" 
      wx:key="id"
      class="filter-item {{currentFilter === item.id ? 'active' : ''}}"
      bindtap="onFilterChange"
      data-filter="{{item.id}}"
    >
      <text class="filter-text">{{item.name}}</text>
      <text class="filter-count">({{item.count}})</text>
    </view>
  </view>

  <!-- 徽章网格 -->
  <view class="badges-grid">
    <view 
      wx:for="{{displayBadges}}" 
      wx:key="id"
      class="badge-card {{item.unlocked ? 'unlocked' : 'locked'}} {{item.rarity}}"
      bindtap="onBadgeClick"
      data-badge="{{item}}"
    >
      <!-- 稀有度标识 -->
      <view class="rarity-indicator {{item.rarity}}"></view>
      
      <!-- 徽章图标 -->
      <view class="badge-icon-container">
        <image
          class="badge-icon"
          src="/images/{{item.icon || 'badge'}}.svg"
          mode="aspectFit"
        ></image>
        <view wx:if="{{!item.unlocked}}" class="lock-overlay">
          <image class="lock-icon" src="/images/lock.svg" mode="aspectFit"></image>
        </view>
        <view wx:if="{{item.unlocked && item.isNew}}" class="new-indicator">NEW</view>
      </view>

      <!-- 徽章信息 -->
      <view class="badge-info">
        <text class="badge-name">{{item.name}}</text>
        <text class="badge-description">{{item.description}}</text>
        
        <!-- 进度信息 -->
        <view wx:if="{{!item.unlocked}}" class="progress-info">
          <view class="progress-bar small">
            <view 
              class="progress-fill" 
              style="width: {{(item.progress / item.target) * 100}}%"
            ></view>
          </view>
          <text class="progress-text">{{item.progress}}/{{item.target}}</text>
        </view>
        
        <!-- 解锁信息 -->
        <view wx:if="{{item.unlocked}}" class="unlock-info">
          <text class="unlock-time">{{item.unlockedTimeText}}</text>
          <text class="points">+{{item.points}} 积分</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view wx:if="{{displayBadges.length === 0}}" class="empty-state">
    <image class="empty-icon" src="/images/badge.svg" mode="aspectFit"></image>
    <text class="empty-text">暂无相关徽章</text>
    <text class="empty-hint">完成更多操作来解锁徽章吧</text>
  </view>

  <!-- 徽章详情弹窗 -->
  <van-popup 
    show="{{showBadgeDetail}}" 
    position="bottom" 
    round
    bind:close="onCloseDetail"
  >
    <view class="badge-detail-popup">
      <view class="detail-header">
        <view class="detail-badge-icon">
          <image
            class="detail-icon"
            src="/images/{{selectedBadge.icon || 'badge'}}.svg"
            mode="aspectFit"
          ></image>
        </view>
        <view class="detail-info">
          <text class="detail-name">{{selectedBadge.name}}</text>
          <text class="detail-category">{{selectedBadge.categoryName}}</text>
          <text class="detail-rarity">{{selectedBadge.rarityName}}</text>
        </view>
      </view>

      <view class="detail-content">
        <text class="detail-description">{{selectedBadge.description}}</text>
        
        <view wx:if="{{selectedBadge.unlocked}}" class="detail-unlock-info">
          <view class="detail-item">
            <text class="detail-label">解锁时间</text>
            <text class="detail-value">{{selectedBadge.unlockedTimeText}}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">获得积分</text>
            <text class="detail-value">+{{selectedBadge.points}}</text>
          </view>
        </view>
        
        <view wx:else class="detail-progress-info">
          <view class="detail-item">
            <text class="detail-label">完成进度</text>
            <text class="detail-value">{{selectedBadge.progress}}/{{selectedBadge.target}}</text>
          </view>
          <view class="detail-progress-bar">
            <view 
              class="progress-fill" 
              style="width: {{(selectedBadge.progress / selectedBadge.target) * 100}}%"
            ></view>
          </view>
          <text class="detail-hint">{{selectedBadge.progressHint}}</text>
        </view>
      </view>

      <view class="detail-actions">
        <van-button 
          wx:if="{{selectedBadge.unlocked}}" 
          type="primary" 
          size="large"
          bindtap="onShareBadge"
        >
          分享徽章
        </van-button>
        <van-button 
          wx:else 
          type="default" 
          size="large"
          bindtap="onCloseDetail"
        >
          继续努力
        </van-button>
      </view>
    </view>
  </van-popup>

  <!-- Vant 组件 -->
  <van-toast id="van-toast" />
</view>
