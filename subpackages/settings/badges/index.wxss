/* subpackages/settings/badges/index.wxss */

.badges-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #FF6B6B 0%, #4ECDC4 50%, #45B7D1 100%);
  padding-bottom: 40rpx;
}

/* 顶部统计 */
.stats-header {
  margin: 24rpx;
  padding: 32rpx;
  border-radius: 32rpx;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20rpx) saturate(180%);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 16rpx 64rpx rgba(0, 0, 0, 0.1);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24rpx;
  margin-bottom: 24rpx;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stat-number {
  font-size: 40rpx;
  font-weight: 600;
  color: #2d3436;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #636e72;
}

.overall-progress {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.progress-bar {
  flex: 1;
  height: 12rpx;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 6rpx;
  overflow: hidden;
}

.progress-bar.small {
  height: 8rpx;
  border-radius: 4rpx;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #FF6B6B, #4ECDC4);
  border-radius: inherit;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 24rpx;
  color: #636e72;
  white-space: nowrap;
}

/* 分类标签 */
.category-tabs {
  margin: 0 24rpx 24rpx;
}

.tabs-scroll {
  white-space: nowrap;
}

.tabs-container {
  display: inline-flex;
  gap: 16rpx;
  padding: 0 8rpx;
}

.tab-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx 24rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 24rpx;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.tab-item.active {
  background: rgba(255, 255, 255, 0.25);
  transform: scale(1.05);
}

.tab-icon {
  width: 32rpx;
  height: 32rpx;
  margin-bottom: 8rpx;
}

.tab-text {
  font-size: 24rpx;
  color: #2d3436;
}

/* 筛选标签 */
.filter-tabs {
  display: flex;
  margin: 0 24rpx 24rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 24rpx;
  padding: 8rpx;
}

.filter-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}

.filter-item.active {
  background: rgba(255, 255, 255, 0.25);
}

.filter-text {
  font-size: 28rpx;
  color: #2d3436;
  margin-right: 8rpx;
}

.filter-count {
  font-size: 24rpx;
  color: #636e72;
}

/* 徽章网格 */
.badges-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
  margin: 0 24rpx;
}

.badge-card {
  position: relative;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  padding: 24rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.badge-card.unlocked {
  background: rgba(255, 182, 193, 0.2);
  border-color: rgba(255, 182, 193, 0.3);
}

.badge-card.locked {
  opacity: 0.7;
}

.badge-card:active {
  transform: scale(0.98);
}

/* 稀有度标识 */
.rarity-indicator {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
}

.rarity-indicator.common {
  background: #FFB6C1;
}

.rarity-indicator.rare {
  background: #4ECDC4;
}

.rarity-indicator.epic {
  background: #6c5ce7;
}

.rarity-indicator.legendary {
  background: #fdcb6e;
}

/* 徽章图标 */
.badge-icon-container {
  position: relative;
  display: flex;
  justify-content: center;
  margin-bottom: 16rpx;
}

.badge-icon {
  width: 120rpx;
  height: 120rpx;
}

.lock-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.lock-icon {
  width: 48rpx;
  height: 48rpx;
}

.new-indicator {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: #ff4d4f;
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  font-weight: 600;
}

/* 徽章信息 */
.badge-info {
  text-align: center;
}

.badge-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #2d3436;
  margin-bottom: 8rpx;
  display: block;
}

.badge-description {
  font-size: 24rpx;
  color: #636e72;
  line-height: 1.4;
  margin-bottom: 16rpx;
  display: block;
}

/* 进度信息 */
.progress-info {
  margin-top: 16rpx;
}

.progress-info .progress-text {
  font-size: 20rpx;
  margin-top: 8rpx;
}

/* 解锁信息 */
.unlock-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16rpx;
}

.unlock-time {
  font-size: 20rpx;
  color: #636e72;
}

.points {
  font-size: 20rpx;
  color: #52c41a;
  font-weight: 600;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 40rpx;
  text-align: center;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  opacity: 0.3;
  margin-bottom: 24rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #636e72;
  margin-bottom: 16rpx;
}

.empty-hint {
  font-size: 28rpx;
  color: #b2bec3;
}

/* 徽章详情弹窗 */
.badge-detail-popup {
  padding: 48rpx 32rpx 32rpx;
  background: #fff;
  border-radius: 32rpx 32rpx 0 0;
}

.detail-header {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
}

.detail-badge-icon {
  margin-right: 24rpx;
}

.detail-icon {
  width: 120rpx;
  height: 120rpx;
}

.detail-info {
  flex: 1;
}

.detail-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #2d3436;
  margin-bottom: 8rpx;
  display: block;
}

.detail-category {
  font-size: 28rpx;
  color: #636e72;
  margin-bottom: 4rpx;
  display: block;
}

.detail-rarity {
  font-size: 24rpx;
  color: #b2bec3;
  display: block;
}

.detail-content {
  margin-bottom: 32rpx;
}

.detail-description {
  font-size: 30rpx;
  color: #2d3436;
  line-height: 1.5;
  margin-bottom: 24rpx;
  display: block;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f5f6fa;
}

.detail-label {
  font-size: 28rpx;
  color: #636e72;
}

.detail-value {
  font-size: 28rpx;
  color: #2d3436;
  font-weight: 500;
}

.detail-progress-bar {
  height: 16rpx;
  background: #f5f6fa;
  border-radius: 8rpx;
  overflow: hidden;
  margin: 16rpx 0;
}

.detail-hint {
  font-size: 26rpx;
  color: #636e72;
  text-align: center;
  margin-top: 16rpx;
}

.detail-actions {
  padding-top: 16rpx;
}
