/* subpackages/settings/about/index.wxss */
@import "../../../styles/theme.wxss";

.about-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #FF6B6B 0%, #4ECDC4 50%, #45B7D1 100%);
  padding-bottom: 40rpx;
}

/* 应用信息区域 */
.app-info-section {
  background: transparent;
  padding: 88rpx 32rpx 60rpx;
  text-align: center;
  color: white;
}

.app-logo {
  margin-bottom: 24rpx;
}

.logo-icon {
  font-size: 120rpx;
  text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}

.app-name {
  display: block;
  font-size: 48rpx;
  font-weight: 700;
  margin-bottom: 16rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.app-version {
  display: block;
  font-size: 28rpx;
  opacity: 0.9;
  margin-bottom: 24rpx;
}

.app-description {
  font-size: 26rpx;
  line-height: 1.6;
  opacity: 0.9;
  max-width: 600rpx;
  margin: 0 auto;
}

/* 通用样式 */
.section {
  margin-bottom: 32rpx;
}

.section-title {
  display: flex;
  align-items: center;
  padding: 32rpx 32rpx 24rpx;
  background: rgba(255, 255, 255, 0.12);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: white;
  margin-left: 16rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

/* 功能特色 */
.features-list {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  padding: 0 32rpx 32rpx;
}

.feature-item {
  display: flex;
  align-items: center;
  padding: 16rpx 0;
}

.feature-dot {
  width: 12rpx;
  height: 12rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  margin-right: 20rpx;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.feature-text {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.95);
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

/* 开发团队 */
.team-info {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  padding: 0 32rpx 32rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.2);
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.info-value {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.95);
  font-weight: 500;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

/* 联系我们 */
.contact-info {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  padding: 0 32rpx 32rpx;
}

.contact-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.contact-item:last-child {
  border-bottom: none;
}

.contact-item:active {
  background: rgba(255, 255, 255, 0.1);
  margin: 0 -32rpx;
  padding-left: 32rpx;
  padding-right: 32rpx;
  border-radius: 12rpx;
}

.contact-label {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  width: 120rpx;
}

.contact-value {
  flex: 1;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.95);
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.contact-action {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  padding: 8rpx 16rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 16rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

/* 法律信息 */
.legal-list {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  padding: 0 32rpx 32rpx;
}

.legal-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 0;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.legal-item:last-child {
  border-bottom: none;
}

.legal-item:active {
  background: rgba(255, 255, 255, 0.1);
  margin: 0 -32rpx;
  padding-left: 32rpx;
  padding-right: 32rpx;
  border-radius: 12rpx;
}

.legal-title {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.95);
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

/* 版本信息 */
.version-actions {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  padding: 32rpx;
  text-align: center;
}

.update-btn {
  width: 200rpx;
  height: 72rpx;
  background: rgba(255, 255, 255, 0.25);
  color: rgba(255, 255, 255, 0.95);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 36rpx;
  font-size: 28rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  backdrop-filter: blur(10rpx);
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.update-btn:active {
  transform: scale(0.98);
  background: rgba(255, 255, 255, 0.3);
}

.copyright {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  padding: 32rpx;
  text-align: center;
  border-top: 1rpx solid rgba(255, 255, 255, 0.2);
}

.copyright-text {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin-bottom: 8rpx;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.copyright-text:last-child {
  margin-bottom: 0;
}