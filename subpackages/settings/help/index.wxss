/* subpackages/settings/help/index.wxss */
@import "../../../styles/theme.wxss";

.help-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #FF6B6B 0%, #4ECDC4 50%, #45B7D1 100%);
  padding-bottom: 40rpx;
}

/* 通用样式 */
.section {
  margin-bottom: 32rpx;
}

.section-title {
  display: flex;
  align-items: center;
  padding: 32rpx 32rpx 24rpx;
  background: rgba(255, 255, 255, 0.12);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: white;
  margin-left: 16rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

/* 常见问题 */
.faq-list {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(15rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.15);
  padding: 0 32rpx 32rpx;
}

.faq-item {
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
  padding: 24rpx 0;
}

.faq-item:last-child {
  border-bottom: none;
}

.question {
  margin-bottom: 16rpx;
}

.question-text {
  font-size: 28rpx;
  font-weight: 500;
  color: white;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.answer-text {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.6;
}

/* 联系我们 */
.contact-list {
  background: white;
  padding: 0 32rpx 32rpx;
}

.contact-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f1f2f6;
  transition: all 0.3s ease;
}

.contact-item:last-child {
  border-bottom: none;
}

.contact-item:active {
  background: #f8f9fa;
  margin: 0 -32rpx;
  padding-left: 32rpx;
  padding-right: 32rpx;
}

.contact-icon {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.icon-text {
  font-size: 32rpx;
}

.contact-info {
  flex: 1;
}

.contact-label {
  display: block;
  font-size: 28rpx;
  color: #2d3436;
  margin-bottom: 4rpx;
}

.contact-value {
  font-size: 24rpx;
  color: #636e72;
}

.contact-action {
  padding: 8rpx 16rpx;
  background: rgba(255, 182, 193, 0.1);
  border-radius: 16rpx;
}

.action-text {
  font-size: 24rpx;
  color: #FFB6C1;
}

/* 意见反馈 */
.feedback-form {
  background: white;
  padding: 0 32rpx 32rpx;
}

.form-item {
  margin-bottom: 32rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #2d3436;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.feedback-picker {
  width: 100%;
}

.picker-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 1rpx solid #e9ecef;
}

.picker-text {
  font-size: 28rpx;
  color: #2d3436;
}

.feedback-textarea {
  width: 100%;
  min-height: 200rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 1rpx solid #e9ecef;
  font-size: 28rpx;
  color: #2d3436;
  line-height: 1.5;
  box-sizing: border-box;
}

.char-count {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #FFB6C1, #FF91A4);
  color: white;
  border: none;
  border-radius: 20rpx;
  font-size: 30rpx;
  font-weight: 500;
  margin-top: 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(255, 182, 193, 0.3);
  transition: all 0.3s ease;
}

.submit-btn:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 12rpx rgba(255, 182, 193, 0.4);
}

/* 使用提示 */
.tips-content {
  background: white;
  padding: 32rpx;
}

.tips-text {
  display: block;
  font-size: 26rpx;
  color: #636e72;
  line-height: 1.6;
  margin-bottom: 16rpx;
}

.tips-text:last-child {
  margin-bottom: 0;
}
