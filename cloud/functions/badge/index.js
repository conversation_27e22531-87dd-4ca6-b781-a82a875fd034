// cloud/functions/badge/index.js
// 成就徽章云函数

const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

exports.main = async (event, context) => {
  const { action, data } = event
  const { OPENID } = cloud.getWXContext()

  try {
    switch (action) {
      case 'syncUserBadges':
        return await syncUserBadges(OPENID, data)
      case 'unlockBadge':
        return await unlockBadge(OPENID, data)
      case 'checkBadgeProgress':
        return await checkBadgeProgress(OPENID, data)
      case 'getUserBadges':
        return await getUserBadges(OPENID)
      case 'batchCheckBadges':
        return await batchCheckBadges(OPENID, data)
      default:
        return {
          success: false,
          message: '未知操作类型'
        }
    }
  } catch (error) {
    console.error('徽章云函数错误:', error)
    return {
      success: false,
      message: error.message || '服务器错误'
    }
  }
}

/**
 * 同步用户徽章数据
 */
async function syncUserBadges(openid, data) {
  try {
    const { lastSyncTime = 0 } = data || {}

    // 获取用户徽章数据
    const userBadgesResult = await db.collection('user_badges')
      .where({ _openid: openid })
      .get()

    let userBadges = null
    if (userBadgesResult.data.length > 0) {
      userBadges = userBadgesResult.data[0]
      
      // 如果有lastSyncTime，只返回更新的数据
      if (lastSyncTime && userBadges.updatedAt && userBadges.updatedAt.getTime() <= lastSyncTime) {
        return {
          success: true,
          data: null, // 无更新
          message: '数据已是最新'
        }
      }
    } else {
      // 创建初始徽章数据
      userBadges = await createInitialBadgeData(openid)
    }

    return {
      success: true,
      data: userBadges
    }

  } catch (error) {
    console.error('同步用户徽章失败:', error)
    return {
      success: false,
      message: '同步失败'
    }
  }
}

/**
 * 创建初始徽章数据
 */
async function createInitialBadgeData(openid) {
  const initialData = {
    _openid: openid,
    badges: {},
    statistics: {
      totalBadges: 36, // 36个高级艺术化徽章
      unlockedBadges: 0,
      completionRate: 0,
      totalPoints: 0,
      rarityStats: {
        common: 0,
        rare: 0,
        epic: 0,
        legendary: 0
      }
    },
    createdAt: new Date(),
    updatedAt: new Date()
  }

  const result = await db.collection('user_badges').add({
    data: initialData
  })

  return {
    ...initialData,
    _id: result._id
  }
}

/**
 * 解锁徽章
 */
async function unlockBadge(openid, data) {
  try {
    const { badgeId, unlockedAt } = data

    // 获取用户徽章数据
    const userBadgesResult = await db.collection('user_badges')
      .where({ _openid: openid })
      .get()

    if (userBadgesResult.data.length === 0) {
      // 创建初始数据
      await createInitialBadgeData(openid)
    }

    // 更新徽章状态
    await db.collection('user_badges')
      .where({ _openid: openid })
      .update({
        data: {
          [`badges.${badgeId}.unlocked`]: true,
          [`badges.${badgeId}.unlockedAt`]: new Date(unlockedAt),
          [`badges.${badgeId}.lastUpdated`]: new Date(),
          updatedAt: new Date()
        }
      })

    // 记录解锁日志
    await db.collection('badge_unlock_log').add({
      data: {
        _openid: openid,
        badgeId,
        unlockedAt: new Date(unlockedAt),
        createdAt: new Date()
      }
    })

    return {
      success: true,
      message: '徽章解锁成功'
    }

  } catch (error) {
    console.error('解锁徽章失败:', error)
    return {
      success: false,
      message: '解锁失败'
    }
  }
}

/**
 * 检查徽章进度
 */
async function checkBadgeProgress(openid, data) {
  try {
    const { badgeId, evidence } = data

    // 根据徽章类型和证据检查进度
    const progress = await calculateBadgeProgress(openid, badgeId, evidence)

    if (progress !== null) {
      // 更新进度
      await db.collection('user_badges')
        .where({ _openid: openid })
        .update({
          data: {
            [`badges.${badgeId}.progress`]: progress.current,
            [`badges.${badgeId}.lastUpdated`]: new Date(),
            updatedAt: new Date()
          }
        })

      return {
        success: true,
        data: progress
      }
    }

    return {
      success: false,
      message: '无法计算进度'
    }

  } catch (error) {
    console.error('检查徽章进度失败:', error)
    return {
      success: false,
      message: '检查失败'
    }
  }
}

/**
 * 计算徽章进度
 */
async function calculateBadgeProgress(openid, badgeId, evidence) {
  try {
    switch (badgeId) {
      case 'first_record':
        return await calculateRecordCount(openid)
      case 'consecutive_days':
        return await calculateConsecutiveDays(openid)
      case 'travel_newbie':
        return await calculateTravelPlanCount(openid)
      case 'voice_expert':
        return await calculateVoiceRecordCount(openid)
      case 'new_user':
        return { current: evidence.profileComplete || 0, target: 1 }
      default:
        return null
    }
  } catch (error) {
    console.error('计算徽章进度失败:', error)
    return null
  }
}

/**
 * 计算记账次数
 */
async function calculateRecordCount(openid) {
  const result = await db.collection('records')
    .where({ _openid: openid })
    .count()

  return {
    current: result.total,
    target: 1
  }
}

/**
 * 计算连续记账天数
 */
async function calculateConsecutiveDays(openid) {
  // 获取最近的记账记录
  const records = await db.collection('records')
    .where({ _openid: openid })
    .orderBy('createTime', 'desc')
    .limit(100)
    .get()

  if (records.data.length === 0) {
    return { current: 0, target: 7 }
  }

  // 计算连续天数
  const dates = records.data.map(record => {
    const date = new Date(record.createTime)
    return date.toDateString()
  })

  const uniqueDates = [...new Set(dates)].sort((a, b) => new Date(b) - new Date(a))
  
  let consecutiveDays = 0
  const today = new Date().toDateString()
  
  for (let i = 0; i < uniqueDates.length; i++) {
    const expectedDate = new Date()
    expectedDate.setDate(expectedDate.getDate() - i)
    
    if (uniqueDates[i] === expectedDate.toDateString()) {
      consecutiveDays++
    } else {
      break
    }
  }

  return {
    current: consecutiveDays,
    target: 7
  }
}

/**
 * 计算旅行计划数量
 */
async function calculateTravelPlanCount(openid) {
  const result = await db.collection('travel_plans')
    .where({ _openid: openid })
    .count()

  return {
    current: result.total,
    target: 1
  }
}

/**
 * 计算语音记账次数
 */
async function calculateVoiceRecordCount(openid) {
  const result = await db.collection('records')
    .where({
      _openid: openid,
      recordType: 'voice'
    })
    .count()

  return {
    current: result.total,
    target: 20
  }
}

/**
 * 获取用户徽章数据
 */
async function getUserBadges(openid) {
  try {
    const result = await db.collection('user_badges')
      .where({ _openid: openid })
      .get()

    if (result.data.length === 0) {
      // 创建初始数据
      const initialData = await createInitialBadgeData(openid)
      return {
        success: true,
        data: initialData
      }
    }

    return {
      success: true,
      data: result.data[0]
    }

  } catch (error) {
    console.error('获取用户徽章失败:', error)
    return {
      success: false,
      message: '获取失败'
    }
  }
}

/**
 * 批量检查徽章
 */
async function batchCheckBadges(openid, data) {
  try {
    const { badgeIds } = data
    const results = []

    for (const badgeId of badgeIds) {
      try {
        const progress = await calculateBadgeProgress(openid, badgeId, {})
        if (progress) {
          results.push({
            badgeId,
            progress,
            success: true
          })
        }
      } catch (error) {
        results.push({
          badgeId,
          success: false,
          error: error.message
        })
      }
    }

    return {
      success: true,
      data: results
    }

  } catch (error) {
    console.error('批量检查徽章失败:', error)
    return {
      success: false,
      message: '批量检查失败'
    }
  }
}
