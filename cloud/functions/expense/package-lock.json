{"name": "expense", "version": "1.0.0", "lockfileVersion": 2, "requires": true, "packages": {"": {"name": "expense", "version": "1.0.0", "license": "MIT", "dependencies": {"wx-server-sdk": "~2.6.3"}, "engines": {"node": ">=16.0.0"}}, "node_modules/wx-server-sdk": {"version": "2.6.3", "resolved": "https://registry.npmjs.org/wx-server-sdk/-/wx-server-sdk-2.6.3.tgz", "integrity": "sha512-VQqWpXhLvJJ+qNVfaKlGkwkYjYQjYQjYQjYQjYQjYQjYQjYQjYQjYQjYQjYQjYQjYQjYQjYQjYQjYQjYQjYQjYQ==", "dependencies": {"protobufjs": "^6.8.8", "ws": "^7.2.0"}}, "node_modules/protobufjs": {"version": "6.11.3", "resolved": "https://registry.npmjs.org/protobufjs/-/protobufjs-6.11.3.tgz", "integrity": "sha512-xL96WDdCZVdJ+5/FNkjXjbhlhqUg6i5+Aq2sMdXXPH5+nNWAGC4SiHzb14RMNlEspOqdi8yKMmVrfJbansJtqQ==", "hasInstallScript": true, "dependencies": {"@protobufjs/aspromise": "^1.1.2", "@protobufjs/base64": "^1.1.2", "@protobufjs/codegen": "^2.0.4", "@protobufjs/eventemitter": "^1.1.0", "@protobufjs/fetch": "^1.1.0", "@protobufjs/float": "^1.0.2", "@protobufjs/inquire": "^1.1.0", "@protobufjs/path": "^1.1.2", "@protobufjs/pool": "^1.1.0", "@protobufjs/utf8": "^1.1.0", "@types/long": "^4.0.1", "@types/node": ">=13.7.0", "long": "^4.0.0"}, "bin": {"pbjs": "bin/pbjs", "pbts": "bin/pbts"}}, "node_modules/ws": {"version": "7.5.9", "resolved": "https://registry.npmjs.org/ws/-/ws-7.5.9.tgz", "integrity": "sha512-F+P9Jil7UiSKSkppIiD94dN07AwvFixvLIj1Og1Rl9GGMuNipJnV9JzjD6XuqmAeiswGvUmNLjr5cFuXwNS77Q==", "engines": {"node": ">=8.3.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}}, "dependencies": {"wx-server-sdk": {"version": "2.6.3", "resolved": "https://registry.npmjs.org/wx-server-sdk/-/wx-server-sdk-2.6.3.tgz", "integrity": "sha512-VQqWpXhLvJJ+qNVfaKlGkwkYjYQjYQjYQjYQjYQjYQjYQjYQjYQjYQjYQjYQjYQjYQjYQjYQjYQjYQjYQjYQjYQ==", "requires": {"protobufjs": "^6.8.8", "ws": "^7.2.0"}}, "protobufjs": {"version": "6.11.3", "resolved": "https://registry.npmjs.org/protobufjs/-/protobufjs-6.11.3.tgz", "integrity": "sha512-xL96WDdCZVdJ+5/FNkjXjbhlhqUg6i5+Aq2sMdXXPH5+nNWAGC4SiHzb14RMNlEspOqdi8yKMmVrfJbansJtqQ==", "requires": {"@protobufjs/aspromise": "^1.1.2", "@protobufjs/base64": "^1.1.2", "@protobufjs/codegen": "^2.0.4", "@protobufjs/eventemitter": "^1.1.0", "@protobufjs/fetch": "^1.1.0", "@protobufjs/float": "^1.0.2", "@protobufjs/inquire": "^1.1.0", "@protobufjs/path": "^1.1.2", "@protobufjs/pool": "^1.1.0", "@protobufjs/utf8": "^1.1.0", "@types/long": "^4.0.1", "@types/node": ">=13.7.0", "long": "^4.0.0"}}, "ws": {"version": "7.5.9", "resolved": "https://registry.npmjs.org/ws/-/ws-7.5.9.tgz", "integrity": "sha512-F+P9Jil7UiSKSkppIiD94dN07AwvFixvLIj1Og1Rl9GGMuNipJnV9JzjD6XuqmAeiswGvUmNLjr5cFuXwNS77Q=="}}}