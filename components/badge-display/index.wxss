/* components/badge-display/index.wxss */

.badge-display-container {
  width: 100%;
}

/* 徽章概览卡片 */
.badge-overview-card {
  margin: 24rpx;
  padding: 32rpx;
  border-radius: 32rpx;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20rpx) saturate(180%);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 16rpx 64rpx rgba(0, 0, 0, 0.1);
}

/* 卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.header-left {
  display: flex;
  align-items: center;
}

.trophy-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 16rpx;
}

.card-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #2d3436;
}

.header-right {
  display: flex;
  align-items: center;
}

.badge-count {
  font-size: 28rpx;
  color: #636e72;
  margin-right: 8rpx;
}

.arrow-icon {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.6;
}

/* 徽章展示区域 */
.badge-showcase {
  margin-bottom: 24rpx;
}

.badge-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.badge-item {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx;
  border-radius: 16rpx;
  background: rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.badge-item.unlocked {
  background: rgba(255, 182, 193, 0.2);
}

.badge-item.locked {
  opacity: 0.6;
}

.badge-icon-container {
  position: relative;
  margin-bottom: 8rpx;
}

.badge-icon {
  width: 80rpx;
  height: 80rpx;
}

.lock-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 50%;
}

.new-badge-indicator {
  position: absolute;
  top: -4rpx;
  right: -4rpx;
  width: 16rpx;
  height: 16rpx;
  background: #ff4d4f;
  border-radius: 50%;
  border: 2rpx solid #fff;
}

.badge-name {
  font-size: 24rpx;
  color: #2d3436;
  text-align: center;
  line-height: 1.2;
  margin-bottom: 8rpx;
}

/* 进度条 */
.progress-container {
  width: 100%;
  margin-top: 8rpx;
}

.progress-bar {
  width: 100%;
  height: 8rpx;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4rpx;
  overflow: hidden;
  margin-bottom: 4rpx;
}

.progress-bar.large {
  height: 12rpx;
  border-radius: 6rpx;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #FF6B6B, #4ECDC4);
  border-radius: inherit;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 20rpx;
  color: #636e72;
  text-align: center;
}

/* 更多徽章指示器 */
.more-badges-item {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx;
  border-radius: 16rpx;
  background: rgba(255, 255, 255, 0.1);
  border: 2rpx dashed rgba(255, 255, 255, 0.3);
}

.more-icon-container {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  margin-bottom: 8rpx;
}

.more-count {
  font-size: 28rpx;
  font-weight: 600;
  color: #636e72;
}

.more-text {
  font-size: 24rpx;
  color: #636e72;
}

/* 最近解锁 */
.recent-unlock {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx;
  background: rgba(255, 182, 193, 0.1);
  border-radius: 16rpx;
  margin-bottom: 24rpx;
}

.recent-text {
  font-size: 26rpx;
  color: #2d3436;
}

.recent-time {
  font-size: 24rpx;
  color: #636e72;
}

/* 整体进度 */
.overall-progress {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.overall-progress .progress-bar {
  flex: 1;
  margin-bottom: 0;
}

.progress-label {
  font-size: 24rpx;
  color: #636e72;
  white-space: nowrap;
}

/* 解锁动画 */
.unlock-animation-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

.unlock-animation-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 48rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 32rpx;
  backdrop-filter: blur(20rpx);
  animation: bounceIn 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.unlock-badge-large {
  margin-bottom: 24rpx;
  animation: pulse 2s infinite;
}

.unlock-badge-icon {
  width: 160rpx;
  height: 160rpx;
}

.unlock-title {
  font-size: 40rpx;
  font-weight: 600;
  color: #2d3436;
  margin-bottom: 16rpx;
}

.unlock-badge-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #FF6B6B;
  margin-bottom: 16rpx;
}

.unlock-description {
  font-size: 28rpx;
  color: #636e72;
  text-align: center;
  line-height: 1.4;
  margin-bottom: 24rpx;
}

.unlock-points {
  font-size: 30rpx;
  font-weight: 600;
  color: #52c41a;
  background: rgba(82, 196, 26, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
}

/* 动画定义 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes bounceIn {
  0% { transform: scale(0.3) rotate(-180deg); opacity: 0; }
  50% { transform: scale(1.05) rotate(0deg); opacity: 1; }
  100% { transform: scale(1) rotate(0deg); opacity: 1; }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}
