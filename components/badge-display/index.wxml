<!--components/badge-display/index.wxml-->
<view class="badge-display-container">
  <!-- 徽章概览卡片 -->
  <view class="badge-overview-card glass-card" bindtap="onViewAllBadges">
    <view class="card-header">
      <view class="header-left">
        <image class="trophy-icon" src="/images/trophy.svg" mode="aspectFit"></image>
        <text class="card-title">我的成就</text>
      </view>
      <view class="header-right">
        <text class="badge-count">{{statistics.unlockedBadges}}/{{statistics.totalBadges}}</text>
        <image class="arrow-icon" src="/images/arrow-right.svg" mode="aspectFit"></image>
      </view>
    </view>

    <!-- 徽章展示区域 -->
    <view class="badge-showcase">
      <view class="badge-grid">
        <view 
          wx:for="{{displayBadges}}" 
          wx:key="id"
          class="badge-item {{item.unlocked ? 'unlocked' : 'locked'}}"
          bindtap="onBadgeClick"
          data-badge="{{item}}"
        >
          <view class="badge-icon-container">
            <image
              class="badge-icon"
              src="/images/{{item.icon || 'badge'}}.svg"
              mode="aspectFit"
            ></image>
            <view wx:if="{{!item.unlocked}}" class="lock-overlay"></view>
            <view wx:if="{{item.unlocked && item.isNew}}" class="new-badge-indicator"></view>
          </view>
          <text class="badge-name">{{item.name}}</text>
          
          <!-- 进度条 -->
          <view wx:if="{{!item.unlocked && item.progress > 0}}" class="progress-container">
            <view class="progress-bar">
              <view 
                class="progress-fill" 
                style="width: {{(item.progress / item.target) * 100}}%"
              ></view>
            </view>
            <text class="progress-text">{{item.progress}}/{{item.target}}</text>
          </view>
        </view>

        <!-- 更多徽章指示器 -->
        <view wx:if="{{hasMoreBadges}}" class="more-badges-item" bindtap="onViewAllBadges">
          <view class="more-icon-container">
            <text class="more-count">+{{remainingCount}}</text>
          </view>
          <text class="more-text">更多</text>
        </view>
      </view>
    </view>

    <!-- 最近解锁 -->
    <view wx:if="{{recentUnlocked}}" class="recent-unlock">
      <text class="recent-text">最近解锁: {{recentUnlocked.name}}</text>
      <text class="recent-time">{{recentUnlocked.timeAgo}}</text>
    </view>

    <!-- 进度条 -->
    <view class="overall-progress">
      <view class="progress-bar large">
        <view 
          class="progress-fill" 
          style="width: {{statistics.completionRate * 100}}%"
        ></view>
      </view>
      <text class="progress-label">完成度 {{Math.round(statistics.completionRate * 100)}}%</text>
    </view>
  </view>

  <!-- 解锁动画 -->
  <view wx:if="{{showUnlockAnimation}}" class="unlock-animation-overlay">
    <view class="unlock-animation-container">
      <view class="unlock-badge-large">
        <image
          class="unlock-badge-icon"
          src="/images/{{unlockingBadge.icon || 'badge'}}.svg"
          mode="aspectFit"
        ></image>
      </view>
      <text class="unlock-title">恭喜获得徽章!</text>
      <text class="unlock-badge-name">{{unlockingBadge.name}}</text>
      <text class="unlock-description">{{unlockingBadge.description}}</text>
      <view class="unlock-points">+{{unlockingBadge.points}} 积分</view>
    </view>
  </view>
</view>
