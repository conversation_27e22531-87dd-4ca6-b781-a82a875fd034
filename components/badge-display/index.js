// components/badge-display/index.js
import badgeManager from '../../utils/badge-manager.js'

Component({
  properties: {
    // 显示模式: overview(概览) | grid(网格) | list(列表)
    mode: {
      type: String,
      value: 'overview'
    },
    // 最大显示数量
    maxDisplay: {
      type: Number,
      value: 3
    },
    // 是否显示进度
    showProgress: {
      type: Boolean,
      value: true
    }
  },

  data: {
    statistics: {
      totalBadges: 0,
      unlockedBadges: 0,
      completionRate: 0,
      totalPoints: 0
    },
    displayBadges: [],
    hasMoreBadges: false,
    remainingCount: 0,
    recentUnlocked: null,
    showUnlockAnimation: false,
    unlockingBadge: null
  },

  lifetimes: {
    attached() {
      this.initBadgeDisplay()
      this.bindEvents()
    },

    detached() {
      this.unbindEvents()
    }
  },

  methods: {
    /**
     * 初始化徽章显示
     */
    async initBadgeDisplay() {
      try {
        // 初始化徽章管理器
        await badgeManager.initialize()
        
        // 获取用户徽章数据
        const userBadges = badgeManager.getUserBadges()
        const badgeDefinitions = badgeManager.getBadgeDefinitions()
        
        // 更新统计信息
        this.setData({
          statistics: userBadges.statistics
        })
        
        // 准备显示的徽章数据
        this.prepareDisplayBadges(userBadges, badgeDefinitions)
        
        // 获取最近解锁的徽章
        this.getRecentUnlocked(userBadges)
        
      } catch (error) {
        console.error('初始化徽章显示失败:', error)
      }
    },

    /**
     * 准备显示的徽章数据
     */
    prepareDisplayBadges(userBadges, badgeDefinitions) {
      const allBadges = Object.keys(badgeDefinitions).map(badgeId => {
        const definition = badgeDefinitions[badgeId]
        const userBadge = userBadges.badges[badgeId] || { progress: 0, unlocked: false }
        
        return {
          id: badgeId,
          name: definition.name,
          description: definition.description,
          icon: definition.icon,
          category: definition.category,
          rarity: definition.rarity,
          points: definition.points,
          unlocked: userBadge.unlocked,
          progress: userBadge.progress || 0,
          target: definition.condition.target,
          unlockedAt: userBadge.unlockedAt,
          isNew: this.isNewBadge(userBadge.unlockedAt)
        }
      })

      // 排序：已解锁的在前，按解锁时间倒序；未解锁的按进度倒序
      allBadges.sort((a, b) => {
        if (a.unlocked && !b.unlocked) return -1
        if (!a.unlocked && b.unlocked) return 1
        
        if (a.unlocked && b.unlocked) {
          return (b.unlockedAt || 0) - (a.unlockedAt || 0)
        }
        
        const aProgress = a.progress / a.target
        const bProgress = b.progress / b.target
        return bProgress - aProgress
      })

      // 根据显示模式选择要显示的徽章
      const displayCount = this.data.maxDisplay
      const displayBadges = allBadges.slice(0, displayCount)
      const hasMoreBadges = allBadges.length > displayCount
      const remainingCount = allBadges.length - displayCount

      this.setData({
        displayBadges,
        hasMoreBadges,
        remainingCount
      })
    },

    /**
     * 判断是否为新徽章（3天内解锁）
     */
    isNewBadge(unlockedAt) {
      if (!unlockedAt) return false
      const threeDaysAgo = Date.now() - (3 * 24 * 60 * 60 * 1000)
      return unlockedAt > threeDaysAgo
    },

    /**
     * 获取最近解锁的徽章
     */
    getRecentUnlocked(userBadges) {
      const unlockedBadges = Object.entries(userBadges.badges)
        .filter(([_, badge]) => badge.unlocked && badge.unlockedAt)
        .sort(([_, a], [__, b]) => (b.unlockedAt || 0) - (a.unlockedAt || 0))

      if (unlockedBadges.length > 0) {
        const [badgeId, badge] = unlockedBadges[0]
        const definition = badgeManager.getBadgeDefinitions()[badgeId]
        
        this.setData({
          recentUnlocked: {
            name: definition.name,
            timeAgo: this.formatTimeAgo(badge.unlockedAt)
          }
        })
      }
    },

    /**
     * 格式化时间差
     */
    formatTimeAgo(timestamp) {
      const now = Date.now()
      const diff = now - timestamp
      const days = Math.floor(diff / (24 * 60 * 60 * 1000))
      const hours = Math.floor(diff / (60 * 60 * 1000))
      const minutes = Math.floor(diff / (60 * 1000))

      if (days > 0) return `${days}天前`
      if (hours > 0) return `${hours}小时前`
      if (minutes > 0) return `${minutes}分钟前`
      return '刚刚'
    },

    /**
     * 绑定事件监听
     */
    bindEvents() {
      // 监听徽章解锁事件
      if (wx.eventBus) {
        wx.eventBus.on('badgeUnlocked', this.onBadgeUnlocked.bind(this))
      }
    },

    /**
     * 解绑事件监听
     */
    unbindEvents() {
      if (wx.eventBus) {
        wx.eventBus.off('badgeUnlocked', this.onBadgeUnlocked.bind(this))
      }
    },

    /**
     * 处理徽章解锁事件
     */
    onBadgeUnlocked(event) {
      const { badge } = event
      
      // 显示解锁动画
      this.setData({
        showUnlockAnimation: true,
        unlockingBadge: badge
      })

      // 3秒后隐藏动画
      setTimeout(() => {
        this.setData({
          showUnlockAnimation: false,
          unlockingBadge: null
        })
        
        // 刷新徽章显示
        this.initBadgeDisplay()
      }, 3000)
    },

    /**
     * 点击徽章
     */
    onBadgeClick(e) {
      const badge = e.currentTarget.dataset.badge
      
      // 触发事件给父组件
      this.triggerEvent('badgeClick', { badge })
      
      // 显示徽章详情
      this.showBadgeDetail(badge)
    },

    /**
     * 显示徽章详情
     */
    showBadgeDetail(badge) {
      const categoryConfig = badgeManager.getCategoryConfig()
      const rarityConfig = badgeManager.getRarityConfig()
      
      const content = `${badge.description}\n\n` +
        `分类: ${categoryConfig[badge.category]?.name || '未知'}\n` +
        `稀有度: ${rarityConfig[badge.rarity]?.name || '普通'}\n` +
        `积分: ${badge.points}\n` +
        (badge.unlocked 
          ? `解锁时间: ${new Date(badge.unlockedAt).toLocaleDateString()}`
          : `进度: ${badge.progress}/${badge.target}`)

      wx.showModal({
        title: badge.name,
        content: content,
        showCancel: false,
        confirmText: '确定'
      })
    },

    /**
     * 查看所有徽章
     */
    onViewAllBadges() {
      // 触发事件给父组件
      this.triggerEvent('viewAllBadges')
      
      // 跳转到徽章详情页面
      wx.navigateTo({
        url: '/subpackages/settings/badges/index',
        fail: (err) => {
          console.error('跳转徽章页面失败:', err)
          wx.showToast({
            title: '页面跳转失败',
            icon: 'none'
          })
        }
      })
    },

    /**
     * 刷新徽章数据
     */
    async refreshBadges() {
      await this.initBadgeDisplay()
    },

    /**
     * 检查特定徽章进度
     */
    async checkBadgeProgress(badgeId, evidence) {
      try {
        const result = await badgeManager.checkBadgeProgress(badgeId, evidence)
        if (result && result.unlocked) {
          // 徽章解锁，刷新显示
          await this.initBadgeDisplay()
        }
        return result
      } catch (error) {
        console.error('检查徽章进度失败:', error)
        return false
      }
    }
  }
})
