<!--components/legendary-unlock/index.wxml-->
<view wx:if="{{showCeremony}}" class="legendary-ceremony-overlay">
  <!-- 背景粒子画布 -->
  <canvas 
    class="particle-canvas" 
    canvas-id="particleCanvas"
    disable-scroll="true">
  </canvas>
  
  <!-- 仪式内容 -->
  <view class="ceremony-content">
    <!-- 第一阶段：屏幕闪光 -->
    <view wx:if="{{phase === 1}}" class="phase-flash">
      <view class="flash-overlay"></view>
      <text class="flash-text">传说成就解锁！</text>
    </view>
    
    <!-- 第二阶段：徽章显现 -->
    <view wx:if="{{phase === 2}}" class="phase-materialize">
      <view class="badge-container">
        <image 
          class="legendary-badge-icon {{animationClass}}" 
          src="/images/{{badgeData.icon || 'badge'}}.svg" 
          mode="aspectFit">
        </image>
        
        <!-- 金色光芒 -->
        <view class="golden-rays">
          <view class="ray ray-1"></view>
          <view class="ray ray-2"></view>
          <view class="ray ray-3"></view>
          <view class="ray ray-4"></view>
          <view class="ray ray-5"></view>
          <view class="ray ray-6"></view>
          <view class="ray ray-7"></view>
          <view class="ray ray-8"></view>
        </view>
        
        <!-- 粒子爆炸效果 -->
        <view class="particle-explosion">
          <view wx:for="{{particles}}" wx:key="index" 
                class="particle particle-{{item.type}}"
                style="left: {{item.x}}px; top: {{item.y}}px; animation-delay: {{item.delay}}s;">
          </view>
        </view>
      </view>
      
      <!-- 徽章信息 -->
      <view class="badge-info">
        <text class="badge-name">{{badgeData.name}}</text>
        <text class="badge-description">{{badgeData.description}}</text>
        <view class="rarity-info">
          <text class="rarity-text">全服仅有 {{rarityPercentage}}% 的用户获得</text>
          <view class="rarity-bar">
            <view class="rarity-fill" style="width: {{rarityPercentage}}%"></view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 第三阶段：庆祝效果 -->
    <view wx:if="{{phase === 3}}" class="phase-celebration">
      <!-- 五彩纸屑 -->
      <view class="confetti-container">
        <view wx:for="{{confetti}}" wx:key="index" 
              class="confetti confetti-{{item.color}}"
              style="left: {{item.x}}px; animation-delay: {{item.delay}}s;">
        </view>
      </view>
      
      <!-- 成就横幅 -->
      <view class="achievement-banner">
        <view class="banner-content">
          <image class="banner-icon" src="/images/{{badgeData.icon || 'badge'}}.svg" mode="aspectFit"></image>
          <view class="banner-text">
            <text class="banner-title">传说成就解锁</text>
            <text class="banner-subtitle">{{badgeData.name}}</text>
          </view>
          <view class="banner-points">+{{badgeData.points}}</view>
        </view>
      </view>
      
      <!-- 继续按钮 -->
      <view class="continue-button" bindtap="onContinue">
        <text class="continue-text">点击继续</text>
        <view class="button-glow"></view>
      </view>
    </view>
  </view>
  
  <!-- 音效控制 -->
  <audio 
    wx:if="{{audioEnabled}}" 
    id="ceremonyAudio" 
    src="{{audioSrc}}" 
    autoplay="{{true}}"
    loop="{{false}}">
  </audio>
</view>
