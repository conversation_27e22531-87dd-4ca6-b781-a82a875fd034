// components/legendary-unlock/index.js
Component({
  properties: {
    badgeData: {
      type: Object,
      value: {}
    },
    rarityPercentage: {
      type: Number,
      value: 1.0
    },
    audioEnabled: {
      type: Boolean,
      value: true
    }
  },

  data: {
    showCeremony: false,
    phase: 1,
    animationClass: '',
    particles: [],
    confetti: [],
    audioSrc: '/audio/legendary-fanfare.mp3'
  },

  lifetimes: {
    attached() {
      // 监听传说级徽章解锁事件
      if (wx.eventBus) {
        wx.eventBus.on('legendaryBadgeUnlocked', this.startCeremony.bind(this))
      }
    },

    detached() {
      if (wx.eventBus) {
        wx.eventBus.off('legendaryBadgeUnlocked', this.startCeremony.bind(this))
      }
    }
  },

  methods: {
    /**
     * 开始传说级解锁仪式
     */
    startCeremony(eventData) {
      const { badge, rarity, ceremony } = eventData

      this.setData({
        showCeremony: true,
        badgeData: badge,
        rarityPercentage: rarity.percentage,
        phase: 1
      })

      // 初始化粒子画布
      this.initParticleCanvas()

      // 开始仪式序列
      this.executePhaseSequence(ceremony.duration)
    },

    /**
     * 执行仪式阶段序列
     */
    executePhaseSequence(totalDuration) {
      // 第一阶段：屏幕闪光 (1秒)
      setTimeout(() => {
        this.setData({ phase: 2 })
        this.startPhase2Animation()
      }, 1000)

      // 第二阶段：徽章显现 (2秒)
      setTimeout(() => {
        this.setData({ phase: 3 })
        this.startPhase3Animation()
      }, 3000)
    },

    /**
     * 第二阶段动画
     */
    startPhase2Animation() {
      // 生成粒子
      const particles = this.generateParticles(30)
      
      this.setData({
        animationClass: 'materialize-animation',
        particles: particles
      })

      // 启动粒子动画
      this.animateParticles()
    },

    /**
     * 第三阶段动画
     */
    startPhase3Animation() {
      // 生成五彩纸屑
      const confetti = this.generateConfetti(50)
      
      this.setData({
        confetti: confetti
      })

      // 启动纸屑动画
      this.animateConfetti()
    },

    /**
     * 生成粒子
     */
    generateParticles(count) {
      const particles = []
      const centerX = 64 // 相对于徽章中心
      const centerY = 64

      for (let i = 0; i < count; i++) {
        const angle = (Math.PI * 2 * i) / count
        const radius = 30 + Math.random() * 20
        
        particles.push({
          x: centerX + Math.cos(angle) * radius,
          y: centerY + Math.sin(angle) * radius,
          type: Math.floor(Math.random() * 3) + 1, // 1-3种类型
          delay: Math.random() * 0.5
        })
      }

      return particles
    },

    /**
     * 生成五彩纸屑
     */
    generateConfetti(count) {
      const confetti = []
      const colors = ['red', 'gold', 'blue', 'green', 'purple']

      for (let i = 0; i < count; i++) {
        confetti.push({
          x: Math.random() * 300, // 屏幕宽度
          color: colors[Math.floor(Math.random() * colors.length)],
          delay: Math.random() * 2
        })
      }

      return confetti
    },

    /**
     * 初始化粒子画布
     */
    initParticleCanvas() {
      const ctx = wx.createCanvasContext('particleCanvas', this)
      
      // 设置画布背景
      ctx.fillStyle = 'rgba(0, 0, 0, 0.8)'
      ctx.fillRect(0, 0, 375, 667) // 假设屏幕尺寸
      
      // 绘制动态背景效果
      this.drawDynamicBackground(ctx)
      
      ctx.draw()
    },

    /**
     * 绘制动态背景
     */
    drawDynamicBackground(ctx) {
      // 绘制渐变背景
      const gradient = ctx.createLinearGradient(0, 0, 375, 667)
      gradient.addColorStop(0, 'rgba(108, 92, 231, 0.3)')
      gradient.addColorStop(0.5, 'rgba(162, 155, 254, 0.2)')
      gradient.addColorStop(1, 'rgba(108, 92, 231, 0.3)')
      
      ctx.fillStyle = gradient
      ctx.fillRect(0, 0, 375, 667)

      // 绘制星空效果
      for (let i = 0; i < 20; i++) {
        const x = Math.random() * 375
        const y = Math.random() * 667
        const radius = Math.random() * 2 + 1
        
        ctx.beginPath()
        ctx.arc(x, y, radius, 0, Math.PI * 2)
        ctx.fillStyle = `rgba(255, 255, 255, ${Math.random() * 0.8 + 0.2})`
        ctx.fill()
      }
    },

    /**
     * 粒子动画
     */
    animateParticles() {
      // 这里可以添加更复杂的粒子动画逻辑
      // 由于小程序限制，主要依赖CSS动画
    },

    /**
     * 纸屑动画
     */
    animateConfetti() {
      // 纸屑下落动画主要通过CSS实现
    },

    /**
     * 继续按钮点击
     */
    onContinue() {
      // 触觉反馈
      if (wx.canIUse('vibrateShort')) {
        wx.vibrateShort({ type: 'light' })
      }

      // 关闭仪式
      this.setData({
        showCeremony: false,
        phase: 1,
        particles: [],
        confetti: []
      })

      // 触发完成事件
      this.triggerEvent('ceremonyComplete', {
        badge: this.data.badgeData
      })
    },

    /**
     * 跳过仪式
     */
    skipCeremony() {
      this.onContinue()
    }
  }
})
