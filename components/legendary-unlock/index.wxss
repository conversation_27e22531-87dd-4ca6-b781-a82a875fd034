/* components/legendary-unlock/index.wxss */

.legendary-ceremony-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background: rgba(0, 0, 0, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 粒子画布 */
.particle-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

/* 仪式内容 */
.ceremony-content {
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

/* 第一阶段：屏幕闪光 */
.phase-flash {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.flash-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, #fdcb6e, #e17055, #fdcb6e);
  animation: flashEffect 1s ease-out;
}

.flash-text {
  position: relative;
  z-index: 3;
  font-size: 48rpx;
  font-weight: bold;
  color: #fff;
  text-shadow: 0 0 20rpx rgba(255, 255, 255, 0.8);
  animation: textGlow 1s ease-out;
}

/* 第二阶段：徽章显现 */
.phase-materialize {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.badge-container {
  position: relative;
  margin-bottom: 60rpx;
}

.legendary-badge-icon {
  width: 320rpx;
  height: 320rpx;
  z-index: 3;
  position: relative;
}

.legendary-badge-icon.materialize-animation {
  animation: badgeMaterialize 2s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* 金色光芒 */
.golden-rays {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 400rpx;
  height: 400rpx;
  z-index: 1;
}

.ray {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 4rpx;
  height: 200rpx;
  background: linear-gradient(to top, transparent, #fdcb6e, transparent);
  transform-origin: bottom center;
  animation: rayRotate 3s linear infinite;
}

.ray-1 { transform: translate(-50%, -100%) rotate(0deg); }
.ray-2 { transform: translate(-50%, -100%) rotate(45deg); }
.ray-3 { transform: translate(-50%, -100%) rotate(90deg); }
.ray-4 { transform: translate(-50%, -100%) rotate(135deg); }
.ray-5 { transform: translate(-50%, -100%) rotate(180deg); }
.ray-6 { transform: translate(-50%, -100%) rotate(225deg); }
.ray-7 { transform: translate(-50%, -100%) rotate(270deg); }
.ray-8 { transform: translate(-50%, -100%) rotate(315deg); }

/* 粒子爆炸 */
.particle-explosion {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 400rpx;
  height: 400rpx;
  z-index: 2;
}

.particle {
  position: absolute;
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  animation: particleExplode 1.5s ease-out forwards;
}

.particle-1 { background: #fdcb6e; }
.particle-2 { background: #e17055; }
.particle-3 { background: #fff; }

/* 徽章信息 */
.badge-info {
  text-align: center;
  color: #fff;
  animation: fadeInUp 1s ease-out 0.5s both;
}

.badge-name {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
  background: linear-gradient(45deg, #fdcb6e, #e17055);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.badge-description {
  display: block;
  font-size: 28rpx;
  opacity: 0.9;
  margin-bottom: 32rpx;
  line-height: 1.4;
}

.rarity-info {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.rarity-text {
  font-size: 24rpx;
  opacity: 0.8;
  margin-bottom: 16rpx;
}

.rarity-bar {
  width: 200rpx;
  height: 8rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4rpx;
  overflow: hidden;
}

.rarity-fill {
  height: 100%;
  background: linear-gradient(90deg, #fdcb6e, #e17055);
  border-radius: 4rpx;
  animation: fillBar 1s ease-out 1s both;
}

/* 第三阶段：庆祝效果 */
.phase-celebration {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

/* 五彩纸屑 */
.confetti-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  z-index: 1;
}

.confetti {
  position: absolute;
  top: -20rpx;
  width: 16rpx;
  height: 16rpx;
  animation: confettiFall 3s linear infinite;
}

.confetti-red { background: #ff6b6b; }
.confetti-gold { background: #fdcb6e; }
.confetti-blue { background: #45b7d1; }
.confetti-green { background: #00b894; }
.confetti-purple { background: #6c5ce7; }

/* 成就横幅 */
.achievement-banner {
  position: relative;
  z-index: 2;
  background: linear-gradient(135deg, rgba(253, 203, 110, 0.9), rgba(225, 112, 85, 0.9));
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 80rpx;
  backdrop-filter: blur(20rpx);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  animation: bannerSlideIn 1s ease-out;
}

.banner-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.banner-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: 24rpx;
}

.banner-text {
  flex: 1;
  color: #fff;
}

.banner-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.banner-subtitle {
  display: block;
  font-size: 28rpx;
  opacity: 0.9;
}

.banner-points {
  font-size: 36rpx;
  font-weight: bold;
  color: #fff;
  text-shadow: 0 0 10rpx rgba(255, 255, 255, 0.5);
}

/* 继续按钮 */
.continue-button {
  position: relative;
  background: linear-gradient(135deg, #fdcb6e, #e17055);
  border-radius: 50rpx;
  padding: 24rpx 48rpx;
  cursor: pointer;
  animation: buttonPulse 2s ease-in-out infinite;
}

.continue-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #fff;
  position: relative;
  z-index: 2;
}

.button-glow {
  position: absolute;
  top: -4rpx;
  left: -4rpx;
  right: -4rpx;
  bottom: -4rpx;
  background: linear-gradient(135deg, #fdcb6e, #e17055);
  border-radius: 54rpx;
  opacity: 0.5;
  filter: blur(8rpx);
  z-index: 1;
}

/* 动画定义 */
@keyframes flashEffect {
  0% { opacity: 0; }
  50% { opacity: 1; }
  100% { opacity: 0; }
}

@keyframes textGlow {
  0% { transform: scale(0.5); opacity: 0; }
  50% { transform: scale(1.1); opacity: 1; }
  100% { transform: scale(1); opacity: 1; }
}

@keyframes badgeMaterialize {
  0% { transform: scale(0) rotate(-180deg); opacity: 0; }
  50% { transform: scale(1.2) rotate(0deg); opacity: 1; }
  100% { transform: scale(1) rotate(0deg); opacity: 1; }
}

@keyframes rayRotate {
  0% { transform: translate(-50%, -100%) rotate(0deg); }
  100% { transform: translate(-50%, -100%) rotate(360deg); }
}

@keyframes particleExplode {
  0% { transform: scale(0); opacity: 1; }
  100% { transform: scale(3); opacity: 0; }
}

@keyframes fadeInUp {
  0% { transform: translateY(40rpx); opacity: 0; }
  100% { transform: translateY(0); opacity: 1; }
}

@keyframes fillBar {
  0% { width: 0%; }
  100% { width: var(--fill-width, 1%); }
}

@keyframes confettiFall {
  0% { transform: translateY(-20rpx) rotate(0deg); }
  100% { transform: translateY(800rpx) rotate(360deg); }
}

@keyframes bannerSlideIn {
  0% { transform: translateY(-100rpx); opacity: 0; }
  100% { transform: translateY(0); opacity: 1; }
}

@keyframes buttonPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}
