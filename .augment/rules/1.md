---
type: "always_apply"
---

每次开始之前请阅读DEVELOPMENT_GUIDE.md，FEATURE_DESIGN.md，了解最新项目进度，每次进行完成修改优化之后及时更新DEVELOPMENT_GUIDE.md，FEATURE_DESIGN.md，避免过度冗余过时的及时删减，  
不要每次完成任务就写一个文档 
每次开发前必须了解项目，必须仔细阅读微信小程序官方开发文档，遵守开发要求
每次玩完成任务，结束前调用寸止mcp
调用sequential-thinking输出可执行步骤列表每次开始之前请阅读DEVELOPMENT_GUIDE.md，FEATURE_DESIGN.md，了解最新项目进度，每次进行完成修改优化之后及时更新DEVELOPMENT_GUIDE.md，FEATURE_DESIGN.md，避免过度冗余过时的及时删减，  
不要每次完成任务就写一个文档 
每次开发前必须了解项目，必须仔细阅读微信小程序官方开发文档，遵守开发要求
Sequential Thinking，context7，fetch，Memory按需调用相关mcp