/**
 * 现代化按钮系统 v2.0
 * 基于统一设计令牌的完整按钮体系
 */

/* === 按钮基础样式 === */
.btn-base {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  border: none;
  font-weight: var(--font-weight-medium);
  text-align: center;
  cursor: pointer;
  transition: all var(--duration-normal) var(--easing-standard);
  overflow: hidden;
  box-sizing: border-box;
  user-select: none;
  -webkit-user-select: none;
}

/* === 波纹扩散效果 === */
.btn-base::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.25);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.6s var(--easing-decelerate), 
              height 0.6s var(--easing-decelerate);
  pointer-events: none;
  z-index: 1;
}

.btn-base:active::before {
  width: 300rpx;
  height: 300rpx;
}

/* === 按钮内容层级 === */
.btn-base .btn-content {
  position: relative;
  z-index: 2;
}

.btn-base text,
.btn-base view,
.btn-base image {
  position: relative;
  z-index: 2;
}

/* === 主要按钮样式 === */
.btn-primary {
  background: var(--gradient-button);
  color: var(--text-white);
  box-shadow: var(--shadow-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-md) var(--spacing-lg);
  font-size: var(--font-md);
}

.btn-primary:active {
  transform: translateY(2rpx) scale(0.98);
  box-shadow: var(--shadow-sm);
  transition: all var(--duration-fast) var(--easing-bounce);
}

/* === 次要按钮样式 === */
.btn-secondary {
  background: linear-gradient(135deg, var(--brand-secondary) 0%, var(--brand-secondary-light) 100%);
  color: var(--text-white);
  box-shadow: var(--shadow-secondary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-md) var(--spacing-lg);
  font-size: var(--font-md);
}

.btn-secondary:active {
  transform: translateY(2rpx) scale(0.98);
  box-shadow: var(--shadow-sm);
  transition: all var(--duration-fast) var(--easing-bounce);
}

/* === 强调按钮样式 === */
.btn-accent {
  background: linear-gradient(135deg, var(--brand-accent) 0%, var(--brand-accent-light) 100%);
  color: var(--text-white);
  box-shadow: var(--shadow-accent);
  border-radius: var(--radius-lg);
  padding: var(--spacing-md) var(--spacing-lg);
  font-size: var(--font-md);
}

.btn-accent:active {
  transform: translateY(2rpx) scale(0.98);
  box-shadow: var(--shadow-sm);
  transition: all var(--duration-fast) var(--easing-bounce);
}

/* === 幽灵按钮样式 === */
.btn-ghost {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-white-secondary);
  border: 2rpx solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-lg);
  padding: var(--spacing-md) var(--spacing-lg);
  font-size: var(--font-md);
  backdrop-filter: blur(16rpx);
  -webkit-backdrop-filter: blur(16rpx);
}

.btn-ghost:active {
  transform: translateY(2rpx) scale(0.98);
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transition: all var(--duration-fast) var(--easing-bounce);
}

/* === 文本按钮样式 === */
.btn-text {
  background: transparent;
  color: var(--brand-primary);
  border: none;
  border-radius: var(--radius-md);
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--font-md);
}

.btn-text:active {
  background: var(--brand-primary-alpha-10);
  transform: scale(0.98);
  transition: all var(--duration-fast) var(--easing-bounce);
}

/* === 按钮尺寸变体 === */
.btn-large {
  padding: var(--spacing-lg) var(--spacing-xl);
  font-size: var(--font-lg);
  border-radius: var(--radius-xl);
}

.btn-small {
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--font-sm);
  border-radius: var(--radius-md);
}

.btn-mini {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--font-xs);
  border-radius: var(--radius-sm);
}

/* === 特殊形状按钮 === */
.btn-round {
  border-radius: var(--radius-round);
}

.btn-square {
  width: 80rpx;
  height: 80rpx;
  padding: 0;
  border-radius: var(--radius-lg);
}

.btn-block {
  width: 100%;
  display: flex;
}

/* === 按钮状态 === */
.btn-loading {
  pointer-events: none;
  opacity: 0.7;
}

.btn-loading::after {
  content: '';
  position: absolute;
  width: 32rpx;
  height: 32rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid var(--text-white);
  border-radius: 50%;
  animation: btn-spin 1s linear infinite;
  z-index: 3;
}

.btn-disabled {
  pointer-events: none;
  opacity: 0.5;
  transform: none !important;
}

/* === 按钮动画 === */
@keyframes btn-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* === 悬停效果（支持的设备） === */
@media (hover: hover) {
  .btn-base:hover {
    transform: translateY(-2rpx);
    box-shadow: var(--shadow-lg);
  }
  
  .btn-primary:hover {
    box-shadow: var(--shadow-primary), var(--shadow-lg);
  }
  
  .btn-secondary:hover {
    box-shadow: var(--shadow-secondary), var(--shadow-lg);
  }
  
  .btn-accent:hover {
    box-shadow: var(--shadow-accent), var(--shadow-lg);
  }
  
  .btn-ghost:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
  }
  
  .btn-text:hover {
    background: var(--brand-primary-alpha-10);
  }
}

/* === 响应式设计 === */
@media (max-width: 750rpx) {
  .btn-base {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-sm);
  }
  
  .btn-large {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: var(--font-md);
  }
  
  .btn-square {
    width: 72rpx;
    height: 72rpx;
  }
}

/* === 深色模式适配 === */
@media (prefers-color-scheme: dark) {
  .btn-ghost {
    background: rgba(255, 255, 255, 0.05);
    color: rgba(255, 255, 255, 0.8);
    border-color: rgba(255, 255, 255, 0.2);
  }
  
  .btn-ghost:active {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.3);
  }
  
  .btn-text {
    color: var(--brand-primary-light);
  }
}
