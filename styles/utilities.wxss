/**
 * 统一工具类系统
 * 基于设计令牌的原子化CSS工具类
 */

/* === 布局工具类 === */
.flex {
  display: flex;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-around {
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.flex-evenly {
  display: flex;
  align-items: center;
  justify-content: space-evenly;
}

.flex-start {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}

.flex-end {
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-nowrap {
  flex-wrap: nowrap;
}

.flex-1 {
  flex: 1;
}

.flex-auto {
  flex: auto;
}

.flex-none {
  flex: none;
}

/* === 间距工具类 === */
/* Padding */
.p-xs { padding: var(--spacing-xs); }
.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }
.p-xl { padding: var(--spacing-xl); }
.p-xxl { padding: var(--spacing-xxl); }
.p-0 { padding: 0; }

/* Padding Top */
.pt-xs { padding-top: var(--spacing-xs); }
.pt-sm { padding-top: var(--spacing-sm); }
.pt-md { padding-top: var(--spacing-md); }
.pt-lg { padding-top: var(--spacing-lg); }
.pt-xl { padding-top: var(--spacing-xl); }
.pt-xxl { padding-top: var(--spacing-xxl); }
.pt-0 { padding-top: 0; }

/* Padding Bottom */
.pb-xs { padding-bottom: var(--spacing-xs); }
.pb-sm { padding-bottom: var(--spacing-sm); }
.pb-md { padding-bottom: var(--spacing-md); }
.pb-lg { padding-bottom: var(--spacing-lg); }
.pb-xl { padding-bottom: var(--spacing-xl); }
.pb-xxl { padding-bottom: var(--spacing-xxl); }
.pb-0 { padding-bottom: 0; }

/* Padding Left */
.pl-xs { padding-left: var(--spacing-xs); }
.pl-sm { padding-left: var(--spacing-sm); }
.pl-md { padding-left: var(--spacing-md); }
.pl-lg { padding-left: var(--spacing-lg); }
.pl-xl { padding-left: var(--spacing-xl); }
.pl-xxl { padding-left: var(--spacing-xxl); }
.pl-0 { padding-left: 0; }

/* Padding Right */
.pr-xs { padding-right: var(--spacing-xs); }
.pr-sm { padding-right: var(--spacing-sm); }
.pr-md { padding-right: var(--spacing-md); }
.pr-lg { padding-right: var(--spacing-lg); }
.pr-xl { padding-right: var(--spacing-xl); }
.pr-xxl { padding-right: var(--spacing-xxl); }
.pr-0 { padding-right: 0; }

/* Padding Horizontal */
.px-xs { padding-left: var(--spacing-xs); padding-right: var(--spacing-xs); }
.px-sm { padding-left: var(--spacing-sm); padding-right: var(--spacing-sm); }
.px-md { padding-left: var(--spacing-md); padding-right: var(--spacing-md); }
.px-lg { padding-left: var(--spacing-lg); padding-right: var(--spacing-lg); }
.px-xl { padding-left: var(--spacing-xl); padding-right: var(--spacing-xl); }
.px-xxl { padding-left: var(--spacing-xxl); padding-right: var(--spacing-xxl); }
.px-0 { padding-left: 0; padding-right: 0; }

/* Padding Vertical */
.py-xs { padding-top: var(--spacing-xs); padding-bottom: var(--spacing-xs); }
.py-sm { padding-top: var(--spacing-sm); padding-bottom: var(--spacing-sm); }
.py-md { padding-top: var(--spacing-md); padding-bottom: var(--spacing-md); }
.py-lg { padding-top: var(--spacing-lg); padding-bottom: var(--spacing-lg); }
.py-xl { padding-top: var(--spacing-xl); padding-bottom: var(--spacing-xl); }
.py-xxl { padding-top: var(--spacing-xxl); padding-bottom: var(--spacing-xxl); }
.py-0 { padding-top: 0; padding-bottom: 0; }

/* Margin */
.m-xs { margin: var(--spacing-xs); }
.m-sm { margin: var(--spacing-sm); }
.m-md { margin: var(--spacing-md); }
.m-lg { margin: var(--spacing-lg); }
.m-xl { margin: var(--spacing-xl); }
.m-xxl { margin: var(--spacing-xxl); }
.m-0 { margin: 0; }
.m-auto { margin: auto; }

/* Margin Top */
.mt-xs { margin-top: var(--spacing-xs); }
.mt-sm { margin-top: var(--spacing-sm); }
.mt-md { margin-top: var(--spacing-md); }
.mt-lg { margin-top: var(--spacing-lg); }
.mt-xl { margin-top: var(--spacing-xl); }
.mt-xxl { margin-top: var(--spacing-xxl); }
.mt-0 { margin-top: 0; }
.mt-auto { margin-top: auto; }

/* Margin Bottom */
.mb-xs { margin-bottom: var(--spacing-xs); }
.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }
.mb-xl { margin-bottom: var(--spacing-xl); }
.mb-xxl { margin-bottom: var(--spacing-xxl); }
.mb-0 { margin-bottom: 0; }
.mb-auto { margin-bottom: auto; }

/* Margin Left */
.ml-xs { margin-left: var(--spacing-xs); }
.ml-sm { margin-left: var(--spacing-sm); }
.ml-md { margin-left: var(--spacing-md); }
.ml-lg { margin-left: var(--spacing-lg); }
.ml-xl { margin-left: var(--spacing-xl); }
.ml-xxl { margin-left: var(--spacing-xxl); }
.ml-0 { margin-left: 0; }
.ml-auto { margin-left: auto; }

/* Margin Right */
.mr-xs { margin-right: var(--spacing-xs); }
.mr-sm { margin-right: var(--spacing-sm); }
.mr-md { margin-right: var(--spacing-md); }
.mr-lg { margin-right: var(--spacing-lg); }
.mr-xl { margin-right: var(--spacing-xl); }
.mr-xxl { margin-right: var(--spacing-xxl); }
.mr-0 { margin-right: 0; }
.mr-auto { margin-right: auto; }

/* Margin Horizontal */
.mx-xs { margin-left: var(--spacing-xs); margin-right: var(--spacing-xs); }
.mx-sm { margin-left: var(--spacing-sm); margin-right: var(--spacing-sm); }
.mx-md { margin-left: var(--spacing-md); margin-right: var(--spacing-md); }
.mx-lg { margin-left: var(--spacing-lg); margin-right: var(--spacing-lg); }
.mx-xl { margin-left: var(--spacing-xl); margin-right: var(--spacing-xl); }
.mx-xxl { margin-left: var(--spacing-xxl); margin-right: var(--spacing-xxl); }
.mx-0 { margin-left: 0; margin-right: 0; }
.mx-auto { margin-left: auto; margin-right: auto; }

/* Margin Vertical */
.my-xs { margin-top: var(--spacing-xs); margin-bottom: var(--spacing-xs); }
.my-sm { margin-top: var(--spacing-sm); margin-bottom: var(--spacing-sm); }
.my-md { margin-top: var(--spacing-md); margin-bottom: var(--spacing-md); }
.my-lg { margin-top: var(--spacing-lg); margin-bottom: var(--spacing-lg); }
.my-xl { margin-top: var(--spacing-xl); margin-bottom: var(--spacing-xl); }
.my-xxl { margin-top: var(--spacing-xxl); margin-bottom: var(--spacing-xxl); }
.my-0 { margin-top: 0; margin-bottom: 0; }
.my-auto { margin-top: auto; margin-bottom: auto; }

/* === 文本工具类 === */
.text-xs { font-size: var(--font-xs); }
.text-sm { font-size: var(--font-sm); }
.text-md { font-size: var(--font-md); }
.text-lg { font-size: var(--font-lg); }
.text-xl { font-size: var(--font-xl); }
.text-xxl { font-size: var(--font-xxl); }
.text-xxxl { font-size: var(--font-xxxl); }

.text-light { font-weight: var(--font-weight-light); }
.text-normal { font-weight: var(--font-weight-normal); }
.text-medium { font-weight: var(--font-weight-medium); }
.text-semibold { font-weight: var(--font-weight-semibold); }
.text-bold { font-weight: var(--font-weight-bold); }

.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-justify { text-align: justify; }

.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-tertiary { color: var(--text-tertiary); }
.text-disabled { color: var(--text-disabled); }
.text-white { color: var(--text-white); }
.text-white-secondary { color: var(--text-white-secondary); }
.text-white-tertiary { color: var(--text-white-tertiary); }
.text-white-disabled { color: var(--text-white-disabled); }

.text-brand-primary { color: var(--brand-primary); }
.text-brand-secondary { color: var(--brand-secondary); }
.text-brand-accent { color: var(--brand-accent); }

.text-success { color: var(--color-success); }
.text-warning { color: var(--color-warning); }
.text-error { color: var(--color-error); }
.text-info { color: var(--color-info); }

.line-height-tight { line-height: var(--line-height-tight); }
.line-height-normal { line-height: var(--line-height-normal); }
.line-height-relaxed { line-height: var(--line-height-relaxed); }
.line-height-loose { line-height: var(--line-height-loose); }

.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-ellipsis-2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.text-ellipsis-3 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

/* === 背景工具类 === */
.bg-primary { background-color: var(--bg-primary); }
.bg-secondary { background-color: var(--bg-secondary); }
.bg-tertiary { background-color: var(--bg-tertiary); }

.bg-brand-primary { background-color: var(--brand-primary); }
.bg-brand-secondary { background-color: var(--brand-secondary); }
.bg-brand-accent { background-color: var(--brand-accent); }

.bg-success { background-color: var(--color-success); }
.bg-warning { background-color: var(--color-warning); }
.bg-error { background-color: var(--color-error); }
.bg-info { background-color: var(--color-info); }

.bg-gradient-primary { background: var(--gradient-primary); }
.bg-gradient-card { background: var(--gradient-card); }
.bg-gradient-button { background: var(--gradient-button); }

.bg-transparent { background-color: transparent; }

/* === 圆角工具类 === */
.rounded-xs { border-radius: var(--radius-xs); }
.rounded-sm { border-radius: var(--radius-sm); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-xxl { border-radius: var(--radius-xxl); }
.rounded-full { border-radius: var(--radius-round); }
.rounded-none { border-radius: 0; }

/* === 阴影工具类 === */
.shadow-xs { box-shadow: var(--shadow-xs); }
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }
.shadow-2xl { box-shadow: var(--shadow-2xl); }
.shadow-none { box-shadow: none; }

.shadow-primary { box-shadow: var(--shadow-primary); }
.shadow-secondary { box-shadow: var(--shadow-secondary); }
.shadow-accent { box-shadow: var(--shadow-accent); }

/* === 定位工具类 === */
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.sticky { position: sticky; }
.static { position: static; }

.top-0 { top: 0; }
.right-0 { right: 0; }
.bottom-0 { bottom: 0; }
.left-0 { left: 0; }

.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }

/* === 显示工具类 === */
.block { display: block; }
.inline { display: inline; }
.inline-block { display: inline-block; }
.hidden { display: none; }

.visible { visibility: visible; }
.invisible { visibility: hidden; }

/* === 溢出工具类 === */
.overflow-hidden { overflow: hidden; }
.overflow-visible { overflow: visible; }
.overflow-scroll { overflow: scroll; }
.overflow-auto { overflow: auto; }

.overflow-x-hidden { overflow-x: hidden; }
.overflow-x-visible { overflow-x: visible; }
.overflow-x-scroll { overflow-x: scroll; }
.overflow-x-auto { overflow-x: auto; }

.overflow-y-hidden { overflow-y: hidden; }
.overflow-y-visible { overflow-y: visible; }
.overflow-y-scroll { overflow-y: scroll; }
.overflow-y-auto { overflow-y: auto; }

/* === 透明度工具类 === */
.opacity-0 { opacity: 0; }
.opacity-25 { opacity: 0.25; }
.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }
.opacity-100 { opacity: 1; }

/* === Z-index工具类 === */
.z-dropdown { z-index: var(--z-dropdown); }
.z-sticky { z-index: var(--z-sticky); }
.z-fixed { z-index: var(--z-fixed); }
.z-modal-backdrop { z-index: var(--z-modal-backdrop); }
.z-modal { z-index: var(--z-modal); }
.z-popover { z-index: var(--z-popover); }
.z-tooltip { z-index: var(--z-tooltip); }
.z-toast { z-index: var(--z-toast); }

/* === 宽高工具类 === */
.w-full { width: 100%; }
.w-auto { width: auto; }
.h-full { height: 100%; }
.h-auto { height: auto; }
.h-screen { height: 100vh; }

/* === 过渡动画工具类 === */
.transition-all { transition: all var(--duration-normal) var(--easing-standard); }
.transition-fast { transition: all var(--duration-fast) var(--easing-standard); }
.transition-slow { transition: all var(--duration-slow) var(--easing-standard); }
.transition-none { transition: none; }

/* === 变换工具类 === */
.transform { transform: translateZ(0); }
.scale-95 { transform: scale(0.95); }
.scale-100 { transform: scale(1); }
.scale-105 { transform: scale(1.05); }
.scale-110 { transform: scale(1.1); }

.rotate-0 { transform: rotate(0deg); }
.rotate-90 { transform: rotate(90deg); }
.rotate-180 { transform: rotate(180deg); }
.rotate-270 { transform: rotate(270deg); }
