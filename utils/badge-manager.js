/**
 * 成就徽章管理器
 * 处理徽章解锁、进度更新、缓存管理等核心功能
 */

import auth from './auth.js'

// 徽章定义配置 - 36个高级艺术化徽章
const BADGE_DEFINITIONS = {
  // ==================== 记账成就类 (15个) ====================

  // 普通级 (3个) - 素雅之美
  'first_record': {
    id: 'first_record',
    name: '初次记账',
    description: '完成人生第一笔记账，理财之路从此开始',
    category: 'accounting',
    rarity: 'common',
    icon: 'accounting-first',
    condition: { type: 'record_count', target: 1 },
    points: 10,
    artistic: {
      materials: ['frosted-glass'],
      effects: ['soft-glow'],
      culturalElements: ['simple-border'],
      colors: ['#FFB6C1', '#FF6B6B']
    }
  },
  'small_amount': {
    id: 'small_amount',
    name: '小额记录',
    description: '记录10笔小于50元的支出，积少成多',
    category: 'accounting',
    rarity: 'common',
    icon: 'accounting-coins',
    condition: { type: 'small_amount_count', target: 10, amount: 50 },
    points: 15,
    artistic: {
      materials: ['frosted-glass'],
      effects: ['soft-glow'],
      culturalElements: ['coin-pattern'],
      colors: ['#FFB6C1', '#FF6B6B']
    }
  },
  'detailed_record': {
    id: 'detailed_record',
    name: '详细记录',
    description: '添加备注的记录达到10笔，记录更详细',
    category: 'accounting',
    rarity: 'common',
    icon: 'accounting-notes',
    condition: { type: 'detailed_record_count', target: 10 },
    points: 15,
    artistic: {
      materials: ['frosted-glass'],
      effects: ['soft-glow'],
      culturalElements: ['scroll-pattern'],
      colors: ['#FFB6C1', '#FF6B6B']
    }
  },

  // 稀有级 (5个) - 精工之美
  'category_expert': {
    id: 'category_expert',
    name: '分类达人',
    description: '使用5个不同分类，分类更清晰',
    category: 'accounting',
    rarity: 'rare',
    icon: 'accounting-categories',
    condition: { type: 'category_count', target: 5 },
    points: 25,
    artistic: {
      materials: ['polished-metal'],
      effects: ['metallic-shine', 'edge-highlight'],
      culturalElements: ['cloud-pattern'],
      colors: ['#4ECDC4', '#45B7D1']
    }
  },

  'consecutive_days': {
    id: 'consecutive_days',
    name: '连续记账',
    description: '连续记账7天，养成好习惯',
    category: 'accounting',
    rarity: 'rare',
    icon: 'accounting-calendar',
    condition: { type: 'consecutive_days', target: 7 },
    points: 30,
    artistic: {
      materials: ['polished-metal'],
      effects: ['metallic-shine', 'edge-highlight'],
      culturalElements: ['chain-pattern'],
      colors: ['#4ECDC4', '#45B7D1']
    }
  },
  'voice_expert': {
    id: 'voice_expert',
    name: '语音专家',
    description: '使用语音记账20次，解放双手',
    category: 'accounting',
    rarity: 'rare',
    icon: 'accounting-voice',
    condition: { type: 'voice_record_count', target: 20 },
    points: 30,
    artistic: {
      materials: ['polished-metal'],
      effects: ['metallic-shine', 'wave-pattern'],
      culturalElements: ['sound-waves'],
      colors: ['#4ECDC4', '#45B7D1']
    }
  },
  'data_analyst': {
    id: 'data_analyst',
    name: '数据分析师',
    description: '查看统计报表15次，数据驱动决策',
    category: 'accounting',
    rarity: 'rare',
    icon: 'accounting-chart',
    condition: { type: 'report_view_count', target: 15 },
    points: 30,
    artistic: {
      materials: ['polished-metal'],
      effects: ['metallic-shine', 'data-glow'],
      culturalElements: ['geometric-pattern'],
      colors: ['#4ECDC4', '#45B7D1']
    }
  },
  'precise_record': {
    id: 'precise_record',
    name: '精确记录',
    description: '记录精确到分的金额10次，一丝不苟',
    category: 'accounting',
    rarity: 'rare',
    icon: 'accounting-precision',
    condition: { type: 'precise_amount_count', target: 10 },
    points: 25,
    artistic: {
      materials: ['polished-metal'],
      effects: ['metallic-shine', 'precision-glow'],
      culturalElements: ['diamond-cut'],
      colors: ['#4ECDC4', '#45B7D1']
    }
  },

  // 史诗级 (5个) - 华丽之美
  'accounting_master': {
    id: 'accounting_master',
    name: '记账达人',
    description: '连续记账30天，真正的记账大师',
    category: 'accounting',
    rarity: 'epic',
    icon: 'accounting-master',
    condition: { type: 'consecutive_days', target: 30 },
    points: 60,
    artistic: {
      materials: ['gem-inlaid'],
      effects: ['multi-glow', 'particle-trail'],
      culturalElements: ['dragon-pattern', 'flame-aura'],
      colors: ['#6c5ce7', '#a29bfe']
    }
  },
  'budget_saver': {
    id: 'budget_saver',
    name: '节约小能手',
    description: '月支出低于预算80%，理财有道',
    category: 'accounting',
    rarity: 'epic',
    icon: 'accounting-saver',
    condition: { type: 'budget_usage', target: 0.8, operator: 'less' },
    points: 50,
    artistic: {
      materials: ['gem-inlaid'],
      effects: ['multi-glow', 'treasure-shine'],
      culturalElements: ['treasure-chest', 'golden-coins'],
      colors: ['#6c5ce7', '#a29bfe']
    }
  },
  'early_bird': {
    id: 'early_bird',
    name: '早起鸟',
    description: '早上8点前记账10次，勤奋如鸟',
    category: 'accounting',
    rarity: 'epic',
    icon: 'accounting-sunrise',
    condition: { type: 'early_record_count', target: 10, time: '08:00' },
    points: 45,
    artistic: {
      materials: ['gem-inlaid'],
      effects: ['multi-glow', 'sunrise-rays'],
      culturalElements: ['phoenix-pattern', 'morning-clouds'],
      colors: ['#6c5ce7', '#a29bfe']
    }
  },
  'night_owl': {
    id: 'night_owl',
    name: '夜猫子',
    description: '晚上10点后记账10次，夜深人静时',
    category: 'accounting',
    rarity: 'epic',
    icon: 'accounting-moon',
    condition: { type: 'late_record_count', target: 10, time: '22:00' },
    points: 45,
    artistic: {
      materials: ['gem-inlaid'],
      effects: ['multi-glow', 'moonlight-aura'],
      culturalElements: ['owl-pattern', 'night-stars'],
      colors: ['#6c5ce7', '#a29bfe']
    }
  },
  'budget_master': {
    id: 'budget_master',
    name: '预算大师',
    description: '连续3个月预算控制完美，理财专家',
    category: 'accounting',
    rarity: 'epic',
    icon: 'accounting-target',
    condition: { type: 'budget_perfect_months', target: 3 },
    points: 55,
    artistic: {
      materials: ['gem-inlaid'],
      effects: ['multi-glow', 'target-precision'],
      culturalElements: ['archery-target', 'precision-lines'],
      colors: ['#6c5ce7', '#a29bfe']
    }
  },

  // 传说级 (2个) - 至尊之美
  'accounting_king': {
    id: 'accounting_king',
    name: '记账之王',
    description: '记账领域的绝对王者，无人能及',
    category: 'accounting',
    rarity: 'legendary',
    icon: 'accounting-crown',
    condition: { type: 'composite', requirements: [
      { type: 'consecutive_days', target: 100 },
      { type: 'total_records', target: 1000 },
      { type: 'perfect_months', target: 6 }
    ]},
    points: 200,
    artistic: {
      materials: ['liquid-metal', 'holographic'],
      effects: ['rainbow-aura', 'particle-storm', 'crown-glow'],
      culturalElements: ['imperial-crown', 'dragon-throne'],
      colors: ['#fdcb6e', '#e17055'],
      specialUnlock: true
    }
  },
  'lightning_recorder': {
    id: 'lightning_recorder',
    name: '闪电记账',
    description: '记账速度如闪电，效率无人能比',
    category: 'accounting',
    rarity: 'legendary',
    icon: 'accounting-lightning',
    condition: { type: 'composite', requirements: [
      { type: 'quick_records', target: 50, maxTime: 10 },
      { type: 'daily_records', target: 20, days: 7 }
    ]},
    points: 150,
    artistic: {
      materials: ['liquid-metal', 'holographic'],
      effects: ['rainbow-aura', 'lightning-storm'],
      culturalElements: ['thunder-god', 'lightning-bolts'],
      colors: ['#fdcb6e', '#e17055'],
      specialUnlock: true
    }
  },

  // ==================== 旅行成就类 (15个) ====================

  // 普通级 (3个) - 素雅之美
  'travel_newbie': {
    id: 'travel_newbie',
    name: '旅行新手',
    description: '创建第一个旅行计划，开启旅行之路',
    category: 'travel',
    rarity: 'common',
    icon: 'travel-plane',
    condition: { type: 'travel_plan_count', target: 1 },
    points: 10,
    artistic: {
      materials: ['frosted-glass'],
      effects: ['soft-glow'],
      culturalElements: ['simple-compass'],
      colors: ['#FFB6C1', '#FF6B6B']
    }
  },
  'itinerary_planner': {
    id: 'itinerary_planner',
    name: '行程规划师',
    description: '创建详细行程安排，计划周全',
    category: 'travel',
    rarity: 'common',
    icon: 'travel-map',
    condition: { type: 'detailed_itinerary_count', target: 1 },
    points: 15,
    artistic: {
      materials: ['frosted-glass'],
      effects: ['soft-glow'],
      culturalElements: ['ancient-map'],
      colors: ['#FFB6C1', '#FF6B6B']
    }
  },
  'location_recorder': {
    id: 'location_recorder',
    name: '位置记录',
    description: '记录5个不同地点的支出，足迹遍布',
    category: 'travel',
    rarity: 'common',
    icon: 'travel-pin',
    condition: { type: 'unique_location_count', target: 5 },
    points: 15,
    artistic: {
      materials: ['frosted-glass'],
      effects: ['soft-glow'],
      culturalElements: ['location-marker'],
      colors: ['#FFB6C1', '#FF6B6B']
    }
  },

  // 稀有级 (5个) - 精工之美
  'budget_setter': {
    id: 'budget_setter',
    name: '预算设定',
    description: '为旅行设置合理预算，计划先行',
    category: 'travel',
    rarity: 'rare',
    icon: 'travel-budget',
    condition: { type: 'travel_budget_set', target: 3 },
    points: 25,
    artistic: {
      materials: ['polished-metal'],
      effects: ['metallic-shine', 'edge-highlight'],
      culturalElements: ['abacus-pattern'],
      colors: ['#4ECDC4', '#45B7D1']
    }
  },
  'explorer': {
    id: 'explorer',
    name: '探索者',
    description: '访问5个不同城市，世界那么大',
    category: 'travel',
    rarity: 'rare',
    icon: 'travel-globe',
    condition: { type: 'unique_destination_count', target: 5 },
    points: 35,
    artistic: {
      materials: ['polished-metal'],
      effects: ['metallic-shine', 'world-glow'],
      culturalElements: ['compass-rose'],
      colors: ['#4ECDC4', '#45B7D1']
    }
  },
  'budget_controller': {
    id: 'budget_controller',
    name: '预算控制师',
    description: '旅行支出控制在预算内，精打细算',
    category: 'travel',
    rarity: 'rare',
    icon: 'travel-wallet',
    condition: { type: 'travel_budget_control', target: 1 },
    points: 35,
    artistic: {
      materials: ['polished-metal'],
      effects: ['metallic-shine', 'control-glow'],
      culturalElements: ['scales-pattern'],
      colors: ['#4ECDC4', '#45B7D1']
    }
  },
  'memory_collector': {
    id: 'memory_collector',
    name: '回忆收集',
    description: '上传10张旅行照片，记录美好时光',
    category: 'travel',
    rarity: 'rare',
    icon: 'travel-camera',
    condition: { type: 'travel_photo_count', target: 10 },
    points: 30,
    artistic: {
      materials: ['polished-metal'],
      effects: ['metallic-shine', 'photo-flash'],
      culturalElements: ['vintage-camera'],
      colors: ['#4ECDC4', '#45B7D1']
    }
  },
  'collaboration_expert': {
    id: 'collaboration_expert',
    name: '协作专家',
    description: '使用协作功能创建计划，团队合作',
    category: 'travel',
    rarity: 'rare',
    icon: 'travel-team',
    condition: { type: 'collaboration_plan_count', target: 3 },
    points: 30,
    artistic: {
      materials: ['polished-metal'],
      effects: ['metallic-shine', 'team-bond'],
      culturalElements: ['unity-symbol'],
      colors: ['#4ECDC4', '#45B7D1']
    }
  },

  // 史诗级 (5个) - 华丽之美
  'travel_master': {
    id: 'travel_master',
    name: '旅行达人',
    description: '完成10个旅行计划，资深旅行家',
    category: 'travel',
    rarity: 'epic',
    icon: 'travel-trophy',
    condition: { type: 'completed_travel_count', target: 10 },
    points: 70,
    artistic: {
      materials: ['gem-inlaid'],
      effects: ['multi-glow', 'achievement-aura'],
      culturalElements: ['victory-laurel', 'travel-stamps'],
      colors: ['#6c5ce7', '#a29bfe']
    }
  },
  'world_traveler': {
    id: 'world_traveler',
    name: '环球旅行家',
    description: '访问15个不同目的地，足迹遍天下',
    category: 'travel',
    rarity: 'epic',
    icon: 'travel-world',
    condition: { type: 'unique_destination_count', target: 15 },
    points: 80,
    artistic: {
      materials: ['gem-inlaid'],
      effects: ['multi-glow', 'world-orbit'],
      culturalElements: ['earth-orbit', 'constellation'],
      colors: ['#6c5ce7', '#a29bfe']
    }
  },
  'money_saver': {
    id: 'money_saver',
    name: '省钱专家',
    description: '5次旅行都低于预算，理财高手',
    category: 'travel',
    rarity: 'epic',
    icon: 'travel-coins',
    condition: { type: 'budget_under_count', target: 5 },
    points: 65,
    artistic: {
      materials: ['gem-inlaid'],
      effects: ['multi-glow', 'coin-rain'],
      culturalElements: ['treasure-rain', 'golden-shower'],
      colors: ['#6c5ce7', '#a29bfe']
    }
  },
  'speed_traveler': {
    id: 'speed_traveler',
    name: '速度旅行',
    description: '快速完成旅行计划，效率至上',
    category: 'travel',
    rarity: 'epic',
    icon: 'travel-rocket',
    condition: { type: 'quick_travel_complete', target: 3, maxDays: 3 },
    points: 60,
    artistic: {
      materials: ['gem-inlaid'],
      effects: ['multi-glow', 'speed-trail'],
      culturalElements: ['rocket-flame', 'speed-lines'],
      colors: ['#6c5ce7', '#a29bfe']
    }
  },
  'secret_explorer': {
    id: 'secret_explorer',
    name: '秘境探索',
    description: '发现隐藏的旅行宝地，探险家精神',
    category: 'travel',
    rarity: 'epic',
    icon: 'travel-island',
    condition: { type: 'hidden_location_count', target: 5 },
    points: 75,
    artistic: {
      materials: ['gem-inlaid'],
      effects: ['multi-glow', 'mystery-aura'],
      culturalElements: ['treasure-island', 'mystery-fog'],
      colors: ['#6c5ce7', '#a29bfe']
    }
  },

  // 传说级 (2个) - 至尊之美
  'travel_legend': {
    id: 'travel_legend',
    name: '旅行大师',
    description: '旅行领域的传奇人物，世界之冠',
    category: 'travel',
    rarity: 'legendary',
    icon: 'travel-crown',
    condition: { type: 'composite', requirements: [
      { type: 'completed_travel_count', target: 50 },
      { type: 'unique_destination_count', target: 30 },
      { type: 'perfect_budget_count', target: 20 }
    ]},
    points: 250,
    artistic: {
      materials: ['liquid-metal', 'holographic'],
      effects: ['rainbow-aura', 'world-crown'],
      culturalElements: ['world-crown', 'travel-constellation'],
      colors: ['#fdcb6e', '#e17055'],
      specialUnlock: true
    }
  },
  'legendary_wanderer': {
    id: 'legendary_wanderer',
    name: '传奇旅者',
    description: '星辰大海的征服者，无尽旅程的王者',
    category: 'travel',
    rarity: 'legendary',
    icon: 'travel-stars',
    condition: { type: 'composite', requirements: [
      { type: 'travel_days_total', target: 365 },
      { type: 'countries_visited', target: 10 },
      { type: 'travel_memories', target: 100 }
    ]},
    points: 300,
    artistic: {
      materials: ['liquid-metal', 'holographic'],
      effects: ['rainbow-aura', 'stellar-field'],
      culturalElements: ['star-map', 'cosmic-journey'],
      colors: ['#fdcb6e', '#e17055'],
      specialUnlock: true
    }
  },

  // ==================== 使用习惯类 (4个) ====================

  // 普通级 (2个) - 素雅之美
  'new_user': {
    id: 'new_user',
    name: '新用户',
    description: '完成注册和首次设置，欢迎加入',
    category: 'usage',
    rarity: 'common',
    icon: 'usage-welcome',
    condition: { type: 'profile_complete', target: 1 },
    points: 10,
    artistic: {
      materials: ['frosted-glass'],
      effects: ['soft-glow'],
      culturalElements: ['welcome-gate'],
      colors: ['#FFB6C1', '#FF6B6B']
    }
  },
  'profile_complete': {
    id: 'profile_complete',
    name: '完善资料',
    description: '个人资料完整度100%，信息齐全',
    category: 'usage',
    rarity: 'common',
    icon: 'usage-profile',
    condition: { type: 'profile_completion', target: 100 },
    points: 15,
    artistic: {
      materials: ['frosted-glass'],
      effects: ['soft-glow'],
      culturalElements: ['profile-scroll'],
      colors: ['#FFB6C1', '#FF6B6B']
    }
  },

  // 稀有级 (2个) - 精工之美
  'loyal_user': {
    id: 'loyal_user',
    name: '忠实用户',
    description: '连续使用15天，真正的忠实用户',
    category: 'usage',
    rarity: 'rare',
    icon: 'usage-heart',
    condition: { type: 'consecutive_usage_days', target: 15 },
    points: 50,
    artistic: {
      materials: ['polished-metal'],
      effects: ['metallic-shine', 'heart-beat'],
      culturalElements: ['diamond-heart'],
      colors: ['#4ECDC4', '#45B7D1']
    }
  },
  'multi_function': {
    id: 'multi_function',
    name: '多功能用户',
    description: '使用所有主要功能，全面体验',
    category: 'usage',
    rarity: 'rare',
    icon: 'usage-grid',
    condition: { type: 'function_usage_complete', target: 1 },
    points: 45,
    artistic: {
      materials: ['polished-metal'],
      effects: ['metallic-shine', 'grid-glow'],
      culturalElements: ['app-constellation'],
      colors: ['#4ECDC4', '#45B7D1']
    }
  },

  // ==================== 特殊成就类 (2个) ====================

  // 传说级 (2个) - 至尊之美
  'perfectionist': {
    id: 'perfectionist',
    name: '完美主义者',
    description: '获得所有基础徽章，追求完美',
    category: 'special',
    rarity: 'legendary',
    icon: 'special-perfect',
    condition: { type: 'basic_badges_complete', target: 1 },
    points: 200,
    artistic: {
      materials: ['liquid-metal', 'holographic'],
      effects: ['rainbow-aura', 'perfect-star'],
      culturalElements: ['perfect-circle', 'harmony-symbol'],
      colors: ['#fdcb6e', '#e17055'],
      specialUnlock: true
    }
  },
  'legendary_user': {
    id: 'legendary_user',
    name: '传奇用户',
    description: '获得所有徽章的终极王者，至尊荣耀',
    category: 'special',
    rarity: 'legendary',
    icon: 'special-crown',
    condition: { type: 'all_badges_complete', target: 1 },
    points: 500,
    artistic: {
      materials: ['liquid-metal', 'holographic'],
      effects: ['rainbow-aura', 'supreme-crown'],
      culturalElements: ['emperor-crown', 'divine-light'],
      colors: ['#fdcb6e', '#e17055'],
      specialUnlock: true
    }
  }
}

// 徽章分类配置 - 36个徽章分布
const BADGE_CATEGORIES = {
  accounting: {
    name: '记账成就',
    color: '#FF6B6B',
    icon: 'wallet',
    count: 15,
    description: '记账相关的成就徽章'
  },
  travel: {
    name: '旅行成就',
    color: '#4ECDC4',
    icon: 'airplane',
    count: 15,
    description: '旅行规划相关的成就徽章'
  },
  usage: {
    name: '使用习惯',
    color: '#45B7D1',
    icon: 'clock',
    count: 4,
    description: '应用使用习惯相关的徽章'
  },
  special: {
    name: '特殊成就',
    color: '#6c5ce7',
    icon: 'star',
    count: 2,
    description: '特殊条件解锁的稀有徽章'
  }
}

// 稀有度配置 - 四级艺术化体系
const RARITY_CONFIG = {
  common: {
    name: '普通',
    color: '#FFB6C1',
    gradient: 'linear-gradient(135deg, #FFB6C1 0%, #FF6B6B 100%)',
    multiplier: 1,
    count: 9,
    description: '素雅之美 - 简约几何图形，单色渐变'
  },
  rare: {
    name: '稀有',
    color: '#4ECDC4',
    gradient: 'linear-gradient(135deg, #4ECDC4 0%, #45B7D1 100%)',
    multiplier: 1.5,
    count: 12,
    description: '精工之美 - 双色渐变，装饰元素'
  },
  epic: {
    name: '史诗',
    color: '#6c5ce7',
    gradient: 'linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%)',
    multiplier: 2.5,
    count: 12,
    description: '华丽之美 - 多层次设计，光效处理'
  },
  legendary: {
    name: '传说',
    color: '#fdcb6e',
    gradient: 'linear-gradient(135deg, #fdcb6e 0%, #e17055 100%)',
    multiplier: 5,
    count: 3,
    description: '至尊之美 - 极致华丽，动态效果'
  }
}

class BadgeManager {
  constructor() {
    this.userBadges = null
    this.lastSyncTime = 0
    this.pendingChecks = []
    this.initialized = false
  }

  /**
   * 初始化徽章管理器
   */
  async initialize() {
    if (this.initialized) return true

    try {
      // 加载本地缓存的徽章数据
      await this.loadLocalBadges()
      
      // 检查是否需要同步云端数据
      const shouldSync = this.shouldSyncWithCloud()
      if (shouldSync) {
        await this.syncWithCloud()
      }

      this.initialized = true
      return true
    } catch (error) {
      console.error('徽章管理器初始化失败:', error)
      return false
    }
  }

  /**
   * 加载本地徽章数据
   */
  async loadLocalBadges() {
    try {
      const cachedData = wx.getStorageSync('user_badges')
      if (cachedData && cachedData.badges) {
        this.userBadges = cachedData
        this.lastSyncTime = cachedData.lastSyncTime || 0
      } else {
        // 初始化空的徽章数据
        this.userBadges = {
          badges: {},
          statistics: {
            totalBadges: 36, // 36个高级艺术化徽章
            unlockedBadges: 0,
            completionRate: 0,
            totalPoints: 0,
            rarityStats: {
              common: 0,
              rare: 0,
              epic: 0,
              legendary: 0
            }
          },
          lastSyncTime: 0
        }
      }
    } catch (error) {
      console.error('加载本地徽章数据失败:', error)
      throw error
    }
  }

  /**
   * 检查是否需要与云端同步
   */
  shouldSyncWithCloud() {
    const now = Date.now()
    const syncInterval = 24 * 60 * 60 * 1000 // 24小时
    return (now - this.lastSyncTime) > syncInterval
  }

  /**
   * 与云端同步徽章数据
   */
  async syncWithCloud() {
    try {
      if (!auth.isLoggedIn()) {
        return false
      }

      const result = await wx.cloud.callFunction({
        name: 'badge',
        data: {
          action: 'syncUserBadges',
          lastSyncTime: this.lastSyncTime
        }
      })

      if (result.result && result.result.success) {
        const cloudData = result.result.data
        
        // 合并本地和云端数据
        this.userBadges = this.mergeLocalAndCloudData(this.userBadges, cloudData)
        
        // 更新本地缓存
        this.userBadges.lastSyncTime = Date.now()
        wx.setStorageSync('user_badges', this.userBadges)
        
        return true
      }
    } catch (error) {
      console.error('云端同步失败:', error)
      return false
    }
  }

  /**
   * 合并本地和云端数据
   */
  mergeLocalAndCloudData(localData, cloudData) {
    const merged = { ...localData }
    
    if (cloudData && cloudData.badges) {
      Object.keys(cloudData.badges).forEach(badgeId => {
        const cloudBadge = cloudData.badges[badgeId]
        const localBadge = merged.badges[badgeId] || { progress: 0, unlocked: false }
        
        merged.badges[badgeId] = {
          progress: Math.max(localBadge.progress || 0, cloudBadge.progress || 0),
          unlocked: localBadge.unlocked || cloudBadge.unlocked,
          unlockedAt: localBadge.unlockedAt || cloudBadge.unlockedAt,
          lastUpdated: Math.max(localBadge.lastUpdated || 0, cloudBadge.lastUpdated || 0)
        }
      })
      
      // 更新统计信息
      merged.statistics = cloudData.statistics || merged.statistics
    }
    
    return merged
  }

  /**
   * 检查并更新徽章进度
   */
  async checkBadgeProgress(badgeId, evidence = {}) {
    if (!this.initialized) {
      await this.initialize()
    }

    const badgeConfig = BADGE_DEFINITIONS[badgeId]
    if (!badgeConfig) {
      console.warn('未找到徽章配置:', badgeId)
      return false
    }

    const currentBadge = this.userBadges.badges[badgeId] || { progress: 0, unlocked: false }
    
    // 如果已经解锁，跳过检查
    if (currentBadge.unlocked) {
      return false
    }

    // 根据条件类型检查进度
    const newProgress = await this.calculateProgress(badgeConfig.condition, evidence)
    
    if (newProgress > currentBadge.progress) {
      // 更新进度
      this.userBadges.badges[badgeId] = {
        ...currentBadge,
        progress: newProgress,
        lastUpdated: Date.now()
      }

      // 检查是否达到解锁条件
      if (newProgress >= badgeConfig.condition.target) {
        return await this.unlockBadge(badgeId)
      }

      // 保存进度更新
      this.saveLocalBadges()
      return { progressUpdated: true, progress: newProgress, target: badgeConfig.condition.target }
    }

    return false
  }

  /**
   * 计算徽章进度
   */
  async calculateProgress(condition, evidence) {
    switch (condition.type) {
      case 'record_count':
        return evidence.recordCount || 0
      case 'consecutive_days':
        return evidence.consecutiveDays || 0
      case 'travel_plan_count':
        return evidence.travelPlanCount || 0
      case 'voice_record_count':
        return evidence.voiceRecordCount || 0
      case 'category_count':
        return evidence.categoryCount || 0
      default:
        return 0
    }
  }

  /**
   * 解锁徽章
   */
  async unlockBadge(badgeId) {
    const badgeConfig = BADGE_DEFINITIONS[badgeId]
    if (!badgeConfig) return false

    // 更新徽章状态
    this.userBadges.badges[badgeId] = {
      ...this.userBadges.badges[badgeId],
      unlocked: true,
      unlockedAt: Date.now(),
      progress: badgeConfig.condition.target || 1
    }

    // 更新统计信息
    this.userBadges.statistics.unlockedBadges += 1
    this.userBadges.statistics.completionRate =
      this.userBadges.statistics.unlockedBadges / this.userBadges.statistics.totalBadges
    this.userBadges.statistics.totalPoints += badgeConfig.points

    // 更新稀有度统计
    this.userBadges.statistics.rarityStats[badgeConfig.rarity] += 1

    // 保存到本地
    this.saveLocalBadges()

    // 同步到云端
    this.syncBadgeUnlock(badgeId)

    // 检查是否为传说级徽章，触发特殊解锁仪式
    if (badgeConfig.rarity === 'legendary' && badgeConfig.specialUnlock) {
      this.triggerLegendaryUnlockCeremony(badgeConfig)
    } else {
      // 触发普通解锁动画和通知
      this.triggerUnlockNotification(badgeConfig)
    }

    return {
      unlocked: true,
      badge: badgeConfig,
      newAchievement: true,
      isLegendary: badgeConfig.rarity === 'legendary'
    }
  }

  /**
   * 保存本地徽章数据
   */
  saveLocalBadges() {
    try {
      wx.setStorageSync('user_badges', this.userBadges)
    } catch (error) {
      console.error('保存本地徽章数据失败:', error)
    }
  }

  /**
   * 同步徽章解锁到云端
   */
  async syncBadgeUnlock(badgeId) {
    try {
      if (!auth.isLoggedIn()) return

      await wx.cloud.callFunction({
        name: 'badge',
        data: {
          action: 'unlockBadge',
          badgeId: badgeId,
          unlockedAt: this.userBadges.badges[badgeId].unlockedAt
        }
      })
    } catch (error) {
      console.error('同步徽章解锁失败:', error)
      // 添加到待同步队列
      this.addToPendingSync(badgeId)
    }
  }

  /**
   * 添加到待同步队列
   */
  addToPendingSync(badgeId) {
    const pending = wx.getStorageSync('pending_badge_sync') || []
    pending.push({
      badgeId,
      unlockedAt: this.userBadges.badges[badgeId].unlockedAt,
      timestamp: Date.now()
    })
    wx.setStorageSync('pending_badge_sync', pending)
  }

  /**
   * 触发解锁通知
   */
  triggerUnlockNotification(badgeConfig) {
    // 显示解锁提示
    wx.showToast({
      title: `获得徽章: ${badgeConfig.name}`,
      icon: 'success',
      duration: 2000
    })

    // 触发触觉反馈
    wx.vibrateShort()

    // 发送页面事件
    wx.eventBus && wx.eventBus.emit('badgeUnlocked', {
      badge: badgeConfig,
      timestamp: Date.now(),
      isLegendary: false
    })
  }

  /**
   * 触发传说级解锁仪式
   */
  triggerLegendaryUnlockCeremony(badgeConfig) {
    // 强烈触觉反馈
    wx.vibrateLong()

    // 计算稀有度百分比
    const totalUsers = 10000 // 假设总用户数，实际应从服务端获取
    const legendaryUsers = Math.floor(totalUsers * 0.01) // 1%的用户拥有传说级徽章
    const percentage = ((legendaryUsers / totalUsers) * 100).toFixed(1)

    // 发送传说级解锁事件
    wx.eventBus && wx.eventBus.emit('legendaryBadgeUnlocked', {
      badge: badgeConfig,
      timestamp: Date.now(),
      isLegendary: true,
      rarity: {
        percentage: percentage,
        rank: 'legendary'
      },
      ceremony: {
        duration: 5000, // 5秒仪式
        effects: ['screen_flash', 'particle_explosion', 'confetti_rain'],
        audio: 'legendary_fanfare'
      }
    })

    // 延迟显示成功提示
    setTimeout(() => {
      wx.showToast({
        title: `传说成就: ${badgeConfig.name}`,
        icon: 'success',
        duration: 3000
      })
    }, 2000)
  }

  /**
   * 获取用户徽章数据
   */
  getUserBadges() {
    return this.userBadges
  }

  /**
   * 获取徽章定义
   */
  getBadgeDefinitions() {
    return BADGE_DEFINITIONS
  }

  /**
   * 获取分类配置
   */
  getCategoryConfig() {
    return BADGE_CATEGORIES
  }

  /**
   * 获取稀有度配置
   */
  getRarityConfig() {
    return RARITY_CONFIG
  }

  /**
   * 批量检查徽章
   */
  async batchCheckBadges(evidenceMap) {
    const results = []
    
    for (const [badgeId, evidence] of Object.entries(evidenceMap)) {
      try {
        const result = await this.checkBadgeProgress(badgeId, evidence)
        if (result) {
          results.push({ badgeId, result })
        }
      } catch (error) {
        console.error(`检查徽章 ${badgeId} 失败:`, error)
      }
    }
    
    return results
  }
}

export default new BadgeManager()
