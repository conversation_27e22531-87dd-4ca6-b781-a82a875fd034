/**
 * 数据缓存管理器
 * 提供内存缓存、TTL支持、版本控制等功能
 * 用于优化小程序性能，减少重复的云函数调用
 */

class CacheManager {
  constructor() {
    // 内存缓存存储
    this.cache = new Map()

    // 缓存配置
    this.config = {
      // 默认TTL: 10分钟（增加缓存时间）
      defaultTTL: 10 * 60 * 1000,
      // 最大缓存条目数
      maxSize: 200,
      // 清理间隔: 15分钟
      cleanupInterval: 15 * 60 * 1000
    }

    // 定时器引用
    this.cleanupTimer = null

    // 启动定期清理
    this.startCleanupTimer()
  }

  /**
   * 设置缓存
   * @param {string} key 缓存键
   * @param {any} data 缓存数据
   * @param {number} ttl 生存时间(毫秒)，默认使用配置值
   * @param {string} version 数据版本，用于缓存失效
   */
  set(key, data, ttl = this.config.defaultTTL, version = '1.0') {
    try {
      // 检查缓存大小限制
      if (this.cache.size >= this.config.maxSize) {
        this.evictOldest()
      }

      const cacheItem = {
        data: data,
        timestamp: Date.now(),
        ttl: ttl,
        version: version,
        accessCount: 0,
        lastAccess: Date.now()
      }

      this.cache.set(key, cacheItem)
      return true
    } catch (error) {
      console.error('缓存设置失败:', error)
      return false
    }
  }

  /**
   * 获取缓存
   * @param {string} key 缓存键
   * @param {string} version 期望的数据版本
   * @returns {any|null} 缓存数据或null
   */
  get(key, version = null) {
    try {
      const cacheItem = this.cache.get(key)
      
      if (!cacheItem) {
        return null
      }

      // 检查TTL
      if (this.isExpired(cacheItem)) {
        this.cache.delete(key)
        return null
      }

      // 检查版本
      if (version && cacheItem.version !== version) {
        this.cache.delete(key)
        return null
      }

      // 更新访问统计
      cacheItem.accessCount++
      cacheItem.lastAccess = Date.now()

      return cacheItem.data
    } catch (error) {
      console.error('缓存获取失败:', error)
      return null
    }
  }

  /**
   * 检查缓存是否存在且有效
   * @param {string} key 缓存键
   * @param {string} version 期望的数据版本
   * @returns {boolean}
   */
  has(key, version = null) {
    const cacheItem = this.cache.get(key)
    
    if (!cacheItem || this.isExpired(cacheItem)) {
      return false
    }

    if (version && cacheItem.version !== version) {
      return false
    }

    return true
  }

  /**
   * 删除指定缓存
   * @param {string} key 缓存键
   */
  delete(key) {
    return this.cache.delete(key)
  }

  /**
   * 清空所有缓存
   */
  clear() {
    this.cache.clear()
  }

  /**
   * 使指定前缀的缓存失效
   * @param {string} prefix 缓存键前缀
   */
  invalidateByPrefix(prefix) {
    const keysToDelete = []
    
    for (const key of this.cache.keys()) {
      if (key.startsWith(prefix)) {
        keysToDelete.push(key)
      }
    }
    
    keysToDelete.forEach(key => this.cache.delete(key))
    return keysToDelete.length
  }

  /**
   * 检查缓存项是否过期
   * @param {object} cacheItem 缓存项
   * @returns {boolean}
   */
  isExpired(cacheItem) {
    return Date.now() - cacheItem.timestamp > cacheItem.ttl
  }

  /**
   * 移除最旧的缓存项
   */
  evictOldest() {
    let oldestKey = null
    let oldestTime = Date.now()

    for (const [key, item] of this.cache.entries()) {
      if (item.lastAccess < oldestTime) {
        oldestTime = item.lastAccess
        oldestKey = key
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey)
    }
  }

  /**
   * 启动定期清理定时器
   */
  startCleanupTimer() {
    // 保存定时器引用以便清理
    this.cleanupTimer = setInterval(() => {
      this.cleanup()
    }, this.config.cleanupInterval)
  }

  /**
   * 停止清理定时器
   */
  stopCleanupTimer() {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer)
      this.cleanupTimer = null
    }
  }

  /**
   * 销毁缓存管理器
   */
  destroy() {
    this.stopCleanupTimer()
    this.cache.clear()
    console.log('缓存管理器已销毁')
  }

  /**
   * 清理过期缓存
   */
  cleanup() {
    const keysToDelete = []
    
    for (const [key, item] of this.cache.entries()) {
      if (this.isExpired(item)) {
        keysToDelete.push(key)
      }
    }
    
    keysToDelete.forEach(key => this.cache.delete(key))
    
    if (keysToDelete.length > 0) {
    }
  }

  /**
   * 获取缓存统计信息
   * @returns {object} 统计信息
   */
  getStats() {
    let totalSize = 0
    let expiredCount = 0
    
    for (const [key, item] of this.cache.entries()) {
      totalSize++
      if (this.isExpired(item)) {
        expiredCount++
      }
    }

    return {
      totalSize,
      expiredCount,
      hitRate: this.calculateHitRate(),
      maxSize: this.config.maxSize
    }
  }

  /**
   * 计算缓存命中率（简化版）
   * @returns {number} 命中率百分比
   */
  calculateHitRate() {
    // 这里可以实现更复杂的命中率计算逻辑
    return 0
  }

  /**
   * 预热缓存 - 批量设置常用数据
   * @param {object} dataMap 数据映射 {key: {data, ttl, version}}
   */
  warmup(dataMap) {
    for (const [key, config] of Object.entries(dataMap)) {
      this.set(key, config.data, config.ttl, config.version)
    }
  }

  /**
   * 智能预加载 - 根据使用模式预加载数据
   */
  async preload(preloadFunctions) {
    const promises = []

    for (const [key, loadFn] of Object.entries(preloadFunctions)) {
      // 只预加载不存在或即将过期的数据
      if (!this.has(key) || this.isExpiringSoon(key)) {
        promises.push(
          loadFn().then(data => {
            if (data) {
              this.set(key, data, this.config.defaultTTL * 2) // 预加载数据缓存更久
            }
          }).catch(() => {
            // 静默处理预加载失败
          })
        )
      }
    }

    // 并行执行所有预加载
    await Promise.allSettled(promises)
  }

  /**
   * 检查缓存是否即将过期（剩余时间少于1/4）
   */
  isExpiringSoon(key) {
    const item = this.cache.get(key)
    if (!item) return true

    const age = Date.now() - item.timestamp
    const remainingTime = item.ttl - age
    return remainingTime < item.ttl * 0.25
  }
}

// 创建全局缓存管理器实例
const cacheManager = new CacheManager()

// 预定义的缓存键
export const CACHE_KEYS = {
  // 财务数据
  FINANCIAL_OVERVIEW: 'financial_overview',
  USER_BUDGET: 'user_budget',
  EXPENSE_RECORDS: 'expense_records',
  
  // 旅行数据
  TRAVEL_STATISTICS: 'travel_statistics',
  TRAVEL_PLANS: 'travel_plans',
  CURRENT_PLAN: 'current_plan',
  ONGOING_PLANS: 'ongoing_plans',
  
  // 用户数据
  USER_INFO: 'user_info',
  USER_STATS: 'user_stats',
  
  // 其他
  DESTINATIONS: 'destinations',
  CATEGORIES: 'categories'
}

// 缓存TTL配置（毫秒）- 优化为更长的缓存时间
export const CACHE_TTL = {
  SHORT: 5 * 60 * 1000,      // 5分钟 - 用于频繁变化的数据
  MEDIUM: 10 * 60 * 1000,    // 10分钟 - 用于一般数据
  LONG: 30 * 60 * 1000,      // 30分钟 - 用于相对稳定的数据
  VERY_LONG: 2 * 60 * 60 * 1000  // 2小时 - 用于很少变化的数据
}

export default cacheManager
