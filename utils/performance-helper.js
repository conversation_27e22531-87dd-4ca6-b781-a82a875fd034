/**
 * 性能优化工具
 * 提供批量setData、防抖、性能监控等功能
 */

class PerformanceHelper {
  constructor() {
    // 批量setData队列
    this.setDataQueue = new Map()
    
    // 防抖定时器
    this.debounceTimers = new Map()
    
    // 性能监控数据
    this.performanceData = {
      setDataCalls: 0,
      batchedCalls: 0,
      totalDataSize: 0
    }
  }

  /**
   * 批量setData - 将多个setData调用合并为一次
   * @param {object} pageInstance 页面实例
   * @param {object} data 要设置的数据
   * @param {function} callback 回调函数
   * @param {number} delay 延迟时间(ms)，默认16ms(一帧)
   */
  batchSetData(pageInstance, data, callback = null, delay = 16) {
    if (!pageInstance || !pageInstance.setData) {
      console.error('无效的页面实例')
      return
    }

    const pageId = this.getPageId(pageInstance)
    
    // 合并数据到队列
    if (!this.setDataQueue.has(pageId)) {
      this.setDataQueue.set(pageId, {
        pageInstance,
        data: {},
        callbacks: []
      })
    }

    const queueItem = this.setDataQueue.get(pageId)
    Object.assign(queueItem.data, data)
    
    if (callback) {
      queueItem.callbacks.push(callback)
    }

    // 清除之前的定时器
    if (this.debounceTimers.has(pageId)) {
      clearTimeout(this.debounceTimers.get(pageId))
    }

    // 设置新的定时器
    const timer = setTimeout(() => {
      this.flushSetData(pageId)
    }, delay)
    
    this.debounceTimers.set(pageId, timer)
  }

  /**
   * 立即执行批量setData
   * @param {string} pageId 页面ID
   */
  flushSetData(pageId) {
    const queueItem = this.setDataQueue.get(pageId)
    
    if (!queueItem || Object.keys(queueItem.data).length === 0) {
      return
    }

    try {
      // 执行setData
      queueItem.pageInstance.setData(queueItem.data, () => {
        // 执行所有回调
        queueItem.callbacks.forEach(callback => {
          try {
            callback()
          } catch (error) {
            console.error('setData回调执行失败:', error)
          }
        })
      })

      // 更新性能统计
      this.performanceData.setDataCalls++
      this.performanceData.batchedCalls++
      this.performanceData.totalDataSize += this.calculateDataSize(queueItem.data)

    } catch (error) {
      console.error('批量setData执行失败:', error)
    } finally {
      // 清理队列
      this.setDataQueue.delete(pageId)
      this.debounceTimers.delete(pageId)
    }
  }

  /**
   * 防抖函数
   * @param {function} func 要防抖的函数
   * @param {number} delay 延迟时间(ms)
   * @param {string} key 防抖键，用于区分不同的防抖函数
   * @returns {function} 防抖后的函数
   */
  debounce(func, delay = 300, key = 'default') {
    return (...args) => {
      // 清除之前的定时器
      if (this.debounceTimers.has(key)) {
        clearTimeout(this.debounceTimers.get(key))
      }

      // 设置新的定时器
      const timer = setTimeout(() => {
        func.apply(this, args)
        this.debounceTimers.delete(key)
      }, delay)
      
      this.debounceTimers.set(key, timer)
    }
  }

  /**
   * 节流函数
   * @param {function} func 要节流的函数
   * @param {number} delay 节流间隔(ms)
   * @param {string} key 节流键
   * @returns {function} 节流后的函数
   */
  throttle(func, delay = 300, key = 'default') {
    const throttleKey = `throttle_${key}`
    let lastExecTime = 0

    return (...args) => {
      const now = Date.now()
      
      if (now - lastExecTime >= delay) {
        lastExecTime = now
        func.apply(this, args)
      }
    }
  }

  /**
   * 性能监控 - 测量函数执行时间
   * @param {function} func 要监控的函数
   * @param {string} name 函数名称
   * @returns {function} 包装后的函数
   */
  monitor(func, name = 'anonymous') {
    return async (...args) => {
      const startTime = Date.now()
      
      try {
        const result = await func.apply(this, args)
        const endTime = Date.now()
        const duration = endTime - startTime
        
        // 记录性能数据
        
        // 如果执行时间过长，发出警告
        if (duration > 1000) {
        }
        
        return result
      } catch (error) {
        const endTime = Date.now()
        const duration = endTime - startTime
        console.error(`[性能监控] ${name} 执行失败 (${duration}ms):`, error)
        throw error
      }
    }
  }

  /**
   * 获取页面唯一ID
   * @param {object} pageInstance 页面实例
   * @returns {string} 页面ID
   */
  getPageId(pageInstance) {
    // 尝试从页面实例获取唯一标识
    if (pageInstance.__pageId) {
      return pageInstance.__pageId
    }
    
    // 生成唯一ID并缓存
    const pageId = `page_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    pageInstance.__pageId = pageId
    return pageId
  }

  /**
   * 计算数据大小（简化版）
   * @param {object} data 数据对象
   * @returns {number} 数据大小（字节）
   */
  calculateDataSize(data) {
    try {
      return JSON.stringify(data).length
    } catch (error) {
      return 0
    }
  }

  /**
   * 获取性能统计信息
   * @returns {object} 性能统计
   */
  getPerformanceStats() {
    return {
      ...this.performanceData,
      averageDataSize: this.performanceData.batchedCalls > 0 
        ? Math.round(this.performanceData.totalDataSize / this.performanceData.batchedCalls)
        : 0,
      queueSize: this.setDataQueue.size,
      activeTimers: this.debounceTimers.size
    }
  }

  /**
   * 重置性能统计
   */
  resetStats() {
    this.performanceData = {
      setDataCalls: 0,
      batchedCalls: 0,
      totalDataSize: 0
    }
  }

  /**
   * 清理所有定时器和队列
   */
  cleanup() {
    // 清理所有定时器
    for (const timer of this.debounceTimers.values()) {
      clearTimeout(timer)
    }
    this.debounceTimers.clear()

    // 清理队列
    this.setDataQueue.clear()
  }

  /**
   * 页面卸载时的清理方法
   * @param {object} pageInstance 页面实例
   */
  cleanupPage(pageInstance) {
    const pageId = this.getPageId(pageInstance)
    
    // 立即执行剩余的setData
    this.flushSetData(pageId)
    
    // 清理定时器
    if (this.debounceTimers.has(pageId)) {
      clearTimeout(this.debounceTimers.get(pageId))
      this.debounceTimers.delete(pageId)
    }
  }

  /**
   * 智能setData - 只更新变化的数据
   * @param {object} pageInstance 页面实例
   * @param {object} newData 新数据
   * @param {function} callback 回调函数
   */
  smartSetData(pageInstance, newData, callback = null) {
    if (!pageInstance || !pageInstance.data) {
      console.error('无效的页面实例')
      return
    }

    const changedData = {}
    let hasChanges = false

    // 比较数据变化
    for (const [key, value] of Object.entries(newData)) {
      if (this.isDataChanged(pageInstance.data[key], value)) {
        changedData[key] = value
        hasChanges = true
      }
    }

    // 只有数据变化时才调用setData
    if (hasChanges) {
      this.batchSetData(pageInstance, changedData, callback)
    } else if (callback) {
      // 即使没有变化也要执行回调
      callback()
    }
  }

  /**
   * 检查数据是否发生变化
   * @param {any} oldValue 旧值
   * @param {any} newValue 新值
   * @returns {boolean} 是否变化
   */
  isDataChanged(oldValue, newValue) {
    // 简单的深度比较（可以根据需要优化）
    try {
      return JSON.stringify(oldValue) !== JSON.stringify(newValue)
    } catch (error) {
      // 如果无法序列化，认为数据发生了变化
      return true
    }
  }
}

// 创建全局性能助手实例
const performanceHelper = new PerformanceHelper()

export default performanceHelper
