<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 记账类史诗级渐变 - 终极升级版 -->
    <linearGradient id="accountingEpic" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#D4AF37" />
      <stop offset="15%" style="stop-color:#FFD700" />
      <stop offset="30%" style="stop-color:#FFA500" />
      <stop offset="45%" style="stop-color:#FF8C00" />
      <stop offset="60%" style="stop-color:#B8860B" />
      <stop offset="75%" style="stop-color:#FFD700" />
      <stop offset="90%" style="stop-color:#FFA500" />
      <stop offset="100%" style="stop-color:#D4AF37" />
    </linearGradient>
    
    <!-- 达人光环渐变 -->
    <linearGradient id="masterAura" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF6347" opacity="0.8" />
      <stop offset="20%" style="stop-color:#FFD700" opacity="0.8" />
      <stop offset="40%" style="stop-color:#32CD32" opacity="0.8" />
      <stop offset="60%" style="stop-color:#1E90FF" opacity="0.8" />
      <stop offset="80%" style="stop-color:#9370DB" opacity="0.8" />
      <stop offset="100%" style="stop-color:#FF6347" opacity="0.8" />
    </linearGradient>
    
    <!-- 宝石镶嵌渐变 -->
    <radialGradient id="gemInlay" cx="30%" cy="30%" r="70%">
      <stop offset="0%" style="stop-color:#FFFFFF" />
      <stop offset="30%" style="stop-color:#FFD700" />
      <stop offset="70%" style="stop-color:#B8860B" />
      <stop offset="100%" style="stop-color:#8B4513" />
    </radialGradient>
    
    <!-- 达人徽章渐变 -->
    <radialGradient id="masterBadge" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#FFFFFF" />
      <stop offset="30%" style="stop-color:#FFD700" />
      <stop offset="70%" style="stop-color:#FF8C00" />
      <stop offset="100%" style="stop-color:#D2691E" />
    </radialGradient>
    
    <!-- 技能光环渐变 -->
    <linearGradient id="skillAura" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF4500" />
      <stop offset="50%" style="stop-color:#FFD700" />
      <stop offset="100%" style="stop-color:#FF6347" />
    </linearGradient>
  </defs>
  
  <!-- 发光效果 - 多层模拟 -->
  <circle cx="64" cy="64" r="54" fill="url(#accountingEpic)" opacity="0.1"/>
  <circle cx="64" cy="64" r="52" fill="url(#accountingEpic)" opacity="0.15"/>
  <circle cx="64" cy="64" r="50" fill="url(#accountingEpic)" opacity="0.2"/>
  
  <!-- 最外层彩虹光环 -->
  <circle cx="64" cy="64" r="62" fill="none" stroke="url(#masterAura)" stroke-width="3" opacity="0.7">
    <animate attributeName="r" values="58;68;58" dur="3s" repeatCount="indefinite"/>
    <animate attributeName="stroke-width" values="2;5;2" dur="3s" repeatCount="indefinite"/>
  </circle>
  
  <!-- 中层动态光环 -->
  <circle cx="64" cy="64" r="58" fill="none" stroke="url(#accountingEpic)" stroke-width="2" opacity="0.8">
    <animate attributeName="opacity" values="0.6;1;0.6" dur="2s" repeatCount="indefinite"/>
  </circle>
  
  <!-- 内层装饰光环 -->
  <circle cx="64" cy="64" r="54" fill="none" stroke="#fff" stroke-width="1" opacity="0.4"/>
  
  <!-- 主体背景 -->
  <circle cx="64" cy="64" r="48" fill="url(#accountingEpic)"/>
  
  <!-- 内圈装饰 -->
  <circle cx="64" cy="64" r="40" fill="none" stroke="#fff" stroke-width="1.8" opacity="0.7"/>
  <circle cx="64" cy="64" r="34" fill="none" stroke="#fff" stroke-width="1" opacity="0.5"/>
  
  <!-- 达人徽章图案 - 史诗级设计 -->
  <g transform="translate(64, 64)">
    <!-- 中央达人徽章发光底层 -->
    <circle cx="0" cy="0" r="22" fill="url(#masterBadge)" opacity="0.3">
      <animate attributeName="r" values="20;24;20" dur="4s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.2;0.5;0.2" dur="4s" repeatCount="indefinite"/>
    </circle>
    <circle cx="0" cy="0" r="20" fill="url(#masterBadge)" opacity="0.5"/>
    
    <!-- 达人徽章主体 -->
    <circle cx="0" cy="0" r="18" fill="url(#masterBadge)"/>
    
    <!-- 达人徽章高光 -->
    <ellipse cx="-6" cy="-6" rx="8" ry="6" fill="#FFFFFF" opacity="0.4"/>
    
    <!-- 达人等级标识 -->
    <g transform="translate(0, -2)">
      <!-- 等级背景 -->
      <rect x="-12" y="-6" width="24" height="12" rx="6" fill="url(#skillAura)" opacity="0.9"/>
      
      <!-- 等级文字 -->
      <text x="0" y="2" text-anchor="middle" fill="#fff" font-family="Arial, sans-serif" font-size="10" font-weight="bold">MASTER</text>
    </g>
    
    <!-- 技能星级显示 -->
    <g transform="translate(0, 12)" opacity="0.9">
      <!-- 五星评级 -->
      <g fill="#FFD700">
        <path d="M-12,-2 L-11,-1 L-10,-2 L-11,0 L-12,0 Z">
          <animate attributeName="opacity" values="0.6;1;0.6" dur="1s" repeatCount="indefinite"/>
        </path>
        <path d="M-6,-2 L-5,-1 L-4,-2 L-5,0 L-6,0 Z">
          <animate attributeName="opacity" values="0.6;1;0.6" dur="1.2s" repeatCount="indefinite"/>
        </path>
        <path d="M0,-2 L1,-1 L2,-2 L1,0 L0,0 Z">
          <animate attributeName="opacity" values="0.6;1;0.6" dur="1.4s" repeatCount="indefinite"/>
        </path>
        <path d="M6,-2 L7,-1 L8,-2 L7,0 L6,0 Z">
          <animate attributeName="opacity" values="0.6;1;0.6" dur="1.6s" repeatCount="indefinite"/>
        </path>
        <path d="M12,-2 L13,-1 L14,-2 L13,0 L12,0 Z">
          <animate attributeName="opacity" values="0.6;1;0.6" dur="1.8s" repeatCount="indefinite"/>
        </path>
      </g>
    </g>
    
    <!-- 技能图标环绕 -->
    <g opacity="0.8">
      <!-- 记账技能图标 -->
      <g transform="translate(-20, -8)">
        <circle cx="0" cy="0" r="4" fill="url(#gemInlay)" opacity="0.3">
          <animate attributeName="opacity" values="0.2;0.5;0.2" dur="2s" repeatCount="indefinite"/>
        </circle>
        <circle cx="0" cy="0" r="3" fill="url(#gemInlay)"/>
        <text x="0" y="1" text-anchor="middle" fill="#fff" font-family="Arial, sans-serif" font-size="4" font-weight="bold">¥</text>
      </g>
      
      <!-- 分析技能图标 -->
      <g transform="translate(20, -8)">
        <circle cx="0" cy="0" r="4" fill="url(#gemInlay)" opacity="0.3">
          <animate attributeName="opacity" values="0.2;0.5;0.2" dur="2.2s" repeatCount="indefinite"/>
        </circle>
        <circle cx="0" cy="0" r="3" fill="url(#gemInlay)"/>
        <rect x="-1.5" y="-1" width="1" height="2" fill="#fff"/>
        <rect x="-0.5" y="-0.5" width="1" height="1.5" fill="#fff"/>
        <rect x="0.5" y="-1.5" width="1" height="3" fill="#fff"/>
      </g>
      
      <!-- 管理技能图标 -->
      <g transform="translate(-8, 18)">
        <circle cx="0" cy="0" r="4" fill="url(#gemInlay)" opacity="0.3">
          <animate attributeName="opacity" values="0.2;0.5;0.2" dur="2.4s" repeatCount="indefinite"/>
        </circle>
        <circle cx="0" cy="0" r="3" fill="url(#gemInlay)"/>
        <rect x="-1.5" y="-1.5" width="3" height="1" fill="#fff"/>
        <rect x="-1.5" y="-0.5" width="3" height="1" fill="#fff"/>
        <rect x="-1.5" y="0.5" width="3" height="1" fill="#fff"/>
      </g>
      
      <!-- 优化技能图标 -->
      <g transform="translate(8, 18)">
        <circle cx="0" cy="0" r="4" fill="url(#gemInlay)" opacity="0.3">
          <animate attributeName="opacity" values="0.2;0.5;0.2" dur="2.6s" repeatCount="indefinite"/>
        </circle>
        <circle cx="0" cy="0" r="3" fill="url(#gemInlay)"/>
        <path d="M-1,-1 L1,1 M-1,1 L1,-1" stroke="#fff" stroke-width="0.8"/>
      </g>
    </g>
    
    <!-- 成就光芒 -->
    <g opacity="0.7">
      <line x1="0" y1="-30" x2="0" y2="-35" stroke="#FFD700" stroke-width="2">
        <animate attributeName="opacity" values="0.5;1;0.5" dur="2s" repeatCount="indefinite"/>
      </line>
      <line x1="-4" y1="-28" x2="-8" y2="-32" stroke="#FF8C00" stroke-width="1.5">
        <animate attributeName="opacity" values="0.4;0.9;0.4" dur="2.2s" repeatCount="indefinite"/>
      </line>
      <line x1="4" y1="-28" x2="8" y2="-32" stroke="#FF8C00" stroke-width="1.5">
        <animate attributeName="opacity" values="0.4;0.9;0.4" dur="1.8s" repeatCount="indefinite"/>
      </line>
      <line x1="-6" y1="-26" x2="-12" y2="-30" stroke="#FF6347" stroke-width="1">
        <animate attributeName="opacity" values="0.3;0.8;0.3" dur="2.4s" repeatCount="indefinite"/>
      </line>
      <line x1="6" y1="-26" x2="12" y2="-30" stroke="#FF6347" stroke-width="1">
        <animate attributeName="opacity" values="0.3;0.8;0.3" dur="2.6s" repeatCount="indefinite"/>
      </line>
    </g>
    
    <!-- 经验值进度条 -->
    <g transform="translate(0, 26)" opacity="0.9">
      <rect x="-18" y="0" width="36" height="4" rx="2" fill="#000" opacity="0.3"/>
      <rect x="-17" y="1" width="30" height="2" rx="1" fill="url(#skillAura)">
        <animate attributeName="width" values="25;34;30" dur="3s" repeatCount="indefinite"/>
      </rect>
      <text x="0" y="7" text-anchor="middle" fill="#8B4513" font-family="Arial, sans-serif" font-size="5" font-weight="bold">EXP: 9999/10000</text>
    </g>
  </g>
  
  <!-- 粒子效果 - 增强版 -->
  <g opacity="0.8">
    <circle cx="20" cy="20" r="2" fill="#FFD700" opacity="0.3">
      <animate attributeName="cy" values="20;15;20" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;0.8;0" dur="3s" repeatCount="indefinite"/>
    </circle>
    <circle cx="20" cy="20" r="1.5" fill="#FFD700">
      <animate attributeName="cy" values="20;15;20" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;0" dur="3s" repeatCount="indefinite"/>
    </circle>
    
    <circle cx="108" cy="30" r="1.8" fill="#FF8C00" opacity="0.3">
      <animate attributeName="cx" values="108;103;108" dur="2.5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;0.7;0" dur="2.5s" repeatCount="indefinite"/>
    </circle>
    <circle cx="108" cy="30" r="1.2" fill="#FF8C00">
      <animate attributeName="cx" values="108;103;108" dur="2.5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;0" dur="2.5s" repeatCount="indefinite"/>
    </circle>
    
    <circle cx="15" cy="100" r="2.2" fill="#FF6347" opacity="0.3">
      <animate attributeName="cy" values="100;105;100" dur="2.8s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;0.6;0" dur="2.8s" repeatCount="indefinite"/>
    </circle>
    <circle cx="15" cy="100" r="1.6" fill="#FF6347">
      <animate attributeName="cy" values="100;105;100" dur="2.8s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;0" dur="2.8s" repeatCount="indefinite"/>
    </circle>
    
    <circle cx="110" cy="95" r="1.5" fill="#9370DB" opacity="0.3">
      <animate attributeName="cx" values="110;115;110" dur="3.2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;0.5;0" dur="3.2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="110" cy="95" r="1" fill="#9370DB">
      <animate attributeName="cx" values="110;115;110" dur="3.2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;0" dur="3.2s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- 底部华丽标识 -->
  <circle cx="64" cy="115" r="12" fill="none" stroke="url(#accountingEpic)" stroke-width="3" opacity="0.8"/>
  <circle cx="64" cy="115" r="8" fill="none" stroke="#FFD700" stroke-width="2" opacity="0.9"/>
  <text x="64" y="119" text-anchor="middle" fill="#8B4513" font-family="Arial, sans-serif" font-size="9" font-weight="bold">达人</text>
</svg>
