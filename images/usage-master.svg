<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 使用类史诗级渐变 -->
    <linearGradient id="usageEpic" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#7B1FA2" />
      <stop offset="20%" style="stop-color:#6A1B9A" />
      <stop offset="40%" style="stop-color:#4A148C" />
      <stop offset="60%" style="stop-color:#38006B" />
      <stop offset="80%" style="stop-color:#2E0054" />
      <stop offset="100%" style="stop-color:#1A0033" />
    </linearGradient>
    
    <!-- 大师徽章渐变 -->
    <radialGradient id="masterBadge" cx="30%" cy="30%" r="70%">
      <stop offset="0%" style="stop-color:#FFFFFF" />
      <stop offset="30%" style="stop-color:#FFD700" />
      <stop offset="70%" style="stop-color:#FF9800" />
      <stop offset="100%" style="stop-color:#E65100" />
    </radialGradient>
    
    <!-- 技能树渐变 -->
    <linearGradient id="skillGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4CAF50" />
      <stop offset="100%" style="stop-color:#2E7D32" />
    </linearGradient>
    
    <!-- 能量渐变 -->
    <radialGradient id="energyGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#E91E63" />
      <stop offset="100%" style="stop-color:#AD1457" />
    </radialGradient>
    
    <!-- 光环渐变 -->
    <linearGradient id="auraGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#9C27B0" />
      <stop offset="100%" style="stop-color:#E91E63" />
    </linearGradient>
  </defs>
  
  <!-- 发光效果 - 多层模拟 -->
  <circle cx="64" cy="64" r="54" fill="url(#usageEpic)" opacity="0.1"/>
  <circle cx="64" cy="64" r="52" fill="url(#usageEpic)" opacity="0.15"/>
  <circle cx="64" cy="64" r="50" fill="url(#usageEpic)" opacity="0.2"/>
  
  <!-- 外层动态光环 -->
  <circle cx="64" cy="64" r="62" fill="none" stroke="url(#usageEpic)" stroke-width="1" opacity="0.3">
    <animate attributeName="r" values="60;66;60" dur="4s" repeatCount="indefinite"/>
    <animate attributeName="opacity" values="0.2;0.5;0.2" dur="4s" repeatCount="indefinite"/>
  </circle>
  
  <!-- 多层装饰边框 -->
  <circle cx="64" cy="64" r="58" fill="none" stroke="url(#usageEpic)" stroke-width="2" opacity="0.6"/>
  <circle cx="64" cy="64" r="54" fill="none" stroke="#fff" stroke-width="1" opacity="0.4"/>
  
  <!-- 主体背景 -->
  <circle cx="64" cy="64" r="48" fill="url(#usageEpic)"/>
  
  <!-- 内圈装饰 -->
  <circle cx="64" cy="64" r="40" fill="none" stroke="#fff" stroke-width="1.8" opacity="0.7"/>
  <circle cx="64" cy="64" r="34" fill="none" stroke="#fff" stroke-width="1" opacity="0.5"/>
  
  <!-- 大师图案 - 史诗级设计 -->
  <g transform="translate(64, 64)">
    <!-- 中央大师徽章 - 多层发光 -->
    <circle cx="0" cy="0" r="18" fill="url(#masterBadge)" opacity="0.3">
      <animate attributeName="r" values="16;20;16" dur="4s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.2;0.4;0.2" dur="4s" repeatCount="indefinite"/>
    </circle>
    <circle cx="0" cy="0" r="16" fill="url(#masterBadge)" opacity="0.5"/>
    <circle cx="0" cy="0" r="14" fill="url(#masterBadge)"/>
    
    <!-- 大师标识 -->
    <g transform="translate(0, 0)" opacity="0.9">
      <rect x="-8" y="-3" width="16" height="6" rx="3" fill="url(#usageEpic)" opacity="0.8"/>
      <text x="0" y="1" text-anchor="middle" fill="#ffffff" font-family="Arial, sans-serif" font-size="4" font-weight="bold">MASTER</text>
    </g>
    
    <!-- 技能树 -->
    <g opacity="0.8">
      <!-- 主干 -->
      <line x1="0" y1="14" x2="0" y2="25" stroke="url(#skillGradient)" stroke-width="2"/>
      
      <!-- 技能节点 -->
      <g transform="translate(-12, 18)">
        <circle cx="0" cy="0" r="3" fill="url(#skillGradient)" opacity="0.3">
          <animate attributeName="opacity" values="0.2;0.5;0.2" dur="2s" repeatCount="indefinite"/>
        </circle>
        <circle cx="0" cy="0" r="2.5" fill="url(#skillGradient)"/>
        <circle cx="0" cy="0" r="1.5" fill="#4CAF50"/>
        <line x1="3" y1="0" x2="12" y2="0" stroke="url(#skillGradient)" stroke-width="1.5"/>
      </g>
      
      <g transform="translate(12, 18)">
        <circle cx="0" cy="0" r="3" fill="url(#skillGradient)" opacity="0.3">
          <animate attributeName="opacity" values="0.2;0.5;0.2" dur="2.3s" repeatCount="indefinite"/>
        </circle>
        <circle cx="0" cy="0" r="2.5" fill="url(#skillGradient)"/>
        <circle cx="0" cy="0" r="1.5" fill="#4CAF50"/>
        <line x1="-3" y1="0" x2="-12" y2="0" stroke="url(#skillGradient)" stroke-width="1.5"/>
      </g>
      
      <g transform="translate(-8, 22)">
        <circle cx="0" cy="0" r="2.5" fill="url(#skillGradient)" opacity="0.3">
          <animate attributeName="opacity" values="0.2;0.4;0.2" dur="2.5s" repeatCount="indefinite"/>
        </circle>
        <circle cx="0" cy="0" r="2" fill="url(#skillGradient)"/>
        <circle cx="0" cy="0" r="1.2" fill="#66BB6A"/>
        <line x1="2" y1="0" x2="8" y2="0" stroke="url(#skillGradient)" stroke-width="1"/>
      </g>
      
      <g transform="translate(8, 22)">
        <circle cx="0" cy="0" r="2.5" fill="url(#skillGradient)" opacity="0.3">
          <animate attributeName="opacity" values="0.2;0.4;0.2" dur="1.8s" repeatCount="indefinite"/>
        </circle>
        <circle cx="0" cy="0" r="2" fill="url(#skillGradient)"/>
        <circle cx="0" cy="0" r="1.2" fill="#66BB6A"/>
        <line x1="-2" y1="0" x2="-8" y2="0" stroke="url(#skillGradient)" stroke-width="1"/>
      </g>
    </g>
    
    <!-- 能量环 -->
    <g opacity="0.7">
      <circle cx="0" cy="0" r="22" fill="none" stroke="url(#energyGradient)" stroke-width="2">
        <animate attributeName="r" values="20;24;20" dur="3s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.5;0.9;0.5" dur="3s" repeatCount="indefinite"/>
      </circle>
      
      <!-- 能量点 -->
      <g>
        <circle cx="0" cy="-22" r="2" fill="url(#energyGradient)">
          <animate attributeName="opacity" values="0.6;1;0.6" dur="1.5s" repeatCount="indefinite"/>
        </circle>
        <circle cx="22" cy="0" r="2" fill="url(#energyGradient)">
          <animate attributeName="opacity" values="0.5;0.9;0.5" dur="1.8s" repeatCount="indefinite"/>
        </circle>
        <circle cx="0" cy="22" r="2" fill="url(#energyGradient)">
          <animate attributeName="opacity" values="0.7;1;0.7" dur="1.3s" repeatCount="indefinite"/>
        </circle>
        <circle cx="-22" cy="0" r="2" fill="url(#energyGradient)">
          <animate attributeName="opacity" values="0.4;0.8;0.4" dur="2s" repeatCount="indefinite"/>
        </circle>
        
        <animateTransform attributeName="transform" type="rotate" values="0;360" dur="10s" repeatCount="indefinite"/>
      </g>
    </g>
    
    <!-- 大师光环 -->
    <g opacity="0.6">
      <circle cx="0" cy="0" r="28" fill="none" stroke="url(#auraGradient)" stroke-width="1.5">
        <animate attributeName="r" values="26;30;26" dur="5s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.4;0.8;0.4" dur="5s" repeatCount="indefinite"/>
      </circle>
      <circle cx="0" cy="0" r="25" fill="none" stroke="url(#auraGradient)" stroke-width="1">
        <animate attributeName="r" values="23;27;23" dur="4.5s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.3;0.7;0.3" dur="4.5s" repeatCount="indefinite"/>
      </circle>
    </g>
    
    <!-- 成就星座 -->
    <g opacity="0.8">
      <g transform="translate(-25, -18)">
        <path d="M0,-3 L1,-1 L3,0 L1,1 L0,3 L-1,1 L-3,0 L-1,-1 Z" fill="url(#masterBadge)">
          <animate attributeName="opacity" values="0.6;1;0.6" dur="2s" repeatCount="indefinite"/>
        </path>
      </g>
      
      <g transform="translate(25, -15)">
        <path d="M0,-2.5 L0.8,-0.8 L2.5,0 L0.8,0.8 L0,2.5 L-0.8,0.8 L-2.5,0 L-0.8,-0.8 Z" fill="url(#masterBadge)">
          <animate attributeName="opacity" values="0.5;0.9;0.5" dur="2.5s" repeatCount="indefinite"/>
        </path>
      </g>
      
      <g transform="translate(-22, 20)">
        <path d="M0,-2 L0.6,-0.6 L2,0 L0.6,0.6 L0,2 L-0.6,0.6 L-2,0 L-0.6,-0.6 Z" fill="url(#masterBadge)">
          <animate attributeName="opacity" values="0.7;1;0.7" dur="1.8s" repeatCount="indefinite"/>
        </path>
      </g>
      
      <g transform="translate(22, 18)">
        <path d="M0,-2.2 L0.7,-0.7 L2.2,0 L0.7,0.7 L0,2.2 L-0.7,0.7 L-2.2,0 L-0.7,-0.7 Z" fill="url(#masterBadge)">
          <animate attributeName="opacity" values="0.4;0.8;0.4" dur="2.2s" repeatCount="indefinite"/>
        </path>
      </g>
    </g>
    
    <!-- 大师标识 -->
    <g transform="translate(0, 32)">
      <rect x="-18" y="0" width="36" height="6" rx="3" fill="url(#masterBadge)" opacity="0.8"/>
      <text x="0" y="4" text-anchor="middle" fill="#ffffff" font-family="Arial, sans-serif" font-size="5" font-weight="bold">GRANDMASTER</text>
    </g>
  </g>
  
  <!-- 底部华丽标识 -->
  <circle cx="64" cy="115" r="12" fill="none" stroke="url(#usageEpic)" stroke-width="2.5" opacity="0.7"/>
  <circle cx="64" cy="115" r="8" fill="none" stroke="#7B1FA2" stroke-width="1.5" opacity="0.9"/>
  <text x="64" y="119" text-anchor="middle" fill="#1A0033" font-family="Arial, sans-serif" font-size="8" font-weight="bold" opacity="0.9">大师</text>
</svg>
