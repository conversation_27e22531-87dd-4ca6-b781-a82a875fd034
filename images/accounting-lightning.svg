<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 传说级渐变 -->
    <linearGradient id="legendaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fdcb6e" />
      <stop offset="25%" style="stop-color:#e17055" />
      <stop offset="50%" style="stop-color:#fdcb6e" />
      <stop offset="75%" style="stop-color:#e17055" />
      <stop offset="100%" style="stop-color:#fdcb6e" />
    </linearGradient>
    
    <!-- 至尊彩虹光环效果 - 升级版 -->
    <linearGradient id="rainbowAura" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ff6b6b" opacity="0.9" />
      <stop offset="10%" style="stop-color:#ff9ff3" opacity="0.9" />
      <stop offset="20%" style="stop-color:#feca57" opacity="0.9" />
      <stop offset="30%" style="stop-color:#48dbfb" opacity="0.9" />
      <stop offset="40%" style="stop-color:#00ffff" opacity="0.9" />
      <stop offset="50%" style="stop-color:#0abde3" opacity="0.9" />
      <stop offset="60%" style="stop-color:#006ba6" opacity="0.9" />
      <stop offset="70%" style="stop-color:#8e44ad" opacity="0.9" />
      <stop offset="80%" style="stop-color:#c44569" opacity="0.9" />
      <stop offset="90%" style="stop-color:#e74c3c" opacity="0.9" />
      <stop offset="100%" style="stop-color:#ff6b6b" opacity="0.9" />
    </linearGradient>

    <!-- 雷神能量渐变 -->
    <linearGradient id="thunderGodGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#00ffff" />
      <stop offset="20%" style="stop-color:#0080ff" />
      <stop offset="40%" style="stop-color:#8000ff" />
      <stop offset="60%" style="stop-color:#ff00ff" />
      <stop offset="80%" style="stop-color:#ff0080" />
      <stop offset="100%" style="stop-color:#00ffff" />
    </linearGradient>
    
    <!-- 至尊闪电能量渐变 - 升级版 -->
    <linearGradient id="lightningEnergy" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFFFFF" />
      <stop offset="15%" style="stop-color:#e3f2fd" />
      <stop offset="30%" style="stop-color:#00FFFF" />
      <stop offset="45%" style="stop-color:#0080FF" />
      <stop offset="60%" style="stop-color:#8000FF" />
      <stop offset="75%" style="stop-color:#4169E1" />
      <stop offset="90%" style="stop-color:#1e3a8a" />
      <stop offset="100%" style="stop-color:#0f172a" />
    </linearGradient>
    
    <!-- 液态金属效果 -->
    <radialGradient id="liquidMetal" cx="30%" cy="30%" r="80%">
      <stop offset="0%" style="stop-color:#ffffff" />
      <stop offset="30%" style="stop-color:#fdcb6e" opacity="0.9" />
      <stop offset="70%" style="stop-color:#e17055" opacity="0.8" />
      <stop offset="100%" style="stop-color:#d63031" />
    </radialGradient>
  </defs>
  
  <!-- 发光效果 - 多层模拟 -->
  <circle cx="64" cy="64" r="52" fill="url(#legendaryGradient)" opacity="0.1"/>
  <circle cx="64" cy="64" r="50" fill="url(#legendaryGradient)" opacity="0.2"/>
  
  <!-- 最外层彩虹光环 - 微扩张优化 -->
  <circle cx="64" cy="64" r="61.8" fill="none" stroke="url(#rainbowAura)" stroke-width="2.8" opacity="0.7" stroke-linecap="round">
    <animate attributeName="r" values="61;62.6;61" dur="2s" repeatCount="indefinite"/>
    <animate attributeName="stroke-width" values="2.6;3.0;2.6" dur="2s" repeatCount="indefinite"/>
    <animate attributeName="opacity" values="0.6;0.8;0.6" dur="2s" repeatCount="indefinite"/>
  </circle>

  <!-- 中层动态光环 - 优化边界 -->
  <circle cx="64" cy="64" r="56.2" fill="none" stroke="url(#legendaryGradient)" stroke-width="1.9" opacity="0.8" stroke-linecap="round">
    <animate attributeName="opacity" values="0.6;1;0.6" dur="1.5s" repeatCount="indefinite"/>
  </circle>

  <!-- 边界过渡层 -->
  <circle cx="64" cy="64" r="52.5" fill="url(#liquidMetal)" opacity="0.08"/>
  <circle cx="64" cy="64" r="50.5" fill="url(#liquidMetal)" opacity="0.15"/>

  <!-- 主体背景 - 精确边界 -->
  <circle cx="64" cy="64" r="48" fill="url(#liquidMetal)"/>

  <!-- 内圈装饰 - 精细化边界 -->
  <circle cx="64" cy="64" r="40.3" fill="none" stroke="#fff" stroke-width="1.6" opacity="0.7" stroke-linecap="round"/>
  <circle cx="64" cy="64" r="35.2" fill="none" stroke="#fff" stroke-width="0.9" opacity="0.5" stroke-linecap="round"/>
  <circle cx="64" cy="64" r="30.5" fill="none" stroke="#fff" stroke-width="0.6" opacity="0.3" stroke-linecap="round"/>
  
  <!-- 闪电图案 - 传说级设计 -->
  <g transform="translate(64, 64)">
    <!-- 主闪电发光底层 - 精细化边界 -->
    <path d="M-7.8,-23.5 Q7.8,-12.2 7.8,-12 Q-3.8,-12.1 -4,-12 Q11.8,-0.2 12,0 Q-7.8,-0.1 -8,0 Q3.8,11.8 4,12 Q-11.8,23.8 -12,24 Q-0.2,11.9 0,12 Q-7.8,11.9 -8,12 Q7.8,-0.1 8,0 Z" fill="url(#lightningEnergy)" opacity="0.25" stroke-linejoin="round">
      <animate attributeName="opacity" values="0.15;0.4;0.15" dur="0.3s" repeatCount="indefinite"/>
    </path>

    <!-- 主闪电 - 优化边角 -->
    <path d="M-5.9,-19.8 Q5.9,-10.1 6,-10 Q-1.9,-10.05 -2,-10 Q9.9,1.9 10,2 Q-5.9,1.95 -6,2 Q1.9,9.9 2,10 Q-9.9,19.9 -10,20 Q1.9,9.95 2,10 Q-5.9,9.95 -6,10 Q5.9,1.95 6,2 Z" fill="url(#lightningEnergy)" stroke-linejoin="round"/>

    <!-- 闪电核心 - 精细化尖角 -->
    <path d="M-3.8,-15.8 Q3.8,-8.1 4,-8 Q-0.9,-8.05 -1,-8 Q7.9,0.9 8,1 Q-3.9,0.95 -4,1 Q0.9,7.9 1,8 Q-7.9,15.9 -8,16 Q0.9,7.95 1,8 Q-3.9,7.95 -4,8 Q3.9,0.95 4,1 Z" fill="#FFFFFF" opacity="0.9" stroke-linejoin="round">
      <animate attributeName="opacity" values="0.7;1;0.7" dur="0.3s" repeatCount="indefinite"/>
    </path>

    <!-- 闪电边缘光晕 - 精细化多层 -->
    <path d="M-7.8,-23.5 Q7.8,-12.2 7.8,-12 Q-3.8,-12.1 -4,-12 Q11.8,-0.2 12,0 Q-7.8,-0.1 -8,0 Q3.8,11.8 4,12 Q-11.8,23.8 -12,24 Q-0.2,11.9 0,12 Q-7.8,11.9 -8,12 Q7.8,-0.1 8,0 Z" fill="none" stroke="#00FFFF" stroke-width="2.8" opacity="0.25" stroke-linecap="round" stroke-linejoin="round">
      <animate attributeName="stroke-width" values="2.2;3.8;2.2" dur="0.5s" repeatCount="indefinite"/>
    </path>
    <path d="M-7.8,-23.5 Q7.8,-12.2 7.8,-12 Q-3.8,-12.1 -4,-12 Q11.8,-0.2 12,0 Q-7.8,-0.1 -8,0 Q3.8,11.8 4,12 Q-11.8,23.8 -12,24 Q-0.2,11.9 0,12 Q-7.8,11.9 -8,12 Q7.8,-0.1 8,0 Z" fill="none" stroke="#00FFFF" stroke-width="1.8" opacity="0.8" stroke-linecap="round" stroke-linejoin="round">
      <animate attributeName="stroke-width" values="1.2;2.8;1.2" dur="0.5s" repeatCount="indefinite"/>
    </path>
    
    <!-- 侧闪电 - 增强版 -->
    <g opacity="0.7">
      <path d="M-20,-8 L-15,-4 L-18,-4 L-12,0 L-16,0 L-10,4 L-18,8 L-14,4 L-16,4 L-12,0 Z" fill="url(#lightningEnergy)" opacity="0.3">
        <animate attributeName="opacity" values="0.2;0.5;0.2" dur="0.8s" repeatCount="indefinite"/>
      </path>
      <path d="M-18,-6 L-13,-3 L-16,-3 L-10,1 L-14,1 L-8,5 L-16,9 L-12,5 L-14,5 L-10,1 Z" fill="url(#lightningEnergy)">
        <animate attributeName="opacity" values="0.5;0.9;0.5" dur="0.8s" repeatCount="indefinite"/>
      </path>
      
      <path d="M20,-8 L15,-4 L18,-4 L12,0 L16,0 L10,4 L18,8 L14,4 L16,4 L12,0 Z" fill="url(#lightningEnergy)" opacity="0.3">
        <animate attributeName="opacity" values="0.2;0.4;0.2" dur="1.2s" repeatCount="indefinite"/>
      </path>
      <path d="M18,-6 L13,-3 L16,-3 L10,1 L14,1 L8,5 L16,9 L12,5 L14,5 L10,1 Z" fill="url(#lightningEnergy)">
        <animate attributeName="opacity" values="0.3;0.8;0.3" dur="1.2s" repeatCount="indefinite"/>
      </path>
    </g>
    
    <!-- 能量球核心 - 精细化多层发光 -->
    <circle cx="0" cy="0" r="8.2" fill="url(#lightningEnergy)" opacity="0.25">
      <animate attributeName="r" values="6.5;10.2;6.5" dur="1s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.15;0.4;0.15" dur="1s" repeatCount="indefinite"/>
    </circle>
    <circle cx="0" cy="0" r="7" fill="url(#lightningEnergy)" opacity="0.5">
      <animate attributeName="r" values="5.8;8.5;5.8" dur="1s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.4;0.7;0.4" dur="1s" repeatCount="indefinite"/>
    </circle>
    <circle cx="0" cy="0" r="5.8" fill="url(#lightningEnergy)" opacity="0.9">
      <animate attributeName="r" values="4.8;7.5;4.8" dur="1s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.7;1;0.7" dur="1s" repeatCount="indefinite"/>
    </circle>
    <circle cx="-1.2" cy="-1.2" r="2.5" fill="#FFFFFF" opacity="0.6"/>

    <!-- 能量脉冲环 - 边界控制优化 -->
    <circle cx="0" cy="0" r="14.5" fill="none" stroke="#00FFFF" stroke-width="0.9" opacity="0.25" stroke-linecap="round">
      <animate attributeName="r" values="12.2;16.8;12.2" dur="2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.4;0.08;0.4" dur="2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="0" cy="0" r="12.8" fill="none" stroke="#00FFFF" stroke-width="1.1" opacity="0.4" stroke-linecap="round">
      <animate attributeName="r" values="10.2;15.2;10.2" dur="2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.6;0.15;0.6" dur="2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="0" cy="0" r="11.2" fill="none" stroke="#00FFFF" stroke-width="0.8" opacity="0.6" stroke-linecap="round">
      <animate attributeName="r" values="8.8;13.8;8.8" dur="2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.8;0.2;0.8" dur="2s" repeatCount="indefinite"/>
    </circle>
    
    <!-- 速度线条 - 精细化边界 -->
    <g stroke="url(#lightningEnergy)" stroke-width="1.8" opacity="0.6" stroke-linecap="round">
      <line x1="-24.5" y1="-4.8" x2="-15.2" y2="-4.8">
        <animate attributeName="opacity" values="0;1;0" dur="0.2s" repeatCount="indefinite"/>
      </line>
      <line x1="-24.8" y1="0.2" x2="-15.5" y2="0.2">
        <animate attributeName="opacity" values="0;1;0" dur="0.3s" repeatCount="indefinite"/>
      </line>
      <line x1="-24.2" y1="5.2" x2="-14.8" y2="5.2">
        <animate attributeName="opacity" values="0;1;0" dur="0.4s" repeatCount="indefinite"/>
      </line>

      <line x1="15.2" y1="-4.8" x2="24.5" y2="-4.8">
        <animate attributeName="opacity" values="0;1;0" dur="0.25s" repeatCount="indefinite"/>
      </line>
      <line x1="15.5" y1="0.2" x2="24.8" y2="0.2">
        <animate attributeName="opacity" values="0;1;0" dur="0.35s" repeatCount="indefinite"/>
      </line>
      <line x1="14.8" y1="5.2" x2="24.2" y2="5.2">
        <animate attributeName="opacity" values="0;1;0" dur="0.45s" repeatCount="indefinite"/>
      </line>
    </g>
    
    <!-- 雷神之锤装饰 -->
    <g transform="translate(0, 28)" opacity="0.8">
      <rect x="-8" y="0" width="16" height="4" rx="2" fill="url(#legendaryGradient)"/>
      <text x="0" y="3" text-anchor="middle" fill="#fff" font-family="Arial, sans-serif" font-size="5" font-weight="bold">SPEED</text>
    </g>
  </g>
  
  <!-- 粒子风暴效果 - 增强版 -->
  <g opacity="0.9">
    <circle cx="20" cy="20" r="2" fill="#00FFFF" opacity="0.3">
      <animate attributeName="cy" values="20;15;20" dur="1s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;0.8;0" dur="1s" repeatCount="indefinite"/>
    </circle>
    <circle cx="20" cy="20" r="1.5" fill="#00FFFF">
      <animate attributeName="cy" values="20;15;20" dur="1s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;0" dur="1s" repeatCount="indefinite"/>
    </circle>
    
    <circle cx="108" cy="30" r="1.5" fill="#FFFFFF" opacity="0.3">
      <animate attributeName="cx" values="108;103;108" dur="0.8s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;0.7;0" dur="0.8s" repeatCount="indefinite"/>
    </circle>
    <circle cx="108" cy="30" r="1" fill="#FFFFFF">
      <animate attributeName="cx" values="108;103;108" dur="0.8s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;0" dur="0.8s" repeatCount="indefinite"/>
    </circle>
    
    <circle cx="15" cy="100" r="2" fill="#00FFFF" opacity="0.3">
      <animate attributeName="cy" values="100;105;100" dur="1.2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;0.6;0" dur="1.2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="15" cy="100" r="1.5" fill="#00FFFF">
      <animate attributeName="cy" values="100;105;100" dur="1.2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;0" dur="1.2s" repeatCount="indefinite"/>
    </circle>
    
    <circle cx="110" cy="95" r="1.5" fill="#FFFFFF" opacity="0.3">
      <animate attributeName="cx" values="110;115;110" dur="1.5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;0.5;0" dur="1.5s" repeatCount="indefinite"/>
    </circle>
    <circle cx="110" cy="95" r="1" fill="#FFFFFF">
      <animate attributeName="cx" values="110;115;110" dur="1.5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;0" dur="1.5s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- 底部传说标识 -->
  <circle cx="64" cy="115" r="12" fill="none" stroke="url(#legendaryGradient)" stroke-width="3" opacity="0.8"/>
  <circle cx="64" cy="115" r="8" fill="none" stroke="#00FFFF" stroke-width="2" opacity="0.9"/>
  <text x="64" y="119" text-anchor="middle" fill="url(#legendaryGradient)" font-family="Arial, sans-serif" font-size="9" font-weight="bold">闪电</text>
</svg>
