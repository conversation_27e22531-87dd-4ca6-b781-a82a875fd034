<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 记账类稀有级渐变 -->
    <linearGradient id="accountingRare" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F39C12" />
      <stop offset="25%" style="stop-color:#E67E22" />
      <stop offset="50%" style="stop-color:#D35400" />
      <stop offset="75%" style="stop-color:#E67E22" />
      <stop offset="100%" style="stop-color:#F39C12" />
    </linearGradient>
    
    <!-- 麦克风金属渐变 -->
    <radialGradient id="micMetal" cx="30%" cy="30%" r="70%">
      <stop offset="0%" style="stop-color:#FFFACD" />
      <stop offset="50%" style="stop-color:#FFD700" />
      <stop offset="100%" style="stop-color:#B8860B" />
    </radialGradient>
    
    <!-- 声波渐变 -->
    <linearGradient id="soundWave" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#FFD700" opacity="0.2" />
      <stop offset="50%" style="stop-color:#F39C12" opacity="0.8" />
      <stop offset="100%" style="stop-color:#FFD700" opacity="0.2" />
    </linearGradient>
  </defs>
  
  <!-- 发光效果 - 多层模拟 -->
  <circle cx="64" cy="64" r="52" fill="url(#accountingRare)" opacity="0.1"/>
  <circle cx="64" cy="64" r="50" fill="url(#accountingRare)" opacity="0.2"/>
  
  <!-- 外圈边框 -->
  <circle cx="64" cy="64" r="58" fill="none" stroke="url(#accountingRare)" stroke-width="2" opacity="0.6"/>
  <circle cx="64" cy="64" r="56" fill="none" stroke="url(#accountingRare)" stroke-width="1" opacity="0.4"/>
  <circle cx="64" cy="64" r="54" fill="none" stroke="#ffffff" stroke-width="1" opacity="0.4"/>
  
  <!-- 主体背景 -->
  <circle cx="64" cy="64" r="48" fill="url(#accountingRare)"/>
  
  <!-- 内圈装饰 -->
  <circle cx="64" cy="64" r="40" fill="none" stroke="#ffffff" stroke-width="1.5" opacity="0.6"/>
  <circle cx="64" cy="64" r="34" fill="none" stroke="#ffffff" stroke-width="1" opacity="0.4"/>
  
  <!-- 麦克风图案 -->
  <g transform="translate(64, 64)">
    <!-- 麦克风发光底层 -->
    <ellipse cx="0" cy="-8" rx="10" ry="14" fill="url(#micMetal)" opacity="0.3"/>
    <ellipse cx="0" cy="-8" rx="9" ry="13" fill="url(#micMetal)" opacity="0.5"/>
    
    <!-- 麦克风主体 -->
    <ellipse cx="0" cy="-8" rx="8" ry="12" fill="url(#micMetal)"/>
    
    <!-- 麦克风高光 -->
    <ellipse cx="-2" cy="-12" rx="3" ry="4" fill="#FFFFFF" opacity="0.6"/>
    
    <!-- 麦克风网格 -->
    <ellipse cx="0" cy="-8" rx="6" ry="10" fill="none" stroke="#8B4513" stroke-width="1" opacity="0.8"/>
    
    <!-- 网格纹理 -->
    <g stroke="#8B4513" stroke-width="0.5" opacity="0.6">
      <line x1="-6" y1="-16" x2="6" y2="-16"/>
      <line x1="-6" y1="-12" x2="6" y2="-12"/>
      <line x1="-6" y1="-8" x2="6" y2="-8"/>
      <line x1="-6" y1="-4" x2="6" y2="-4"/>
      <line x1="-6" y1="0" x2="6" y2="0"/>
      
      <line x1="-4" y1="-18" x2="-4" y2="2"/>
      <line x1="0" y1="-18" x2="0" y2="2"/>
      <line x1="4" y1="-18" x2="4" y2="2"/>
    </g>
    
    <!-- 麦克风支架 -->
    <rect x="-1" y="4" width="2" height="8" rx="1" fill="url(#micMetal)"/>
    
    <!-- 支架底座 -->
    <ellipse cx="0" cy="14" rx="6" ry="2" fill="url(#micMetal)" opacity="0.8"/>
    
    <!-- 声波效果 - 多层模拟发光 -->
    <g opacity="0.8">
      <!-- 第一层声波 - 发光底层 -->
      <path d="M12,-8 Q16,-12 20,-8 Q16,-4 12,-8" 
            fill="none" 
            stroke="url(#soundWave)" 
            stroke-width="4" 
            opacity="0.2">
        <animate attributeName="opacity" values="0.1;0.3;0.1" dur="1.5s" repeatCount="indefinite"/>
      </path>
      <path d="M12,-8 Q16,-12 20,-8 Q16,-4 12,-8" 
            fill="none" 
            stroke="url(#soundWave)" 
            stroke-width="2">
        <animate attributeName="opacity" values="0.4;0.8;0.4" dur="1.5s" repeatCount="indefinite"/>
      </path>
      
      <!-- 第二层声波 -->
      <path d="M16,-8 Q22,-14 28,-8 Q22,-2 16,-8" 
            fill="none" 
            stroke="url(#soundWave)" 
            stroke-width="3" 
            opacity="0.2">
        <animate attributeName="opacity" values="0.1;0.2;0.1" dur="1.8s" repeatCount="indefinite"/>
      </path>
      <path d="M16,-8 Q22,-14 28,-8 Q22,-2 16,-8" 
            fill="none" 
            stroke="url(#soundWave)" 
            stroke-width="1.5">
        <animate attributeName="opacity" values="0.3;0.6;0.3" dur="1.8s" repeatCount="indefinite"/>
      </path>
      
      <!-- 第三层声波 -->
      <path d="M20,-8 Q28,-16 36,-8 Q28,0 20,-8" 
            fill="none" 
            stroke="url(#soundWave)" 
            stroke-width="2" 
            opacity="0.1">
        <animate attributeName="opacity" values="0.05;0.15;0.05" dur="2.2s" repeatCount="indefinite"/>
      </path>
      <path d="M20,-8 Q28,-16 36,-8 Q28,0 20,-8" 
            fill="none" 
            stroke="url(#soundWave)" 
            stroke-width="1">
        <animate attributeName="opacity" values="0.2;0.4;0.2" dur="2.2s" repeatCount="indefinite"/>
      </path>
    </g>
    
    <!-- 音符装饰 -->
    <g fill="#FFD700" opacity="0.7">
      <!-- 主音符 -->
      <circle cx="-20" cy="-15" r="2"/>
      <rect x="-19" y="-20" width="1" height="8"/>
      <path d="M-18,-20 Q-16,-18 -14,-20" fill="none" stroke="#FFD700" stroke-width="1"/>
      
      <!-- 副音符 -->
      <circle cx="22" cy="12" r="1.5"/>
      <rect x="22.5" y="7" width="0.8" height="6"/>
    </g>
    
    <!-- 中心指示灯 - 多层发光效果 -->
    <circle cx="0" cy="-8" r="4" fill="#FF4500" opacity="0.2">
      <animate attributeName="opacity" values="0.1;0.3;0.1" dur="1s" repeatCount="indefinite"/>
    </circle>
    <circle cx="0" cy="-8" r="3" fill="#FF4500" opacity="0.5">
      <animate attributeName="opacity" values="0.3;0.7;0.3" dur="1s" repeatCount="indefinite"/>
    </circle>
    <circle cx="0" cy="-8" r="2" fill="#FF4500" opacity="0.9">
      <animate attributeName="opacity" values="0.6;1;0.6" dur="1s" repeatCount="indefinite"/>
    </circle>
    
    <!-- 频谱显示 -->
    <g transform="translate(-8, 18)" opacity="0.7">
      <rect x="0" y="0" width="1" height="3" fill="#00FF00">
        <animate attributeName="height" values="2;5;2" dur="0.3s" repeatCount="indefinite"/>
      </rect>
      <rect x="2" y="0" width="1" height="4" fill="#00FF00">
        <animate attributeName="height" values="3;6;3" dur="0.4s" repeatCount="indefinite"/>
      </rect>
      <rect x="4" y="0" width="1" height="2" fill="#FFFF00">
        <animate attributeName="height" values="1;4;1" dur="0.5s" repeatCount="indefinite"/>
      </rect>
      <rect x="6" y="0" width="1" height="5" fill="#FFFF00">
        <animate attributeName="height" values="4;7;4" dur="0.6s" repeatCount="indefinite"/>
      </rect>
      <rect x="8" y="0" width="1" height="3" fill="#FF6347">
        <animate attributeName="height" values="2;5;2" dur="0.7s" repeatCount="indefinite"/>
      </rect>
      <rect x="10" y="0" width="1" height="4" fill="#FF6347">
        <animate attributeName="height" values="3;6;3" dur="0.8s" repeatCount="indefinite"/>
      </rect>
      <rect x="12" y="0" width="1" height="2" fill="#FF0000">
        <animate attributeName="height" values="1;4;1" dur="0.9s" repeatCount="indefinite"/>
      </rect>
      <rect x="14" y="0" width="1" height="3" fill="#FF0000">
        <animate attributeName="height" values="2;5;2" dur="1s" repeatCount="indefinite"/>
      </rect>
    </g>
  </g>
  
  <!-- 底部标识 -->
  <circle cx="64" cy="108" r="10" fill="none" stroke="url(#accountingRare)" stroke-width="2" opacity="0.6"/>
  <text x="64" y="112" 
        text-anchor="middle" 
        fill="#8B4513" 
        font-family="Arial, sans-serif" 
        font-size="8" 
        font-weight="bold"
        opacity="0.9">语音</text>
</svg>
