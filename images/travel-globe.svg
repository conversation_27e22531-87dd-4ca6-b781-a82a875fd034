<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="travelRare" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#42A5F5" />
      <stop offset="100%" style="stop-color:#0D47A1" />
    </linearGradient>
    <radialGradient id="globeGradient" cx="30%" cy="30%" r="70%">
      <stop offset="0%" style="stop-color:#4FC3F7" />
      <stop offset="100%" style="stop-color:#0D47A1" />
    </radialGradient>
  </defs>
  
  <circle cx="64" cy="64" r="52" fill="url(#travelRare)" opacity="0.1"/>
  <circle cx="64" cy="64" r="48" fill="url(#travelRare)"/>
  <circle cx="64" cy="64" r="40" fill="none" stroke="#ffffff" stroke-width="1.5" opacity="0.6"/>
  
  <g transform="translate(64, 64)">
    <circle cx="0" cy="0" r="16" fill="url(#globeGradient)"/>
    <path d="M2,-12 Q8,-14 12,-8 Q10,-4 6,-2 Q2,-4 2,-12 Z" fill="#4CAF50" opacity="0.9"/>
    <path d="M-4,-8 Q0,-10 2,-6 Q4,0 2,6 Q0,8 -2,4 Q-4,0 -4,-8 Z" fill="#4CAF50" opacity="0.9"/>
    <ellipse cx="0" cy="0" rx="16" ry="8" stroke="#FFFFFF" stroke-width="0.5" opacity="0.4" fill="none"/>
    <line x1="-16" y1="0" x2="16" y2="0" stroke="#FFFFFF" stroke-width="0.5" opacity="0.4"/>
  </g>
  
  <circle cx="64" cy="110" r="8" fill="none" stroke="#42A5F5" stroke-width="1.5"/>
  <text x="64" y="114" text-anchor="middle" fill="#0D47A1" font-family="Arial, sans-serif" font-size="8" font-weight="bold">地球</text>
</svg>
