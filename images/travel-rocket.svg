<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="travelEpic" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1E88E5" />
      <stop offset="100%" style="stop-color:#083481" />
    </linearGradient>
    <linearGradient id="rocketGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ECEFF1" />
      <stop offset="100%" style="stop-color:#90A4AE" />
    </linearGradient>
    <radialGradient id="flameGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#FFEB3B" />
      <stop offset="100%" style="stop-color:#E65100" />
    </radialGradient>
  </defs>
  
  <circle cx="64" cy="64" r="54" fill="url(#travelEpic)" opacity="0.1"/>
  <circle cx="64" cy="64" r="48" fill="url(#travelEpic)"/>
  <circle cx="64" cy="64" r="40" fill="none" stroke="#fff" stroke-width="1.8" opacity="0.7"/>
  
  <g transform="translate(64, 64)">
    <ellipse cx="0" cy="-4" rx="4" ry="12" fill="url(#rocketGradient)"/>
    <path d="M0,-16 L-4,-8 L4,-8 Z" fill="url(#rocketGradient)"/>
    <path d="M-4,4 L-8,8 L-4,8 Z" fill="url(#rocketGradient)" opacity="0.8"/>
    <path d="M4,4 L8,8 L4,8 Z" fill="url(#rocketGradient)" opacity="0.8"/>
    
    <ellipse cx="0" cy="12" rx="3" ry="6" fill="url(#flameGradient)">
      <animate attributeName="ry" values="6;8;6" dur="0.5s" repeatCount="indefinite"/>
    </ellipse>
    
    <circle cx="0" cy="-8" r="2" fill="#81D4FA"/>
  </g>
  
  <circle cx="64" cy="115" r="8" fill="none" stroke="#1E88E5" stroke-width="1.5"/>
  <text x="64" y="119" text-anchor="middle" fill="#083481" font-family="Arial, sans-serif" font-size="8" font-weight="bold">火箭</text>
</svg>
