<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="travelEpic" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1E88E5" />
      <stop offset="100%" style="stop-color:#083481" />
    </linearGradient>
    <radialGradient id="trophyGradient" cx="30%" cy="30%" r="70%">
      <stop offset="0%" style="stop-color:#FFD700" />
      <stop offset="100%" style="stop-color:#E65100" />
    </radialGradient>
  </defs>
  
  <circle cx="64" cy="64" r="54" fill="url(#travelEpic)" opacity="0.1"/>
  <circle cx="64" cy="64" r="48" fill="url(#travelEpic)"/>
  <circle cx="64" cy="64" r="40" fill="none" stroke="#fff" stroke-width="1.8" opacity="0.7"/>
  
  <g transform="translate(64, 64)">
    <path d="M-6,-8 Q-8,-12 -6,-16 Q0,-18 6,-16 Q8,-12 6,-8 L4,4 Q2,6 0,6 Q-2,6 -4,4 Z" fill="url(#trophyGradient)"/>
    <ellipse cx="-8" cy="-12" rx="2" ry="4" fill="none" stroke="url(#trophyGradient)" stroke-width="1.5"/>
    <ellipse cx="8" cy="-12" rx="2" ry="4" fill="none" stroke="url(#trophyGradient)" stroke-width="1.5"/>
    <rect x="-8" y="6" width="16" height="3" rx="1.5" fill="url(#trophyGradient)"/>
    
    <g transform="translate(0, -2)">
      <rect x="-4" y="0" width="8" height="3" rx="1.5" fill="url(#travelEpic)" opacity="0.8"/>
      <text x="0" y="2" text-anchor="middle" fill="#FFFFFF" font-family="Arial, sans-serif" font-size="2" font-weight="bold">1ST</text>
    </g>
  </g>
  
  <circle cx="64" cy="115" r="8" fill="none" stroke="#1E88E5" stroke-width="1.5"/>
  <text x="64" y="119" text-anchor="middle" fill="#083481" font-family="Arial, sans-serif" font-size="8" font-weight="bold">奖杯</text>
</svg>
