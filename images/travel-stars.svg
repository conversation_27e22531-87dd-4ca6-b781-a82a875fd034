<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="travelLegendary" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0D47A1" />
      <stop offset="100%" style="stop-color:#64B5F6" />
    </linearGradient>
    <radialGradient id="starGradient" cx="30%" cy="30%" r="70%">
      <stop offset="0%" style="stop-color:#FFFFFF" />
      <stop offset="50%" style="stop-color:#FFD700" />
      <stop offset="100%" style="stop-color:#FF9800" />
    </radialGradient>
  </defs>
  
  <circle cx="64" cy="64" r="56" fill="url(#travelLegendary)" opacity="0.1"/>
  <circle cx="64" cy="64" r="48" fill="url(#travelLegendary)"/>
  <circle cx="64" cy="64" r="42" fill="none" stroke="#fff" stroke-width="2" opacity="0.9"/>
  
  <g transform="translate(64, 64)">
    <path d="M0,-12 L3,-3 L12,0 L3,3 L0,12 L-3,3 L-12,0 L-3,-3 Z" fill="url(#starGradient)">
      <animate attributeName="opacity" values="0.8;1;0.8" dur="2s" repeatCount="indefinite"/>
    </path>
    
    <g opacity="0.9">
      <path d="M-15,-8 L-13,-6 L-11,-8 L-13,-10 Z" fill="url(#starGradient)">
        <animate attributeName="opacity" values="0.6;1;0.6" dur="1.5s" repeatCount="indefinite"/>
      </path>
      <path d="M15,-6 L17,-4 L19,-6 L17,-8 Z" fill="url(#starGradient)">
        <animate attributeName="opacity" values="0.5;0.9;0.5" dur="1.8s" repeatCount="indefinite"/>
      </path>
      <path d="M-12,10 L-10,12 L-8,10 L-10,8 Z" fill="url(#starGradient)">
        <animate attributeName="opacity" values="0.7;1;0.7" dur="1.3s" repeatCount="indefinite"/>
      </path>
      <path d="M12,8 L14,10 L16,8 L14,6 Z" fill="url(#starGradient)">
        <animate attributeName="opacity" values="0.4;0.8;0.4" dur="2.2s" repeatCount="indefinite"/>
      </path>
    </g>
    
    <g stroke="#FFFFFF" stroke-width="0.5" opacity="0.4">
      <line x1="0" y1="0" x2="-15" y2="-8"/>
      <line x1="0" y1="0" x2="15" y2="-6"/>
      <line x1="0" y1="0" x2="-12" y2="10"/>
      <line x1="0" y1="0" x2="12" y2="8"/>
    </g>
  </g>
  
  <circle cx="64" cy="115" r="8" fill="none" stroke="#FFD700" stroke-width="2"/>
  <text x="64" y="119" text-anchor="middle" fill="#0D47A1" font-family="Arial, sans-serif" font-size="8" font-weight="bold">星空</text>
</svg>
