<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 旅行类传说级渐变 -->
    <linearGradient id="travelLegendary" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0D47A1" />
      <stop offset="15%" style="stop-color:#1565C0" />
      <stop offset="30%" style="stop-color:#1976D2" />
      <stop offset="45%" style="stop-color:#1E88E5" />
      <stop offset="60%" style="stop-color:#2196F3" />
      <stop offset="75%" style="stop-color:#42A5F5" />
      <stop offset="90%" style="stop-color:#64B5F6" />
      <stop offset="100%" style="stop-color:#0D47A1" />
    </linearGradient>
    
    <!-- 彩虹光环效果 -->
    <linearGradient id="rainbowAura" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ff6b6b" opacity="0.9" />
      <stop offset="12%" style="stop-color:#ff9ff3" opacity="0.9" />
      <stop offset="24%" style="stop-color:#feca57" opacity="0.9" />
      <stop offset="36%" style="stop-color:#48dbfb" opacity="0.9" />
      <stop offset="48%" style="stop-color:#0abde3" opacity="0.9" />
      <stop offset="60%" style="stop-color:#006ba6" opacity="0.9" />
      <stop offset="72%" style="stop-color:#8e44ad" opacity="0.9" />
      <stop offset="84%" style="stop-color:#c44569" opacity="0.9" />
      <stop offset="100%" style="stop-color:#ff6b6b" opacity="0.9" />
    </linearGradient>
    
    <!-- 液态金属效果 -->
    <radialGradient id="liquidMetal" cx="25%" cy="25%" r="85%">
      <stop offset="0%" style="stop-color:#ffffff" />
      <stop offset="15%" style="stop-color:#e3f2fd" />
      <stop offset="30%" style="stop-color:#bbdefb" />
      <stop offset="50%" style="stop-color:#90caf9" />
      <stop offset="70%" style="stop-color:#64b5f6" />
      <stop offset="85%" style="stop-color:#42a5f5" />
      <stop offset="100%" style="stop-color:#2196f3" />
    </radialGradient>
    
    <!-- 传奇徽章渐变 -->
    <radialGradient id="legendBadge" cx="30%" cy="30%" r="70%">
      <stop offset="0%" style="stop-color:#FFFFFF" />
      <stop offset="30%" style="stop-color:#FFD700" />
      <stop offset="70%" style="stop-color:#FF9800" />
      <stop offset="100%" style="stop-color:#E65100" />
    </radialGradient>
    
    <!-- 世界地图渐变 -->
    <linearGradient id="worldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4CAF50" />
      <stop offset="50%" style="stop-color:#2E7D32" />
      <stop offset="100%" style="stop-color:#1B5E20" />
    </linearGradient>
  </defs>
  
  <!-- 发光效果 - 多层模拟 -->
  <circle cx="64" cy="64" r="54" fill="url(#travelLegendary)" opacity="0.1"/>
  <circle cx="64" cy="64" r="52" fill="url(#travelLegendary)" opacity="0.15"/>
  <circle cx="64" cy="64" r="50" fill="url(#travelLegendary)" opacity="0.2"/>
  
  <!-- 至尊彩虹光环 - 微扩张优化 -->
  <circle cx="64" cy="64" r="61.5" fill="none" stroke="url(#rainbowAura)" stroke-width="3" opacity="0.8" stroke-linecap="round">
    <animate attributeName="r" values="60.8;62.2;60.8" dur="3s" repeatCount="indefinite"/>
    <animate attributeName="stroke-width" values="2.8;3.2;2.8" dur="3s" repeatCount="indefinite"/>
    <animate attributeName="opacity" values="0.7;0.9;0.7" dur="3s" repeatCount="indefinite"/>
  </circle>
  
  <!-- 传说级光环 -->
  <circle cx="64" cy="64" r="56.5" fill="none" stroke="url(#travelLegendary)" stroke-width="2.2" opacity="0.85" stroke-linecap="round">
    <animate attributeName="opacity" values="0.7;1;0.7" dur="2s" repeatCount="indefinite"/>
    <animate attributeName="stroke-width" values="1.8;2.8;1.8" dur="2s" repeatCount="indefinite"/>
  </circle>
  
  <!-- 边界过渡层 -->
  <circle cx="64" cy="64" r="52" fill="url(#liquidMetal)" opacity="0.1"/>
  <circle cx="64" cy="64" r="50" fill="url(#liquidMetal)" opacity="0.2"/>
  
  <!-- 主体背景 - 精确边界 -->
  <circle cx="64" cy="64" r="48" fill="url(#liquidMetal)"/>
  
  <!-- 内圈装饰 - 精细化边界 -->
  <circle cx="64" cy="64" r="40.5" fill="none" stroke="#fff" stroke-width="1.5" opacity="0.7" stroke-linecap="round"/>
  <circle cx="64" cy="64" r="35" fill="none" stroke="#fff" stroke-width="0.8" opacity="0.5" stroke-linecap="round"/>
  <circle cx="64" cy="64" r="30" fill="none" stroke="#fff" stroke-width="0.5" opacity="0.3" stroke-linecap="round"/>
  
  <!-- 传奇图案 - 传说级设计 -->
  <g transform="translate(64, 64)">
    <!-- 中央传奇徽章 - 多层发光 -->
    <circle cx="0" cy="0" r="22" fill="url(#legendBadge)" opacity="0.3">
      <animate attributeName="r" values="20;24;20" dur="4s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.2;0.5;0.2" dur="4s" repeatCount="indefinite"/>
    </circle>
    <circle cx="0" cy="0" r="20" fill="url(#legendBadge)" opacity="0.5"/>
    <circle cx="0" cy="0" r="18" fill="url(#legendBadge)"/>
    
    <!-- 世界地图 -->
    <g opacity="0.9">
      <!-- 大陆1 -->
      <path d="M-8,-12 Q-4,-14 0,-12 Q4,-10 8,-12 Q6,-8 4,-6 Q0,-8 -4,-6 Q-8,-8 -8,-12 Z" fill="url(#worldGradient)"/>
      <!-- 大陆2 -->
      <path d="M-6,2 Q-2,0 2,2 Q6,4 8,8 Q4,10 0,8 Q-4,10 -8,8 Q-6,4 -6,2 Z" fill="url(#worldGradient)"/>
      <!-- 岛屿 -->
      <ellipse cx="-12" cy="-2" rx="2" ry="3" fill="url(#worldGradient)"/>
      <ellipse cx="12" cy="6" rx="1.5" ry="2" fill="url(#worldGradient)"/>
      <circle cx="10" cy="-8" r="1.5" fill="url(#worldGradient)"/>
      <circle cx="-10" cy="12" r="1" fill="url(#worldGradient)"/>
    </g>
    
    <!-- 传奇标记 -->
    <g opacity="0.8">
      <!-- 北极星 -->
      <g transform="translate(0, -15)">
        <circle cx="0" cy="0" r="2" fill="#FFD700" opacity="0.3">
          <animate attributeName="opacity" values="0.2;0.5;0.2" dur="2s" repeatCount="indefinite"/>
        </circle>
        <circle cx="0" cy="0" r="1.5" fill="#FFD700">
          <animate attributeName="opacity" values="0.7;1;0.7" dur="2s" repeatCount="indefinite"/>
        </circle>
        <g stroke="#FFD700" stroke-width="0.5" opacity="0.8">
          <line x1="0" y1="-3" x2="0" y2="-4"/>
          <line x1="2" y1="-2" x2="3" y2="-3"/>
          <line x1="3" y1="0" x2="4" y2="0"/>
          <line x1="2" y1="2" x2="3" y2="3"/>
          <line x1="0" y1="3" x2="0" y2="4"/>
          <line x1="-2" y1="2" x2="-3" y2="3"/>
          <line x1="-3" y1="0" x2="-4" y2="0"/>
          <line x1="-2" y1="-2" x2="-3" y2="-3"/>
        </g>
      </g>
      
      <!-- 传奇路径 -->
      <g opacity="0.7">
        <path d="M-18,-8 Q-12,-12 -6,-8 Q0,-4 6,-8 Q12,-12 18,-8" 
              stroke="#FFD700" 
              stroke-width="2" 
              fill="none" 
              stroke-dasharray="3,1">
          <animate attributeName="stroke-dashoffset" values="0;-20" dur="4s" repeatCount="indefinite"/>
        </path>
        
        <path d="M-18,8 Q-12,4 -6,8 Q0,12 6,8 Q12,4 18,8" 
              stroke="#FFD700" 
              stroke-width="2" 
              fill="none" 
              stroke-dasharray="3,1">
          <animate attributeName="stroke-dashoffset" values="0;-20" dur="4.5s" repeatCount="indefinite"/>
        </path>
      </g>
    </g>
    
    <!-- 传奇成就环 -->
    <g opacity="0.8">
      <!-- 探险成就 -->
      <g transform="translate(-20, -12)">
        <circle cx="0" cy="0" r="3" fill="url(#legendBadge)" opacity="0.3">
          <animate attributeName="opacity" values="0.2;0.5;0.2" dur="2.5s" repeatCount="indefinite"/>
        </circle>
        <circle cx="0" cy="0" r="2.5" fill="url(#legendBadge)"/>
        <path d="M-1,-1 L0,-2 L1,-1 L0,1 Z" fill="#fff"/>
        <circle cx="0" cy="0" r="0.5" fill="#E53935"/>
      </g>
      
      <!-- 海洋成就 -->
      <g transform="translate(20, -12)">
        <circle cx="0" cy="0" r="3" fill="url(#legendBadge)" opacity="0.3">
          <animate attributeName="opacity" values="0.2;0.5;0.2" dur="2.8s" repeatCount="indefinite"/>
        </circle>
        <circle cx="0" cy="0" r="2.5" fill="url(#legendBadge)"/>
        <path d="M-1.5,0 Q-0.5,-1 0.5,0 Q1.5,1 0.5,0.5 Q-0.5,1 -1.5,0 Z" fill="#fff"/>
        <circle cx="0" cy="0" r="0.3" fill="#2196F3"/>
      </g>
      
      <!-- 山峰成就 -->
      <g transform="translate(-20, 12)">
        <circle cx="0" cy="0" r="3" fill="url(#legendBadge)" opacity="0.3">
          <animate attributeName="opacity" values="0.2;0.5;0.2" dur="3.1s" repeatCount="indefinite"/>
        </circle>
        <circle cx="0" cy="0" r="2.5" fill="url(#legendBadge)"/>
        <path d="M0,-1.5 L-1.5,1 L1.5,1 Z" fill="#fff"/>
        <path d="M0,-1.2 L-0.8,-0.2 L0.8,-0.2 Z" fill="#E0E0E0"/>
      </g>
      
      <!-- 冒险成就 -->
      <g transform="translate(20, 12)">
        <circle cx="0" cy="0" r="3" fill="url(#legendBadge)" opacity="0.3">
          <animate attributeName="opacity" values="0.2;0.5;0.2" dur="2.3s" repeatCount="indefinite"/>
        </circle>
        <circle cx="0" cy="0" r="2.5" fill="url(#legendBadge)"/>
        <ellipse cx="0" cy="0" rx="1" ry="1.5" fill="#FF9800"/>
        <ellipse cx="0" cy="0.2" rx="0.6" ry="1" fill="#FFD54F"/>
      </g>
    </g>
    
    <!-- 传奇等级显示 -->
    <g transform="translate(0, 0)" opacity="0.9">
      <rect x="-8" y="-3" width="16" height="6" rx="3" fill="url(#travelLegendary)" opacity="0.8"/>
      <text x="0" y="1" text-anchor="middle" fill="#ffffff" font-family="Arial, sans-serif" font-size="4" font-weight="bold">LEGEND</text>
    </g>
    
    <!-- 神圣光芒 - 边界控制优化 -->
    <g opacity="0.7">
      <!-- 主光芒 - 边界控制 -->
      <line x1="0" y1="-21.5" x2="0" y2="-28" stroke="#FFD700" stroke-width="2" stroke-linecap="round">
        <animate attributeName="y2" values="-28;-32;-28" dur="2s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.5;1;0.5" dur="2s" repeatCount="indefinite"/>
      </line>
      
      <!-- 侧光芒 - 边界控制 -->
      <line x1="-2.8" y1="-19.5" x2="-5.5" y2="-26" stroke="#FFD700" stroke-width="1.5" stroke-linecap="round">
        <animate attributeName="opacity" values="0.4;0.9;0.4" dur="1.8s" repeatCount="indefinite"/>
      </line>
      <line x1="2.8" y1="-19.5" x2="5.5" y2="-26" stroke="#FFD700" stroke-width="1.5" stroke-linecap="round">
        <animate attributeName="opacity" values="0.4;0.9;0.4" dur="2.2s" repeatCount="indefinite"/>
      </line>
      
      <!-- 扩散光芒 - 边界控制 -->
      <line x1="-4.5" y1="-17.8" x2="-8.5" y2="-23.5" stroke="#FF9800" stroke-width="1" stroke-linecap="round">
        <animate attributeName="opacity" values="0.3;0.7;0.3" dur="2.5s" repeatCount="indefinite"/>
      </line>
      <line x1="4.5" y1="-17.8" x2="8.5" y2="-23.5" stroke="#FF9800" stroke-width="1" stroke-linecap="round">
        <animate attributeName="opacity" values="0.3;0.7;0.3" dur="1.9s" repeatCount="indefinite"/>
      </line>
    </g>
    
    <!-- 传奇标识 -->
    <g transform="translate(0, 30)">
      <rect x="-18" y="0" width="36" height="6" rx="3" fill="url(#legendBadge)" opacity="0.8"/>
      <text x="0" y="4" text-anchor="middle" fill="#ffffff" font-family="Arial, sans-serif" font-size="5" font-weight="bold">LEGENDARY</text>
    </g>
  </g>
  
  <!-- 粒子效果 - 增强版 -->
  <g opacity="0.8">
    <circle cx="20" cy="20" r="2" fill="#FFD700" opacity="0.3">
      <animate attributeName="cy" values="20;15;20" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;0.8;0" dur="3s" repeatCount="indefinite"/>
    </circle>
    <circle cx="20" cy="20" r="1.5" fill="#FFD700">
      <animate attributeName="cy" values="20;15;20" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;0" dur="3s" repeatCount="indefinite"/>
    </circle>
    
    <circle cx="108" cy="30" r="1.8" fill="#FF9800" opacity="0.3">
      <animate attributeName="cx" values="108;103;108" dur="2.5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;0.7;0" dur="2.5s" repeatCount="indefinite"/>
    </circle>
    <circle cx="108" cy="30" r="1.2" fill="#FF9800">
      <animate attributeName="cx" values="108;103;108" dur="2.5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;0" dur="2.5s" repeatCount="indefinite"/>
    </circle>
    
    <circle cx="15" cy="100" r="2.2" fill="#42A5F5" opacity="0.3">
      <animate attributeName="cy" values="100;105;100" dur="2.8s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;0.6;0" dur="2.8s" repeatCount="indefinite"/>
    </circle>
    <circle cx="15" cy="100" r="1.6" fill="#42A5F5">
      <animate attributeName="cy" values="100;105;100" dur="2.8s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;0" dur="2.8s" repeatCount="indefinite"/>
    </circle>
    
    <circle cx="110" cy="95" r="1.5" fill="#9C27B0" opacity="0.3">
      <animate attributeName="cx" values="110;115;110" dur="3.2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;0.5;0" dur="3.2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="110" cy="95" r="1" fill="#9C27B0">
      <animate attributeName="cx" values="110;115;110" dur="3.2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;0" dur="3.2s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- 底部华丽标识 -->
  <circle cx="64" cy="115" r="12" fill="none" stroke="url(#travelLegendary)" stroke-width="3" opacity="0.8"/>
  <circle cx="64" cy="115" r="8" fill="none" stroke="#FFD700" stroke-width="2" opacity="0.9"/>
  <text x="64" y="119" text-anchor="middle" fill="#0D47A1" font-family="Arial, sans-serif" font-size="9" font-weight="bold">传奇</text>
</svg>
