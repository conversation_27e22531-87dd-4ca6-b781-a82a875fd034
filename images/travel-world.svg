<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 旅行类传说级渐变 -->
    <linearGradient id="travelLegendary" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0D47A1" />
      <stop offset="15%" style="stop-color:#1565C0" />
      <stop offset="30%" style="stop-color:#1976D2" />
      <stop offset="45%" style="stop-color:#1E88E5" />
      <stop offset="60%" style="stop-color:#2196F3" />
      <stop offset="75%" style="stop-color:#42A5F5" />
      <stop offset="90%" style="stop-color:#64B5F6" />
      <stop offset="100%" style="stop-color:#0D47A1" />
    </linearGradient>
    
    <!-- 彩虹光环效果 -->
    <linearGradient id="rainbowAura" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ff6b6b" opacity="0.9" />
      <stop offset="10%" style="stop-color:#ff9ff3" opacity="0.9" />
      <stop offset="20%" style="stop-color:#feca57" opacity="0.9" />
      <stop offset="30%" style="stop-color:#48dbfb" opacity="0.9" />
      <stop offset="40%" style="stop-color:#00ffff" opacity="0.9" />
      <stop offset="50%" style="stop-color:#0abde3" opacity="0.9" />
      <stop offset="60%" style="stop-color:#006ba6" opacity="0.9" />
      <stop offset="70%" style="stop-color:#8e44ad" opacity="0.9" />
      <stop offset="80%" style="stop-color:#c44569" opacity="0.9" />
      <stop offset="90%" style="stop-color:#e74c3c" opacity="0.9" />
      <stop offset="100%" style="stop-color:#ff6b6b" opacity="0.9" />
    </linearGradient>
    
    <!-- 液态金属效果 -->
    <radialGradient id="liquidMetal" cx="25%" cy="25%" r="85%">
      <stop offset="0%" style="stop-color:#ffffff" />
      <stop offset="15%" style="stop-color:#e3f2fd" />
      <stop offset="30%" style="stop-color:#bbdefb" />
      <stop offset="50%" style="stop-color:#90caf9" />
      <stop offset="70%" style="stop-color:#64b5f6" />
      <stop offset="85%" style="stop-color:#42a5f5" />
      <stop offset="100%" style="stop-color:#2196f3" />
    </radialGradient>
    
    <!-- 地球渐变 -->
    <radialGradient id="earthGradient" cx="30%" cy="30%" r="70%">
      <stop offset="0%" style="stop-color:#4FC3F7" />
      <stop offset="50%" style="stop-color:#2196F3" />
      <stop offset="100%" style="stop-color:#0D47A1" />
    </radialGradient>
    
    <!-- 大陆渐变 -->
    <linearGradient id="continentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4CAF50" />
      <stop offset="50%" style="stop-color:#2E7D32" />
      <stop offset="100%" style="stop-color:#1B5E20" />
    </linearGradient>
    
    <!-- 皇冠渐变 -->
    <radialGradient id="crownGradient" cx="30%" cy="30%" r="70%">
      <stop offset="0%" style="stop-color:#FFD700" />
      <stop offset="50%" style="stop-color:#FF9800" />
      <stop offset="100%" style="stop-color:#E65100" />
    </radialGradient>
  </defs>
  
  <!-- 发光效果 - 多层模拟 -->
  <circle cx="64" cy="64" r="54" fill="url(#travelLegendary)" opacity="0.1"/>
  <circle cx="64" cy="64" r="52" fill="url(#travelLegendary)" opacity="0.15"/>
  <circle cx="64" cy="64" r="50" fill="url(#travelLegendary)" opacity="0.2"/>
  
  <!-- 至尊彩虹光环 - 微扩张优化 -->
  <circle cx="64" cy="64" r="61.8" fill="none" stroke="url(#rainbowAura)" stroke-width="2.8" opacity="0.7" stroke-linecap="round">
    <animate attributeName="r" values="61;62.6;61" dur="2s" repeatCount="indefinite"/>
    <animate attributeName="stroke-width" values="2.6;3.0;2.6" dur="2s" repeatCount="indefinite"/>
    <animate attributeName="opacity" values="0.6;0.8;0.6" dur="2s" repeatCount="indefinite"/>
  </circle>
  
  <!-- 传说级光环 -->
  <circle cx="64" cy="64" r="56.2" fill="none" stroke="url(#travelLegendary)" stroke-width="1.9" opacity="0.8" stroke-linecap="round">
    <animate attributeName="opacity" values="0.6;1;0.6" dur="1.5s" repeatCount="indefinite"/>
  </circle>
  
  <!-- 边界过渡层 -->
  <circle cx="64" cy="64" r="52.5" fill="url(#liquidMetal)" opacity="0.08"/>
  <circle cx="64" cy="64" r="50.5" fill="url(#liquidMetal)" opacity="0.15"/>
  
  <!-- 主体背景 - 精确边界 -->
  <circle cx="64" cy="64" r="48" fill="url(#liquidMetal)"/>
  
  <!-- 内圈装饰 - 精细化边界 -->
  <circle cx="64" cy="64" r="40.3" fill="none" stroke="#fff" stroke-width="1.6" opacity="0.7" stroke-linecap="round"/>
  <circle cx="64" cy="64" r="35.2" fill="none" stroke="#fff" stroke-width="0.9" opacity="0.5" stroke-linecap="round"/>
  <circle cx="64" cy="64" r="30.5" fill="none" stroke="#fff" stroke-width="0.6" opacity="0.3" stroke-linecap="round"/>
  
  <!-- 世界征服者图案 - 传说级设计 -->
  <g transform="translate(64, 64)">
    <!-- 地球主体 - 多层发光 -->
    <circle cx="0" cy="0" r="22" fill="url(#earthGradient)" opacity="0.3">
      <animate attributeName="r" values="20;24;20" dur="6s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.2;0.4;0.2" dur="6s" repeatCount="indefinite"/>
    </circle>
    <circle cx="0" cy="0" r="20" fill="url(#earthGradient)" opacity="0.5"/>
    <circle cx="0" cy="0" r="18" fill="url(#earthGradient)"/>
    
    <!-- 世界大陆 - 详细设计 -->
    <g opacity="0.9">
      <!-- 亚洲 -->
      <path d="M2,-12 Q8,-14 12,-10 Q14,-6 10,-4 Q6,-2 4,-6 Q2,-8 2,-12 Z" fill="url(#continentGradient)"/>
      <!-- 欧洲 -->
      <path d="M-2,-14 Q2,-16 4,-12 Q2,-10 0,-12 Q-2,-14 -2,-14 Z" fill="url(#continentGradient)"/>
      <!-- 非洲 -->
      <path d="M-4,-8 Q0,-10 2,-6 Q4,0 2,6 Q0,8 -2,4 Q-4,0 -4,-8 Z" fill="url(#continentGradient)"/>
      <!-- 北美洲 -->
      <path d="M-12,-10 Q-8,-14 -4,-10 Q-6,-6 -8,-4 Q-12,-6 -12,-10 Z" fill="url(#continentGradient)"/>
      <!-- 南美洲 -->
      <path d="M-10,2 Q-6,0 -4,4 Q-6,10 -8,8 Q-10,4 -10,2 Z" fill="url(#continentGradient)"/>
      <!-- 澳洲 -->
      <path d="M8,8 Q12,6 14,10 Q12,12 8,10 Q8,8 8,8 Z" fill="url(#continentGradient)"/>
      <!-- 南极洲 -->
      <ellipse cx="0" cy="16" rx="8" ry="2" fill="url(#continentGradient)" opacity="0.8"/>
    </g>
    
    <!-- 世界皇冠 - 多层发光 -->
    <g transform="translate(0, -25)">
      <!-- 皇冠发光底层 - 精细化圆角 -->
      <rect x="-12.5" y="2.5" width="25" height="9" rx="4.5" fill="url(#crownGradient)" opacity="0.2"/>
      <rect x="-11.5" y="3" width="23" height="8" rx="4" fill="url(#crownGradient)" opacity="0.4"/>
      <rect x="-10.5" y="3.5" width="21" height="7" rx="3.5" fill="url(#crownGradient)" opacity="0.6"/>
      
      <!-- 皇冠底座 - 优化边角 -->
      <rect x="-10" y="4" width="20" height="6" rx="3" fill="url(#crownGradient)"/>
      
      <!-- 皇冠主体发光 - 平滑边界 -->
      <path d="M-10,4 Q-8.5,-9.8 -8,-10 Q-5.2,3.8 -5,4 Q-2.5,-13.8 -2,-14 Q-0.8,3.8 0,4 Q2.8,-13.8 3,-14 Q5.8,3.8 6,4 Q8.5,-9.8 9,-10 Q10.2,3.8 10,4 Z" fill="url(#crownGradient)" opacity="0.3"/>
      <path d="M-9.5,4 Q-7.2,-8.8 -7,-9 Q-4.2,3.8 -4,4 Q-1.2,-12.8 -1,-13 Q0.8,3.8 1,4 Q3.8,-12.8 4,-13 Q6.8,3.8 7,4 Q8.8,-8.8 9,-9 Q9.2,3.8 9.5,4 Z" fill="url(#crownGradient)"/>
      
      <!-- 中央最高峰发光 - 精细化尖角 -->
      <path d="M-1.5,-13 Q0,-19.5 0,-20 Q0,-19.5 1.5,-13 Q0.9,-13.1 0,-13.3 Q-0.9,-13.1 -1.5,-13 Z" fill="url(#crownGradient)" opacity="0.3"/>
      <path d="M-1.2,-13 Q0,-18.8 0,-19 Q0,-18.8 1.2,-13 Q0.6,-13.1 0,-13.2 Q-0.6,-13.1 -1.2,-13 Z" fill="url(#crownGradient)"/>
      
      <!-- 皇冠宝石 - 精细化多层发光 -->
      <!-- 中央主宝石 - 优化边界 -->
      <circle cx="0" cy="-15.2" r="2.2" fill="url(#crownGradient)" opacity="0.2">
        <animate attributeName="opacity" values="0.1;0.4;0.1" dur="2s" repeatCount="indefinite"/>
      </circle>
      <circle cx="0" cy="-15.1" r="1.8" fill="url(#crownGradient)" opacity="0.4"/>
      <circle cx="0" cy="-15" r="1.5" fill="url(#crownGradient)" opacity="0.9"/>
      <circle cx="0" cy="-15" r="1" fill="#FFD700"/>
      <circle cx="-0.4" cy="-15.4" r="0.5" fill="#FFFFFF" opacity="0.6"/>
      
      <!-- 侧面宝石 -->
      <circle cx="-7.8" cy="-9.2" r="1.5" fill="url(#crownGradient)" opacity="0.2">
        <animate attributeName="opacity" values="0.1;0.3;0.1" dur="2.2s" repeatCount="indefinite"/>
      </circle>
      <circle cx="-7.9" cy="-9.1" r="1.2" fill="url(#crownGradient)" opacity="0.4"/>
      <circle cx="-8" cy="-9" r="1" fill="url(#crownGradient)" opacity="0.8"/>
      <circle cx="-8" cy="-9" r="0.6" fill="#E91E63"/>
      <circle cx="-8.3" cy="-9.3" r="0.3" fill="#FFFFFF" opacity="0.5"/>
      
      <circle cx="7.8" cy="-9.2" r="1.5" fill="url(#crownGradient)" opacity="0.2">
        <animate attributeName="opacity" values="0.1;0.3;0.1" dur="2.4s" repeatCount="indefinite"/>
      </circle>
      <circle cx="7.9" cy="-9.1" r="1.2" fill="url(#crownGradient)" opacity="0.4"/>
      <circle cx="8" cy="-9" r="1" fill="url(#crownGradient)" opacity="0.8"/>
      <circle cx="8" cy="-9" r="0.6" fill="#9C27B0"/>
      <circle cx="7.7" cy="-9.3" r="0.3" fill="#FFFFFF" opacity="0.5"/>
    </g>
    
    <!-- 征服标记 - 环绕地球 -->
    <g opacity="0.8">
      <!-- 征服旗帜 -->
      <g transform="translate(-18, -8)">
        <rect x="0" y="0" width="0.5" height="6" fill="#8D6E63"/>
        <rect x="0.5" y="0" width="4" height="3" fill="url(#crownGradient)">
          <animate attributeName="width" values="4;4.5;4" dur="2s" repeatCount="indefinite"/>
        </rect>
        <circle cx="2.5" cy="1.5" r="0.5" fill="#FFFFFF" opacity="0.8"/>
      </g>
      
      <g transform="translate(18, -8)">
        <rect x="0" y="0" width="0.5" height="6" fill="#8D6E63"/>
        <rect x="-4" y="0" width="4" height="3" fill="url(#crownGradient)">
          <animate attributeName="width" values="4;4.5;4" dur="2.2s" repeatCount="indefinite"/>
        </rect>
        <circle cx="-2" cy="1.5" r="0.5" fill="#FFFFFF" opacity="0.8"/>
      </g>
      
      <g transform="translate(-8, 18)">
        <rect x="0" y="0" width="0.5" height="6" fill="#8D6E63"/>
        <rect x="0.5" y="0" width="4" height="3" fill="url(#crownGradient)">
          <animate attributeName="width" values="4;4.5;4" dur="2.4s" repeatCount="indefinite"/>
        </rect>
        <circle cx="2.5" cy="1.5" r="0.5" fill="#FFFFFF" opacity="0.8"/>
      </g>
      
      <g transform="translate(8, 18)">
        <rect x="0" y="0" width="0.5" height="6" fill="#8D6E63"/>
        <rect x="-4" y="0" width="4" height="3" fill="url(#crownGradient)">
          <animate attributeName="width" values="4;4.5;4" dur="2.6s" repeatCount="indefinite"/>
        </rect>
        <circle cx="-2" cy="1.5" r="0.5" fill="#FFFFFF" opacity="0.8"/>
      </g>
    </g>
    
    <!-- 世界轨道 -->
    <g opacity="0.6">
      <ellipse cx="0" cy="0" rx="28" ry="8" fill="none" stroke="#FFD700" stroke-width="1" opacity="0.4">
        <animate attributeName="opacity" values="0.2;0.6;0.2" dur="4s" repeatCount="indefinite"/>
      </ellipse>
      <ellipse cx="0" cy="0" rx="26" ry="6" fill="none" stroke="#FFD700" stroke-width="0.8" opacity="0.6">
        <animate attributeName="opacity" values="0.4;0.8;0.4" dur="3.5s" repeatCount="indefinite"/>
      </ellipse>
    </g>
    
    <!-- 征服者等级显示 -->
    <g transform="translate(0, 0)" opacity="0.9">
      <rect x="-12" y="-3" width="24" height="6" rx="3" fill="url(#travelLegendary)" opacity="0.8"/>
      <text x="0" y="1" text-anchor="middle" fill="#ffffff" font-family="Arial, sans-serif" font-size="4" font-weight="bold">CONQUEROR</text>
    </g>
    
    <!-- 神圣光芒 - 边界控制优化 -->
    <g opacity="0.7">
      <!-- 主光芒 - 边界控制 -->
      <line x1="0" y1="-28" x2="0" y2="-32" stroke="#FFD700" stroke-width="2" stroke-linecap="round">
        <animate attributeName="y2" values="-32;-35;-32" dur="2s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.5;1;0.5" dur="2s" repeatCount="indefinite"/>
      </line>
      
      <!-- 侧光芒 - 边界控制 -->
      <line x1="-3.5" y1="-26" x2="-6.5" y2="-30" stroke="#FFD700" stroke-width="1.5" stroke-linecap="round">
        <animate attributeName="opacity" values="0.4;0.9;0.4" dur="1.8s" repeatCount="indefinite"/>
      </line>
      <line x1="3.5" y1="-26" x2="6.5" y2="-30" stroke="#FFD700" stroke-width="1.5" stroke-linecap="round">
        <animate attributeName="opacity" values="0.4;0.9;0.4" dur="2.2s" repeatCount="indefinite"/>
      </line>
      
      <!-- 扩散光芒 - 边界控制 -->
      <line x1="-5.5" y1="-24" x2="-9.5" y2="-28" stroke="#FF9800" stroke-width="1" stroke-linecap="round">
        <animate attributeName="opacity" values="0.3;0.7;0.3" dur="2.5s" repeatCount="indefinite"/>
      </line>
      <line x1="5.5" y1="-24" x2="9.5" y2="-28" stroke="#FF9800" stroke-width="1" stroke-linecap="round">
        <animate attributeName="opacity" values="0.3;0.7;0.3" dur="1.9s" repeatCount="indefinite"/>
      </line>
    </g>
    
    <!-- 世界标识 -->
    <g transform="translate(0, 32)">
      <rect x="-18" y="0" width="36" height="6" rx="3" fill="url(#crownGradient)" opacity="0.8"/>
      <text x="0" y="4" text-anchor="middle" fill="#ffffff" font-family="Arial, sans-serif" font-size="5" font-weight="bold">WORLD RULER</text>
    </g>
  </g>
  
  <!-- 粒子效果 - 增强版 -->
  <g opacity="0.8">
    <circle cx="20" cy="20" r="2" fill="#FFD700" opacity="0.3">
      <animate attributeName="cy" values="20;15;20" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;0.8;0" dur="3s" repeatCount="indefinite"/>
    </circle>
    <circle cx="20" cy="20" r="1.5" fill="#FFD700">
      <animate attributeName="cy" values="20;15;20" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;0" dur="3s" repeatCount="indefinite"/>
    </circle>
    
    <circle cx="108" cy="30" r="1.8" fill="#FF9800" opacity="0.3">
      <animate attributeName="cx" values="108;103;108" dur="2.5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;0.7;0" dur="2.5s" repeatCount="indefinite"/>
    </circle>
    <circle cx="108" cy="30" r="1.2" fill="#FF9800">
      <animate attributeName="cx" values="108;103;108" dur="2.5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;0" dur="2.5s" repeatCount="indefinite"/>
    </circle>
    
    <circle cx="15" cy="100" r="2.2" fill="#42A5F5" opacity="0.3">
      <animate attributeName="cy" values="100;105;100" dur="2.8s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;0.6;0" dur="2.8s" repeatCount="indefinite"/>
    </circle>
    <circle cx="15" cy="100" r="1.6" fill="#42A5F5">
      <animate attributeName="cy" values="100;105;100" dur="2.8s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;0" dur="2.8s" repeatCount="indefinite"/>
    </circle>
    
    <circle cx="110" cy="95" r="1.5" fill="#9C27B0" opacity="0.3">
      <animate attributeName="cx" values="110;115;110" dur="3.2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;0.5;0" dur="3.2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="110" cy="95" r="1" fill="#9C27B0">
      <animate attributeName="cx" values="110;115;110" dur="3.2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;0" dur="3.2s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- 底部华丽标识 -->
  <circle cx="64" cy="115" r="12" fill="none" stroke="url(#travelLegendary)" stroke-width="3" opacity="0.8"/>
  <circle cx="64" cy="115" r="8" fill="none" stroke="#FFD700" stroke-width="2" opacity="0.9"/>
  <text x="64" y="119" text-anchor="middle" fill="#0D47A1" font-family="Arial, sans-serif" font-size="9" font-weight="bold">世界</text>
</svg>
