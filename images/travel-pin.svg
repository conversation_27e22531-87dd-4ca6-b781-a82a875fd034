<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 旅行类普通级渐变 -->
    <linearGradient id="travelNormal" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4FC3F7" />
      <stop offset="50%" style="stop-color:#29B6F6" />
      <stop offset="100%" style="stop-color:#0288D1" />
    </linearGradient>
    
    <!-- 定位针渐变 -->
    <linearGradient id="pinGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#E91E63" />
      <stop offset="100%" style="stop-color:#AD1457" />
    </linearGradient>
  </defs>
  
  <!-- 外圈边框 -->
  <circle cx="64" cy="64" r="58" fill="none" stroke="url(#travelNormal)" stroke-width="2" opacity="0.6"/>
  <circle cx="64" cy="64" r="54" fill="none" stroke="#ffffff" stroke-width="1" opacity="0.4"/>
  
  <!-- 主体背景 -->
  <circle cx="64" cy="64" r="48" fill="url(#travelNormal)"/>
  
  <!-- 内圈装饰 -->
  <circle cx="64" cy="64" r="40" fill="none" stroke="#ffffff" stroke-width="1" opacity="0.6"/>
  
  <!-- 定位针图案 -->
  <g transform="translate(64, 64)">
    <!-- 主定位针 -->
    <g transform="translate(0, -2)">
      <circle cx="0" cy="-8" r="8" fill="url(#pinGradient)"/>
      <circle cx="0" cy="-8" r="5" fill="#FFFFFF"/>
      <circle cx="0" cy="-8" r="3" fill="url(#pinGradient)"/>
      <path d="M0,0 L-3,-3 L3,-3 Z" fill="url(#pinGradient)"/>
    </g>
    
    <!-- 其他位置标记 -->
    <g opacity="0.7">
      <g transform="translate(-15, -10)">
        <circle cx="0" cy="0" r="4" fill="#4CAF50"/>
        <circle cx="0" cy="0" r="2" fill="#FFFFFF"/>
      </g>
      
      <g transform="translate(12, -6)">
        <circle cx="0" cy="0" r="3" fill="#FF9800"/>
        <circle cx="0" cy="0" r="1.5" fill="#FFFFFF"/>
      </g>
      
      <g transform="translate(-8, 12)">
        <circle cx="0" cy="0" r="3.5" fill="#9C27B0"/>
        <circle cx="0" cy="0" r="1.8" fill="#FFFFFF"/>
      </g>
      
      <g transform="translate(15, 10)">
        <circle cx="0" cy="0" r="2.5" fill="#2196F3"/>
        <circle cx="0" cy="0" r="1.2" fill="#FFFFFF"/>
      </g>
    </g>
    
    <!-- 连接线 -->
    <g stroke="#FFFFFF" stroke-width="1" opacity="0.4" stroke-dasharray="2,1">
      <line x1="0" y1="0" x2="-15" y2="-10"/>
      <line x1="0" y1="0" x2="12" y2="-6"/>
      <line x1="0" y1="0" x2="-8" y2="12"/>
      <line x1="0" y1="0" x2="15" y2="10"/>
    </g>
    
    <!-- 位置计数 -->
    <g transform="translate(0, 18)">
      <rect x="-6" y="0" width="12" height="4" rx="2" fill="#FFFFFF" opacity="0.9"/>
      <text x="0" y="2.5" text-anchor="middle" fill="#0288D1" font-family="Arial, sans-serif" font-size="2.5" font-weight="bold">5个</text>
    </g>
  </g>
  
  <!-- 底部标识 -->
  <circle cx="64" cy="108" r="10" fill="none" stroke="url(#travelNormal)" stroke-width="2" opacity="0.6"/>
  <text x="64" y="112" text-anchor="middle" fill="#0288D1" font-family="Arial, sans-serif" font-size="8" font-weight="bold" opacity="0.9">位置</text>
</svg>
