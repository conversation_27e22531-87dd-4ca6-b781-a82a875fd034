<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 记账类史诗级渐变 -->
    <linearGradient id="accountingEpic" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#D4AF37" />
      <stop offset="25%" style="stop-color:#FFD700" />
      <stop offset="50%" style="stop-color:#B8860B" />
      <stop offset="75%" style="stop-color:#FFD700" />
      <stop offset="100%" style="stop-color:#D4AF37" />
    </linearGradient>
    
    <!-- 储蓄罐宝石渐变 -->
    <radialGradient id="gemInlay" cx="30%" cy="30%" r="70%">
      <stop offset="0%" style="stop-color:#FFFFFF" opacity="0.9" />
      <stop offset="30%" style="stop-color:#FFD700" opacity="0.8" />
      <stop offset="70%" style="stop-color:#B8860B" opacity="0.9" />
      <stop offset="100%" style="stop-color:#8B4513" opacity="1" />
    </radialGradient>
    
    <!-- 金币雨渐变 -->
    <radialGradient id="coinRain" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#FFD700" />
      <stop offset="50%" style="stop-color:#FFA500" />
      <stop offset="100%" style="stop-color:#FF8C00" />
    </radialGradient>
  </defs>
  
  <!-- 发光效果 - 多层模拟 -->
  <circle cx="64" cy="64" r="52" fill="url(#accountingEpic)" opacity="0.1"/>
  <circle cx="64" cy="64" r="50" fill="url(#accountingEpic)" opacity="0.2"/>
  
  <!-- 外层动态光环 -->
  <circle cx="64" cy="64" r="62" fill="none" stroke="url(#accountingEpic)" stroke-width="1" opacity="0.3">
    <animate attributeName="r" values="60;66;60" dur="4s" repeatCount="indefinite"/>
    <animate attributeName="opacity" values="0.2;0.5;0.2" dur="4s" repeatCount="indefinite"/>
  </circle>
  
  <!-- 多层装饰边框 -->
  <circle cx="64" cy="64" r="58" fill="none" stroke="url(#accountingEpic)" stroke-width="2" opacity="0.6"/>
  <circle cx="64" cy="64" r="54" fill="none" stroke="#fff" stroke-width="1" opacity="0.4"/>
  
  <!-- 主体背景 -->
  <circle cx="64" cy="64" r="48" fill="url(#accountingEpic)"/>
  
  <!-- 内圈装饰 -->
  <circle cx="64" cy="64" r="40" fill="none" stroke="#fff" stroke-width="1.8" opacity="0.7"/>
  <circle cx="64" cy="64" r="34" fill="none" stroke="#fff" stroke-width="1" opacity="0.5"/>
  
  <!-- 储蓄罐图案 - 史诗级设计 -->
  <g transform="translate(64, 64)">
    <!-- 储蓄罐发光底层 -->
    <ellipse cx="0" cy="2" rx="20" ry="16" fill="url(#gemInlay)" opacity="0.3"/>
    <ellipse cx="0" cy="2" rx="19" ry="15" fill="url(#gemInlay)" opacity="0.5"/>
    
    <!-- 储蓄罐主体 -->
    <ellipse cx="0" cy="2" rx="18" ry="14" fill="url(#gemInlay)"/>
    
    <!-- 储蓄罐高光 -->
    <ellipse cx="-6" cy="-4" rx="8" ry="6" fill="#FFFFFF" opacity="0.4"/>
    
    <!-- 储蓄罐装饰环 -->
    <ellipse cx="0" cy="2" rx="16" ry="12" fill="none" stroke="#8B4513" stroke-width="1.5" opacity="0.8"/>
    <ellipse cx="0" cy="2" rx="12" ry="9" fill="none" stroke="#B8860B" stroke-width="1" opacity="0.6"/>
    
    <!-- 投币口 -->
    <rect x="-8" y="-12" width="16" height="3" rx="1.5" fill="#000" opacity="0.8"/>
    <rect x="-7" y="-11.5" width="14" height="2" rx="1" fill="#333" opacity="0.6"/>
    
    <!-- 储蓄罐腿部 -->
    <g fill="url(#gemInlay)" opacity="0.9">
      <ellipse cx="-10" cy="14" rx="3" ry="2"/>
      <ellipse cx="10" cy="14" rx="3" ry="2"/>
      <ellipse cx="-6" cy="16" rx="2.5" ry="1.5"/>
      <ellipse cx="6" cy="16" rx="2.5" ry="1.5"/>
    </g>
    
    <!-- 储蓄罐尾巴 -->
    <path d="M18,0 Q22,-4 20,-8 Q18,-6 16,-4" fill="none" stroke="url(#accountingEpic)" stroke-width="2" opacity="0.8"/>
    
    <!-- 储蓄罐鼻子 -->
    <ellipse cx="-12" cy="0" rx="4" ry="3" fill="url(#gemInlay)" opacity="0.9"/>
    <ellipse cx="-14" cy="-1" rx="1.5" ry="1" fill="#000" opacity="0.7"/>
    <ellipse cx="-14" cy="1" rx="1.5" ry="1" fill="#000" opacity="0.7"/>
    
    <!-- 金币雨效果 - 增强版 -->
    <g opacity="0.8">
      <!-- 大金币 - 多层发光 -->
      <circle cx="-8" cy="-20" r="4" fill="url(#coinRain)" opacity="0.3">
        <animate attributeName="cy" values="-20;20;-20" dur="3s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.3;0.1;0.3" dur="3s" repeatCount="indefinite"/>
      </circle>
      <circle cx="-8" cy="-20" r="3" fill="url(#coinRain)">
        <animate attributeName="cy" values="-20;20;-20" dur="3s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.8;0.3;0.8" dur="3s" repeatCount="indefinite"/>
      </circle>
      
      <circle cx="6" cy="-25" r="3.5" fill="url(#coinRain)" opacity="0.3">
        <animate attributeName="cy" values="-25;25;-25" dur="3.5s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.3;0.1;0.3" dur="3.5s" repeatCount="indefinite"/>
      </circle>
      <circle cx="6" cy="-25" r="2.5" fill="url(#coinRain)">
        <animate attributeName="cy" values="-25;25;-25" dur="3.5s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.7;0.2;0.7" dur="3.5s" repeatCount="indefinite"/>
      </circle>
      
      <circle cx="12" cy="-18" r="3" fill="url(#coinRain)" opacity="0.3">
        <animate attributeName="cy" values="-18;22;-18" dur="2.8s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.3;0.1;0.3" dur="2.8s" repeatCount="indefinite"/>
      </circle>
      <circle cx="12" cy="-18" r="2" fill="url(#coinRain)">
        <animate attributeName="cy" values="-18;22;-18" dur="2.8s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.6;0.1;0.6" dur="2.8s" repeatCount="indefinite"/>
      </circle>
      
      <!-- 小金币群 -->
      <circle cx="-15" cy="-22" r="2" fill="#FFD700" opacity="0.7">
        <animate attributeName="cy" values="-22;18;-22" dur="4s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.7;0.1;0.7" dur="4s" repeatCount="indefinite"/>
      </circle>
      
      <circle cx="0" cy="-28" r="2.3" fill="#FFA500" opacity="0.6">
        <animate attributeName="cy" values="-28;28;-28" dur="3.2s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.6;0.1;0.6" dur="3.2s" repeatCount="indefinite"/>
      </circle>
      
      <circle cx="-20" cy="-15" r="1.7" fill="#FF8C00" opacity="0.5">
        <animate attributeName="cy" values="-15;25;-15" dur="3.8s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.5;0.1;0.5" dur="3.8s" repeatCount="indefinite"/>
      </circle>
    </g>
    
    <!-- 节约标识 -->
    <g transform="translate(0, 22)" opacity="0.9">
      <rect x="-12" y="0" width="24" height="6" rx="3" fill="url(#accountingEpic)" opacity="0.8"/>
      <text x="0" y="4" text-anchor="middle" fill="#fff" font-family="Arial, sans-serif" font-size="6" font-weight="bold">SAVE 80%</text>
    </g>
    
    <!-- 装饰性光芒 -->
    <g opacity="0.6">
      <line x1="0" y1="-30" x2="0" y2="-35" stroke="#FFD700" stroke-width="2"/>
      <line x1="-3" y1="-28" x2="-6" y2="-32" stroke="#FFD700" stroke-width="1.5"/>
      <line x1="3" y1="-28" x2="6" y2="-32" stroke="#FFD700" stroke-width="1.5"/>
      <line x1="-5" y1="-26" x2="-9" y2="-29" stroke="#FFA500" stroke-width="1"/>
      <line x1="5" y1="-26" x2="9" y2="-29" stroke="#FFA500" stroke-width="1"/>
    </g>
  </g>
  
  <!-- 底部华丽标识 -->
  <circle cx="64" cy="110" r="12" fill="none" stroke="url(#accountingEpic)" stroke-width="2.5" opacity="0.7"/>
  <circle cx="64" cy="110" r="8" fill="none" stroke="#FFD700" stroke-width="1.5" opacity="0.9"/>
  <text x="64" y="114" text-anchor="middle" fill="#8B4513" font-family="Arial, sans-serif" font-size="8" font-weight="bold" opacity="0.9">节约</text>
</svg>
