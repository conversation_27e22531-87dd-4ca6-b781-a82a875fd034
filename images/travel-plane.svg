<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 旅行类普通级渐变 -->
    <linearGradient id="travelNormal" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4FC3F7" />
      <stop offset="50%" style="stop-color:#29B6F6" />
      <stop offset="100%" style="stop-color:#0288D1" />
    </linearGradient>
    
    <!-- 飞机渐变 -->
    <linearGradient id="planeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ECEFF1" />
      <stop offset="100%" style="stop-color:#90A4AE" />
    </linearGradient>
    
    <!-- 云朵渐变 -->
    <radialGradient id="cloudGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#FFFFFF" />
      <stop offset="100%" style="stop-color:#E3F2FD" />
    </radialGradient>
  </defs>
  
  <!-- 外圈边框 -->
  <circle cx="64" cy="64" r="58" fill="none" stroke="url(#travelNormal)" stroke-width="2" opacity="0.6"/>
  <circle cx="64" cy="64" r="54" fill="none" stroke="#ffffff" stroke-width="1" opacity="0.4"/>
  
  <!-- 主体背景 -->
  <circle cx="64" cy="64" r="48" fill="url(#travelNormal)"/>
  
  <!-- 内圈装饰 -->
  <circle cx="64" cy="64" r="40" fill="none" stroke="#ffffff" stroke-width="1" opacity="0.6"/>
  
  <!-- 飞机图案 -->
  <g transform="translate(64, 64)">
    <!-- 飞机主体 -->
    <ellipse cx="0" cy="0" rx="12" ry="3" fill="url(#planeGradient)"/>
    
    <!-- 机翼 -->
    <ellipse cx="-3" cy="0" rx="8" ry="1.5" fill="url(#planeGradient)" opacity="0.8"/>
    <ellipse cx="3" cy="0" rx="6" ry="1" fill="url(#planeGradient)" opacity="0.8"/>
    
    <!-- 机头 -->
    <ellipse cx="10" cy="0" rx="2" ry="1" fill="#FFFFFF"/>
    
    <!-- 窗户 -->
    <circle cx="2" cy="0" r="0.8" fill="#81D4FA"/>
    <circle cx="-2" cy="0" r="0.8" fill="#81D4FA"/>
    <circle cx="-6" cy="0" r="0.8" fill="#81D4FA"/>
    
    <!-- 云朵 -->
    <g opacity="0.7">
      <ellipse cx="-18" cy="-12" rx="4" ry="2" fill="url(#cloudGradient)">
        <animate attributeName="cx" values="-18;-15;-18" dur="4s" repeatCount="indefinite"/>
      </ellipse>
      <ellipse cx="15" cy="10" rx="3" ry="1.5" fill="url(#cloudGradient)">
        <animate attributeName="cx" values="15;18;15" dur="5s" repeatCount="indefinite"/>
      </ellipse>
    </g>
    
    <!-- 飞行轨迹 -->
    <path d="M-20,-8 Q-10,-4 0,0 Q10,4 20,8" stroke="#FFFFFF" stroke-width="1" fill="none" opacity="0.6" stroke-dasharray="2,1">
      <animate attributeName="stroke-dashoffset" values="0;-15" dur="3s" repeatCount="indefinite"/>
    </path>
  </g>
  
  <!-- 底部标识 -->
  <circle cx="64" cy="108" r="10" fill="none" stroke="url(#travelNormal)" stroke-width="2" opacity="0.6"/>
  <text x="64" y="112" text-anchor="middle" fill="#0288D1" font-family="Arial, sans-serif" font-size="8" font-weight="bold" opacity="0.9">飞行</text>
</svg>
