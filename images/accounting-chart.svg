<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 记账类稀有级渐变 -->
    <linearGradient id="accountingRare" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F39C12;stop-opacity:1" />
      <stop offset="25%" style="stop-color:#E67E22;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#D35400;stop-opacity:1" />
      <stop offset="75%" style="stop-color:#E67E22;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#F39C12;stop-opacity:1" />
    </linearGradient>
    
    <!-- 图表背景渐变 -->
    <linearGradient id="chartBg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFFEF7;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#FFF8DC;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#F5F5DC;stop-opacity:1" />
    </linearGradient>
    
    <!-- 数据发光渐变 -->
    <linearGradient id="dataGlow" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFD700;stop-opacity:0.8" />
      <stop offset="50%" style="stop-color:#F39C12;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#E67E22;stop-opacity:0.8" />
    </linearGradient>
    
    <!-- 奢华金属光泽 -->
    <filter id="luxuryMetallic">
      <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
      <feOffset dx="0" dy="2" result="offsetBlur"/>
      <feFlood flood-color="#ffffff" flood-opacity="0.4"/>
      <feComposite in="SourceGraphic" in2="offsetBlur" operator="over"/>
      <feGaussianBlur stdDeviation="4" result="blur2"/>
      <feMerge>
        <feMergeNode in="blur2"/>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    
    <!-- 数据发光效果 -->
    <filter id="chartGlow">
      <feGaussianBlur stdDeviation="1.5" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- 外圈奢华边框 -->
  <circle cx="64" cy="64" r="62" fill="none" stroke="url(#accountingRare)" stroke-width="0.8" opacity="0.3"/>
  <circle cx="64" cy="64" r="58" fill="none" stroke="url(#accountingRare)" stroke-width="1.5" opacity="0.5"/>
  <circle cx="64" cy="64" r="54" fill="none" stroke="url(#accountingRare)" stroke-width="2.5" opacity="0.7"/>
  
  <!-- 主体背景 -->
  <circle cx="64" cy="64" r="48" fill="url(#accountingRare)" filter="url(#luxuryMetallic)"/>
  
  <!-- 内圈装饰 -->
  <circle cx="64" cy="64" r="40" fill="none" stroke="#fff" stroke-width="1.8" opacity="0.7"/>
  <circle cx="64" cy="64" r="34" fill="none" stroke="#fff" stroke-width="1" opacity="0.5"/>
  
  <!-- 几何纹样装饰 -->
  <g transform="translate(64, 64)" opacity="0.25">
    <g stroke="#fff" stroke-width="1" fill="none">
      <rect x="-28" y="-28" width="8" height="8"/>
      <rect x="-12" y="-28" width="8" height="8"/>
      <rect x="4" y="-28" width="8" height="8"/>
      <rect x="20" y="-28" width="8" height="8"/>
      
      <rect x="-28" y="20" width="8" height="8"/>
      <rect x="-12" y="20" width="8" height="8"/>
      <rect x="4" y="20" width="8" height="8"/>
      <rect x="20" y="20" width="8" height="8"/>
    </g>
  </g>
  
  <!-- 图表图案 - 奢华设计 -->
  <g transform="translate(64, 64)">
    <!-- 图表背景框 -->
    <rect x="-20" y="-16" width="40" height="28" rx="3" 
          fill="url(#chartBg)" 
          stroke="#B8860B" 
          stroke-width="1.5" 
          opacity="0.95"/>
    
    <!-- 图表网格 -->
    <g stroke="#B8860B" stroke-width="0.4" opacity="0.5">
      <!-- 横向网格线 -->
      <line x1="-18" y1="-12" x2="18" y2="-12"/>
      <line x1="-18" y1="-6" x2="18" y2="-6"/>
      <line x1="-18" y1="0" x2="18" y2="0"/>
      <line x1="-18" y1="6" x2="18" y2="6"/>
      
      <!-- 纵向网格线 -->
      <line x1="-12" y1="-14" x2="-12" y2="10"/>
      <line x1="-4" y1="-14" x2="-4" y2="10"/>
      <line x1="4" y1="-14" x2="4" y2="10"/>
      <line x1="12" y1="-14" x2="12" y2="10"/>
    </g>
    
    <!-- 坐标轴 -->
    <g stroke="#8B4513" stroke-width="1.2" opacity="0.8">
      <line x1="-18" y1="8" x2="18" y2="8"/>
      <line x1="-16" y1="-14" x2="-16" y2="10"/>
    </g>
    
    <!-- 柱状图数据 -->
    <g fill="url(#dataGlow)" filter="url(#chartGlow)">
      <rect x="-14" y="2" width="3" height="6" rx="0.5"/>
      <rect x="-10" y="-2" width="3" height="10" rx="0.5"/>
      <rect x="-6" y="4" width="3" height="4" rx="0.5"/>
      <rect x="-2" y="-6" width="3" height="14" rx="0.5"/>
      <rect x="2" y="0" width="3" height="8" rx="0.5"/>
      <rect x="6" y="-4" width="3" height="12" rx="0.5"/>
      <rect x="10" y="1" width="3" height="7" rx="0.5"/>
      <rect x="14" y="-8" width="3" height="16" rx="0.5"/>
    </g>
    
    <!-- 趋势线 -->
    <path d="M-12,4 Q-8,0 -4,6 Q0,-4 4,2 Q8,-2 12,3 Q16,-6 16,-6" 
          fill="none" 
          stroke="#FF6347" 
          stroke-width="2" 
          filter="url(#chartGlow)"/>
    
    <!-- 数据点 -->
    <g fill="#FF6347" opacity="0.9">
      <circle cx="-12" cy="4" r="1.5"/>
      <circle cx="-4" cy="6" r="1.5"/>
      <circle cx="4" cy="2" r="1.5"/>
      <circle cx="12" cy="3" r="1.5"/>
      <circle cx="16" cy="-6" r="1.5"/>
    </g>
    
    <!-- 图表标题区域 -->
    <rect x="-18" y="-20" width="36" height="6" rx="2" 
          fill="#F39C12" 
          opacity="0.8"/>
    <text x="0" y="-15" 
          text-anchor="middle" 
          fill="#fff" 
          font-family="Arial, sans-serif" 
          font-size="6" 
          font-weight="bold">数据分析</text>
    
    <!-- 图例 -->
    <g transform="translate(12, -12)" opacity="0.8">
      <rect x="0" y="0" width="2" height="2" fill="url(#dataGlow)"/>
      <text x="4" y="2" 
            fill="#8B4513" 
            font-family="Arial, sans-serif" 
            font-size="4">收入</text>
      
      <line x1="0" y1="4" x2="2" y2="4" stroke="#FF6347" stroke-width="1"/>
      <text x="4" y="5" 
            fill="#8B4513" 
            font-family="Arial, sans-serif" 
            font-size="4">支出</text>
    </g>
    
    <!-- 装饰性数据标签 -->
    <g fill="#8B4513" font-family="Arial, sans-serif" font-size="3" opacity="0.7">
      <text x="-12" y="14" text-anchor="middle">Jan</text>
      <text x="-4" y="14" text-anchor="middle">Feb</text>
      <text x="4" y="14" text-anchor="middle">Mar</text>
      <text x="12" y="14" text-anchor="middle">Apr</text>
    </g>
    
    <!-- 放大镜装饰 -->
    <g transform="translate(16, 16)" opacity="0.6">
      <circle cx="0" cy="0" r="3" 
              fill="none" 
              stroke="#B8860B" 
              stroke-width="1"/>
      <line x1="2" y1="2" x2="4" y2="4" 
            stroke="#B8860B" 
            stroke-width="1" 
            stroke-linecap="round"/>
    </g>
  </g>
  
  <!-- 底部奢华标识 -->
  <circle cx="64" cy="108" r="10" fill="none" stroke="url(#accountingRare)" stroke-width="2" opacity="0.6"/>
  <circle cx="64" cy="108" r="6" fill="none" stroke="#F39C12" stroke-width="1" opacity="0.8"/>
  <text x="64" y="112" 
        text-anchor="middle" 
        fill="#8B4513" 
        font-family="Arial, sans-serif" 
        font-size="6" 
        font-weight="bold"
        opacity="0.9">分析师</text>
</svg>
