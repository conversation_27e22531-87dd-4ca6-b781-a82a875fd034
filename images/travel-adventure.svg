<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 旅行类史诗级渐变 -->
    <linearGradient id="travelEpic" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1E88E5" />
      <stop offset="20%" style="stop-color:#1976D2" />
      <stop offset="40%" style="stop-color:#1565C0" />
      <stop offset="60%" style="stop-color:#0D47A1" />
      <stop offset="80%" style="stop-color:#0A3D91" />
      <stop offset="100%" style="stop-color:#083481" />
    </linearGradient>
    
    <!-- 冒险装备渐变 -->
    <linearGradient id="gearGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8D6E63" />
      <stop offset="100%" style="stop-color:#5D4037" />
    </linearGradient>
    
    <!-- 火焰渐变 -->
    <radialGradient id="fireGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#FFEB3B" />
      <stop offset="50%" style="stop-color:#FF9800" />
      <stop offset="100%" style="stop-color:#E65100" />
    </radialGradient>
    
    <!-- 帐篷渐变 -->
    <linearGradient id="tentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4CAF50" />
      <stop offset="100%" style="stop-color:#2E7D32" />
    </linearGradient>
    
    <!-- 星空渐变 -->
    <radialGradient id="starGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#FFFFFF" />
      <stop offset="100%" style="stop-color:#FFD54F" />
    </radialGradient>
  </defs>
  
  <!-- 发光效果 - 多层模拟 -->
  <circle cx="64" cy="64" r="54" fill="url(#travelEpic)" opacity="0.1"/>
  <circle cx="64" cy="64" r="52" fill="url(#travelEpic)" opacity="0.15"/>
  <circle cx="64" cy="64" r="50" fill="url(#travelEpic)" opacity="0.2"/>
  
  <!-- 外层动态光环 -->
  <circle cx="64" cy="64" r="62" fill="none" stroke="url(#travelEpic)" stroke-width="1" opacity="0.3">
    <animate attributeName="r" values="60;66;60" dur="4s" repeatCount="indefinite"/>
    <animate attributeName="opacity" values="0.2;0.5;0.2" dur="4s" repeatCount="indefinite"/>
  </circle>
  
  <!-- 多层装饰边框 -->
  <circle cx="64" cy="64" r="58" fill="none" stroke="url(#travelEpic)" stroke-width="2" opacity="0.6"/>
  <circle cx="64" cy="64" r="54" fill="none" stroke="#fff" stroke-width="1" opacity="0.4"/>
  
  <!-- 主体背景 -->
  <circle cx="64" cy="64" r="48" fill="url(#travelEpic)"/>
  
  <!-- 内圈装饰 -->
  <circle cx="64" cy="64" r="40" fill="none" stroke="#fff" stroke-width="1.8" opacity="0.7"/>
  <circle cx="64" cy="64" r="34" fill="none" stroke="#fff" stroke-width="1" opacity="0.5"/>
  
  <!-- 冒险图案 - 史诗级设计 -->
  <g transform="translate(64, 64)">
    <!-- 营火 - 多层发光 -->
    <g transform="translate(0, 8)">
      <!-- 火焰发光底层 -->
      <ellipse cx="0" cy="-4" rx="6" ry="8" fill="url(#fireGradient)" opacity="0.3">
        <animate attributeName="ry" values="8;10;8" dur="1.5s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.2;0.4;0.2" dur="1.5s" repeatCount="indefinite"/>
      </ellipse>
      
      <!-- 主火焰 -->
      <ellipse cx="0" cy="-4" rx="4" ry="6" fill="url(#fireGradient)">
        <animate attributeName="ry" values="6;7;6" dur="1.2s" repeatCount="indefinite"/>
        <animate attributeName="rx" values="4;4.5;4" dur="1.8s" repeatCount="indefinite"/>
      </ellipse>
      
      <!-- 火焰核心 -->
      <ellipse cx="0" cy="-2" rx="2" ry="3" fill="#FFEB3B">
        <animate attributeName="opacity" values="0.8;1;0.8" dur="1s" repeatCount="indefinite"/>
      </ellipse>
      
      <!-- 木柴 -->
      <rect x="-4" y="2" width="8" height="1.5" rx="0.75" fill="url(#gearGradient)"/>
      <rect x="-3" y="3" width="6" height="1" rx="0.5" fill="url(#gearGradient)" opacity="0.8"/>
      
      <!-- 火花 -->
      <g opacity="0.8">
        <circle cx="-3" cy="-8" r="0.5" fill="#FFD54F">
          <animate attributeName="cy" values="-8;-12;-8" dur="2s" repeatCount="indefinite"/>
          <animate attributeName="opacity" values="0.8;0.2;0.8" dur="2s" repeatCount="indefinite"/>
        </circle>
        <circle cx="2" cy="-9" r="0.3" fill="#FF9800">
          <animate attributeName="cy" values="-9;-13;-9" dur="2.5s" repeatCount="indefinite"/>
          <animate attributeName="opacity" values="0.7;0.1;0.7" dur="2.5s" repeatCount="indefinite"/>
        </circle>
      </g>
    </g>
    
    <!-- 帐篷 - 多层发光 -->
    <g transform="translate(-18, 4)">
      <!-- 帐篷发光底层 -->
      <path d="M0,-8 L-8,8 L8,8 Z" fill="url(#tentGradient)" opacity="0.3"/>
      <path d="M0,-6 L-6,6 L6,6 Z" fill="url(#tentGradient)"/>
      
      <!-- 帐篷装饰 -->
      <line x1="0" y1="-6" x2="0" y2="6" stroke="#1B5E20" stroke-width="0.5"/>
      <rect x="-2" y="4" width="4" height="2" rx="2" fill="#8D6E63"/>
      
      <!-- 帐篷拉绳 -->
      <line x1="-6" y1="6" x2="-10" y2="10" stroke="#8D6E63" stroke-width="0.5"/>
      <line x1="6" y1="6" x2="10" y2="10" stroke="#8D6E63" stroke-width="0.5"/>
      <circle cx="-10" cy="10" r="0.5" fill="#8D6E63"/>
      <circle cx="10" cy="10" r="0.5" fill="#8D6E63"/>
    </g>
    
    <!-- 冒险装备包 -->
    <g transform="translate(18, 6)">
      <!-- 装备包发光底层 -->
      <rect x="0" y="0" width="8" height="10" rx="2" fill="url(#gearGradient)" opacity="0.3"/>
      <rect x="0.5" y="0.5" width="7" height="9" rx="1.5" fill="url(#gearGradient)"/>
      
      <!-- 装备包装饰 -->
      <rect x="1" y="2" width="6" height="1" rx="0.5" fill="#A1887F"/>
      <rect x="1" y="5" width="6" height="1" rx="0.5" fill="#A1887F"/>
      <circle cx="2" cy="7.5" r="0.5" fill="#FFD54F"/>
      <circle cx="6" cy="7.5" r="0.5" fill="#FFD54F"/>
      
      <!-- 登山绳 -->
      <path d="M8,2 Q12,4 10,8" stroke="#8D6E63" stroke-width="1.5" fill="none"/>
      
      <!-- 水壶 -->
      <ellipse cx="10" cy="8" rx="1.5" ry="2" fill="#607D8B"/>
      <rect x="9.5" y="6" width="1" height="1" rx="0.5" fill="#455A64"/>
    </g>
    
    <!-- 星空 -->
    <g opacity="0.8">
      <circle cx="-20" cy="-18" r="1" fill="url(#starGradient)">
        <animate attributeName="opacity" values="0.6;1;0.6" dur="2s" repeatCount="indefinite"/>
      </circle>
      <circle cx="-15" cy="-22" r="0.8" fill="url(#starGradient)">
        <animate attributeName="opacity" values="0.5;0.9;0.5" dur="2.5s" repeatCount="indefinite"/>
      </circle>
      <circle cx="18" cy="-20" r="1.2" fill="url(#starGradient)">
        <animate attributeName="opacity" values="0.7;1;0.7" dur="1.8s" repeatCount="indefinite"/>
      </circle>
      <circle cx="22" cy="-15" r="0.6" fill="url(#starGradient)">
        <animate attributeName="opacity" values="0.4;0.8;0.4" dur="3s" repeatCount="indefinite"/>
      </circle>
      
      <!-- 月亮 -->
      <g transform="translate(0, -22)">
        <circle cx="0" cy="0" r="4" fill="#FFFFFF" opacity="0.3"/>
        <circle cx="0" cy="0" r="3" fill="#FFFFFF" opacity="0.8"/>
        <circle cx="1" cy="-1" r="2" fill="#F5F5F5"/>
      </g>
    </g>
    
    <!-- 冒险工具 -->
    <g transform="translate(-8, -12)" opacity="0.9">
      <!-- 手电筒 -->
      <rect x="0" y="0" width="2" height="6" rx="1" fill="#424242"/>
      <rect x="0.2" y="0.2" width="1.6" height="5.6" rx="0.8" fill="#616161"/>
      <circle cx="1" cy="0" r="1" fill="#FFD54F" opacity="0.8">
        <animate attributeName="opacity" values="0.6;1;0.6" dur="1.5s" repeatCount="indefinite"/>
      </circle>
      
      <!-- 光束 -->
      <path d="M1,0 L-2,-4 L4,-4 Z" fill="#FFD54F" opacity="0.3">
        <animate attributeName="opacity" values="0.2;0.5;0.2" dur="1.5s" repeatCount="indefinite"/>
      </path>
    </g>
    
    <!-- 指南针 -->
    <g transform="translate(12, -15)" opacity="0.9">
      <circle cx="0" cy="0" r="3" fill="url(#gearGradient)" opacity="0.3"/>
      <circle cx="0" cy="0" r="2.5" fill="url(#gearGradient)"/>
      <circle cx="0" cy="0" r="2" fill="#ECEFF1"/>
      
      <!-- 指针 -->
      <path d="M0,-1.5 L-0.3,0 L0,1.5 L0.3,0 Z" fill="#E53935">
        <animate attributeName="transform" values="rotate(0);rotate(360)" dur="8s" repeatCount="indefinite"/>
      </path>
    </g>
    
    <!-- 冒险路径 -->
    <g opacity="0.6">
      <path d="M-25,12 Q-15,8 -5,12 Q5,16 15,12 Q25,8 30,12" 
            stroke="#FFD54F" 
            stroke-width="2" 
            fill="none" 
            stroke-dasharray="4,2">
        <animate attributeName="stroke-dashoffset" values="0;-30" dur="6s" repeatCount="indefinite"/>
      </path>
      
      <!-- 足迹 -->
      <g fill="#8D6E63" opacity="0.7">
        <ellipse cx="-20" cy="14" rx="1" ry="0.5">
          <animate attributeName="opacity" values="0.5;0.9;0.5" dur="2s" repeatCount="indefinite"/>
        </ellipse>
        <ellipse cx="-18" cy="15" rx="0.8" ry="0.4">
          <animate attributeName="opacity" values="0.4;0.8;0.4" dur="2.2s" repeatCount="indefinite"/>
        </ellipse>
        <ellipse cx="0" cy="14" rx="1" ry="0.5">
          <animate attributeName="opacity" values="0.6;1;0.6" dur="1.8s" repeatCount="indefinite"/>
        </ellipse>
        <ellipse cx="2" cy="15" rx="0.8" ry="0.4">
          <animate attributeName="opacity" values="0.5;0.9;0.5" dur="2.5s" repeatCount="indefinite"/>
        </ellipse>
      </g>
    </g>
    
    <!-- 冒险标识 -->
    <g transform="translate(0, 24)">
      <rect x="-18" y="0" width="36" height="6" rx="3" fill="url(#fireGradient)" opacity="0.8"/>
      <text x="0" y="4" text-anchor="middle" fill="#ffffff" font-family="Arial, sans-serif" font-size="5" font-weight="bold">ADVENTURE</text>
    </g>
  </g>
  
  <!-- 底部华丽标识 -->
  <circle cx="64" cy="115" r="12" fill="none" stroke="url(#travelEpic)" stroke-width="2.5" opacity="0.7"/>
  <circle cx="64" cy="115" r="8" fill="none" stroke="#1E88E5" stroke-width="1.5" opacity="0.9"/>
  <text x="64" y="119" text-anchor="middle" fill="#083481" font-family="Arial, sans-serif" font-size="8" font-weight="bold" opacity="0.9">冒险</text>
</svg>
