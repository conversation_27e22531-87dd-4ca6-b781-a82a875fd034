<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 使用类普通级渐变 -->
    <linearGradient id="usageNormal" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#9C27B0" />
      <stop offset="50%" style="stop-color:#7B1FA2" />
      <stop offset="100%" style="stop-color:#4A148C" />
    </linearGradient>
    
    <!-- 欢迎横幅渐变 -->
    <linearGradient id="bannerGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFFFFF" />
      <stop offset="100%" style="stop-color:#F3E5F5" />
    </linearGradient>
    
    <!-- 礼花渐变 -->
    <radialGradient id="confettiGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#FFD54F" />
      <stop offset="100%" style="stop-color:#FF9800" />
    </radialGradient>
  </defs>
  
  <!-- 外圈边框 -->
  <circle cx="64" cy="64" r="58" fill="none" stroke="url(#usageNormal)" stroke-width="2" opacity="0.6"/>
  <circle cx="64" cy="64" r="54" fill="none" stroke="#ffffff" stroke-width="1" opacity="0.4"/>
  
  <!-- 主体背景 -->
  <circle cx="64" cy="64" r="48" fill="url(#usageNormal)"/>
  
  <!-- 内圈装饰 -->
  <circle cx="64" cy="64" r="40" fill="none" stroke="#ffffff" stroke-width="1" opacity="0.6"/>
  
  <!-- 欢迎图案 -->
  <g transform="translate(64, 64)">
    <!-- 欢迎横幅 -->
    <rect x="-18" y="-6" width="36" height="12" rx="6" fill="url(#bannerGradient)"/>
    
    <!-- 欢迎文字 -->
    <text x="0" y="-1" text-anchor="middle" fill="#9C27B0" font-family="Arial, sans-serif" font-size="6" font-weight="bold">欢迎</text>
    <text x="0" y="4" text-anchor="middle" fill="#7B1FA2" font-family="Arial, sans-serif" font-size="3" font-weight="bold">WELCOME</text>
    
    <!-- 礼花装饰 -->
    <g opacity="0.8">
      <circle cx="-15" cy="-15" r="1.5" fill="url(#confettiGradient)">
        <animate attributeName="opacity" values="0.6;1;0.6" dur="2s" repeatCount="indefinite"/>
      </circle>
      <circle cx="15" cy="-12" r="1.2" fill="#E91E63">
        <animate attributeName="opacity" values="0.5;0.9;0.5" dur="2.3s" repeatCount="indefinite"/>
      </circle>
      <circle cx="-12" cy="15" r="1" fill="#4CAF50">
        <animate attributeName="opacity" values="0.7;1;0.7" dur="1.8s" repeatCount="indefinite"/>
      </circle>
      <circle cx="18" cy="12" r="1.3" fill="#2196F3">
        <animate attributeName="opacity" values="0.4;0.8;0.4" dur="2.5s" repeatCount="indefinite"/>
      </circle>
    </g>
    
    <!-- 装饰线条 -->
    <g stroke="#FFFFFF" stroke-width="1" opacity="0.6">
      <line x1="-20" y1="-8" x2="-20" y2="-12"/>
      <line x1="20" y1="-8" x2="20" y2="-12"/>
      <line x1="-20" y1="8" x2="-20" y2="12"/>
      <line x1="20" y1="8" x2="20" y2="12"/>
    </g>
    
    <!-- 欢迎标识 -->
    <g transform="translate(0, 18)">
      <rect x="-10" y="0" width="20" height="4" rx="2" fill="#4CAF50" opacity="0.8"/>
      <text x="0" y="2.5" text-anchor="middle" fill="#FFFFFF" font-family="Arial, sans-serif" font-size="2.5" font-weight="bold">新用户</text>
    </g>
  </g>
  
  <!-- 底部标识 -->
  <circle cx="64" cy="108" r="10" fill="none" stroke="url(#usageNormal)" stroke-width="2" opacity="0.6"/>
  <text x="64" y="112" text-anchor="middle" fill="#4A148C" font-family="Arial, sans-serif" font-size="8" font-weight="bold" opacity="0.9">欢迎</text>
</svg>
