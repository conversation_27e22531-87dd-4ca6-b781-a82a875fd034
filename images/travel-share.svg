<svg width="400" height="300" viewBox="0 0 400 300" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="shareGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF6B6B;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#4ECDC4;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#45B7D1;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="cardGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:rgba(255,255,255,0.9);stop-opacity:1" />
      <stop offset="100%" style="stop-color:rgba(255,255,255,0.7);stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="400" height="300" fill="url(#shareGradient)"/>
  
  <!-- 装饰圆圈 -->
  <circle cx="350" cy="50" r="30" fill="rgba(255,255,255,0.2)"/>
  <circle cx="50" cy="250" r="40" fill="rgba(255,255,255,0.15)"/>
  <circle cx="320" cy="220" r="20" fill="rgba(255,255,255,0.25)"/>
  
  <!-- 主卡片 -->
  <rect x="50" y="80" width="300" height="140" rx="20" ry="20" fill="url(#cardGradient)" opacity="0.9"/>
  
  <!-- 飞机图标 -->
  <g transform="translate(80, 110)">
    <path d="M20 10L30 5L35 15L25 20L20 15L15 20L10 15L15 5Z" fill="#4ECDC4" opacity="0.8"/>
    <circle cx="20" cy="15" r="3" fill="#45B7D1"/>
  </g>
  
  <!-- 标题文字 -->
  <text x="120" y="130" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#2d3436">
    邀请你一起规划旅行
  </text>
  
  <!-- 副标题 -->
  <text x="120" y="155" font-family="Arial, sans-serif" font-size="16" fill="#636e72">
    与好友共同创建精彩旅程
  </text>
  
  <!-- 协作图标 -->
  <g transform="translate(280, 110)">
    <circle cx="15" cy="15" r="8" fill="#FF6B6B" opacity="0.8"/>
    <circle cx="25" cy="15" r="8" fill="#4ECDC4" opacity="0.8"/>
    <circle cx="35" cy="15" r="8" fill="#45B7D1" opacity="0.8"/>
  </g>
  
  <!-- 底部装饰线 -->
  <rect x="80" y="190" width="240" height="2" rx="1" fill="rgba(255,255,255,0.6)"/>
  
  <!-- 小程序标识 -->
  <text x="200" y="260" font-family="Arial, sans-serif" font-size="14" fill="rgba(255,255,255,0.8)" text-anchor="middle">
    爱巢小记 · 旅行规划
  </text>
</svg>
