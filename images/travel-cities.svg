<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 旅行类稀有级渐变 -->
    <linearGradient id="travelRare" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#42A5F5" />
      <stop offset="25%" style="stop-color:#2196F3" />
      <stop offset="50%" style="stop-color:#1976D2" />
      <stop offset="75%" style="stop-color:#1565C0" />
      <stop offset="100%" style="stop-color:#0D47A1" />
    </linearGradient>
    
    <!-- 城市建筑渐变 -->
    <linearGradient id="buildingGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#90A4AE" />
      <stop offset="100%" style="stop-color:#607D8B" />
    </linearGradient>
    
    <!-- 地标渐变 -->
    <linearGradient id="landmarkGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFB74D" />
      <stop offset="100%" style="stop-color:#FF9800" />
    </linearGradient>
    
    <!-- 天空渐变 -->
    <linearGradient id="skyGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#E3F2FD" />
      <stop offset="100%" style="stop-color:#BBDEFB" />
    </linearGradient>
  </defs>
  
  <!-- 发光效果 - 多层模拟 -->
  <circle cx="64" cy="64" r="52" fill="url(#travelRare)" opacity="0.1"/>
  <circle cx="64" cy="64" r="50" fill="url(#travelRare)" opacity="0.2"/>
  
  <!-- 外圈边框 -->
  <circle cx="64" cy="64" r="58" fill="none" stroke="url(#travelRare)" stroke-width="2" opacity="0.6"/>
  <circle cx="64" cy="64" r="54" fill="none" stroke="#ffffff" stroke-width="1" opacity="0.4"/>
  
  <!-- 主体背景 -->
  <circle cx="64" cy="64" r="48" fill="url(#travelRare)"/>
  
  <!-- 内圈装饰 -->
  <circle cx="64" cy="64" r="40" fill="none" stroke="#ffffff" stroke-width="1.5" opacity="0.6"/>
  <circle cx="64" cy="64" r="34" fill="none" stroke="#ffffff" stroke-width="1" opacity="0.4"/>
  
  <!-- 城市天际线图案 -->
  <g transform="translate(64, 64)">
    <!-- 天空背景 -->
    <circle cx="0" cy="0" r="30" fill="url(#skyGradient)" opacity="0.3"/>
    
    <!-- 城市建筑群 - 多层发光升级 -->
    <g opacity="0.9">
      <!-- 摩天大楼1 - 发光效果 -->
      <rect x="-20.5" y="-5.5" width="7" height="21" rx="1" fill="url(#buildingGradient)" opacity="0.3"/>
      <rect x="-20" y="-5" width="6" height="20" rx="1" fill="url(#buildingGradient)"/>
      <rect x="-19" y="-8" width="1" height="3" fill="#FFD54F">
        <animate attributeName="opacity" values="0.6;1;0.6" dur="3s" repeatCount="indefinite"/>
      </rect>
      <rect x="-17" y="-6" width="1" height="1" fill="#FFD54F">
        <animate attributeName="opacity" values="0.4;0.9;0.4" dur="2.5s" repeatCount="indefinite"/>
      </rect>

      <!-- 摩天大楼2 - 发光效果 -->
      <rect x="-12.5" y="-12.5" width="9" height="28" rx="1" fill="url(#buildingGradient)" opacity="0.3"/>
      <rect x="-12" y="-12" width="8" height="27" rx="1" fill="url(#buildingGradient)"/>
      <rect x="-11" y="-15" width="2" height="3" fill="#FFD54F">
        <animate attributeName="opacity" values="0.7;1;0.7" dur="2.8s" repeatCount="indefinite"/>
      </rect>
      <rect x="-8" y="-14" width="1" height="2" fill="#FFD54F">
        <animate attributeName="opacity" values="0.5;0.9;0.5" dur="3.2s" repeatCount="indefinite"/>
      </rect>
      <rect x="-10" y="-8" width="1" height="1" fill="#FFD54F">
        <animate attributeName="opacity" values="0.3;0.8;0.3" dur="2.2s" repeatCount="indefinite"/>
      </rect>
      <rect x="-7" y="-6" width="1" height="1" fill="#FFD54F">
        <animate attributeName="opacity" values="0.4;0.7;0.4" dur="2.7s" repeatCount="indefinite"/>
      </rect>

      <!-- 摩天大楼3 - 发光效果 -->
      <rect x="-2.5" y="-8.5" width="6" height="24" rx="1" fill="url(#buildingGradient)" opacity="0.3"/>
      <rect x="-2" y="-8" width="5" height="23" rx="1" fill="url(#buildingGradient)"/>
      <rect x="-1" y="-10" width="1" height="2" fill="#FFD54F">
        <animate attributeName="opacity" values="0.6;1;0.6" dur="2.4s" repeatCount="indefinite"/>
      </rect>
      <rect x="1" y="-7" width="1" height="1" fill="#FFD54F">
        <animate attributeName="opacity" values="0.4;0.8;0.4" dur="3.1s" repeatCount="indefinite"/>
      </rect>

      <!-- 摩天大楼4 - 发光效果 -->
      <rect x="4.5" y="-6.5" width="8" height="22" rx="1" fill="url(#buildingGradient)" opacity="0.3"/>
      <rect x="5" y="-6" width="7" height="21" rx="1" fill="url(#buildingGradient)"/>
      <rect x="6" y="-9" width="2" height="3" fill="#FFD54F">
        <animate attributeName="opacity" values="0.7;1;0.7" dur="2.6s" repeatCount="indefinite"/>
      </rect>
      <rect x="9" y="-8" width="1" height="2" fill="#FFD54F">
        <animate attributeName="opacity" values="0.5;0.9;0.5" dur="2.9s" repeatCount="indefinite"/>
      </rect>
      <rect x="7" y="-4" width="1" height="1" fill="#FFD54F">
        <animate attributeName="opacity" values="0.3;0.7;0.3" dur="3.3s" repeatCount="indefinite"/>
      </rect>
      <rect x="10" y="-2" width="1" height="1" fill="#FFD54F">
        <animate attributeName="opacity" values="0.4;0.8;0.4" dur="2.1s" repeatCount="indefinite"/>
      </rect>

      <!-- 摩天大楼5 - 发光效果 -->
      <rect x="13.5" y="-3.5" width="5" height="19" rx="1" fill="url(#buildingGradient)" opacity="0.3"/>
      <rect x="14" y="-3" width="4" height="18" rx="1" fill="url(#buildingGradient)"/>
      <rect x="15" y="-5" width="1" height="2" fill="#FFD54F">
        <animate attributeName="opacity" values="0.6;1;0.6" dur="2.3s" repeatCount="indefinite"/>
      </rect>
      <rect x="17" y="-4" width="1" height="1" fill="#FFD54F">
        <animate attributeName="opacity" values="0.4;0.9;0.4" dur="2.8s" repeatCount="indefinite"/>
      </rect>
    </g>
    
    <!-- 地标建筑 -->
    <g fill="url(#landmarkGradient)" opacity="0.8">
      <!-- 塔尖建筑 -->
      <path d="M0,-18 L-3,-8 L3,-8 Z"/>
      <rect x="-1" y="-8" width="2" height="8"/>
      <circle cx="0" cy="-18" r="1" fill="#E91E63"/>
    </g>
    
    <!-- 飞机轨迹 -->
    <g opacity="0.7">
      <path d="M-25,-15 Q-10,-20 5,-15 Q20,-10 30,-15" 
            stroke="#FFFFFF" 
            stroke-width="1" 
            fill="none" 
            stroke-dasharray="2,2">
        <animate attributeName="stroke-dashoffset" values="0;-20" dur="3s" repeatCount="indefinite"/>
      </path>
      
      <!-- 飞机图标 -->
      <g transform="translate(25, -16)">
        <path d="M0,0 L-2,-1 L-4,0 L-2,1 Z" fill="#FFFFFF"/>
        <animate attributeName="transform" values="translate(-25,-14);translate(30,-16)" dur="6s" repeatCount="indefinite"/>
      </g>
    </g>
    
    <!-- 城市标识 -->
    <g transform="translate(0, 22)">
      <rect x="-15" y="0" width="30" height="6" rx="3" fill="url(#landmarkGradient)" opacity="0.8"/>
      <text x="0" y="4" text-anchor="middle" fill="#ffffff" font-family="Arial, sans-serif" font-size="5" font-weight="bold">5 CITIES</text>
    </g>
    
    <!-- 装饰云朵 -->
    <g fill="#FFFFFF" opacity="0.6">
      <ellipse cx="-22" cy="-18" rx="3" ry="1.5">
        <animate attributeName="cx" values="-22;-18;-22" dur="8s" repeatCount="indefinite"/>
      </ellipse>
      <ellipse cx="18" cy="-20" rx="2.5" ry="1.2">
        <animate attributeName="cx" values="18;22;18" dur="10s" repeatCount="indefinite"/>
      </ellipse>
    </g>
  </g>
  
  <!-- 底部华丽标识 -->
  <circle cx="64" cy="110" r="12" fill="none" stroke="url(#travelRare)" stroke-width="2.5" opacity="0.7"/>
  <circle cx="64" cy="110" r="8" fill="none" stroke="#42A5F5" stroke-width="1.5" opacity="0.9"/>
  <text x="64" y="114" text-anchor="middle" fill="#0D47A1" font-family="Arial, sans-serif" font-size="8" font-weight="bold" opacity="0.9">城市</text>
</svg>
