<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="travelEpic" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1E88E5" />
      <stop offset="100%" style="stop-color:#083481" />
    </linearGradient>
    <linearGradient id="islandGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4CAF50" />
      <stop offset="100%" style="stop-color:#2E7D32" />
    </linearGradient>
    <radialGradient id="waterGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#4FC3F7" />
      <stop offset="100%" style="stop-color:#0288D1" />
    </radialGradient>
  </defs>
  
  <circle cx="64" cy="64" r="54" fill="url(#travelEpic)" opacity="0.1"/>
  <circle cx="64" cy="64" r="48" fill="url(#travelEpic)"/>
  <circle cx="64" cy="64" r="40" fill="none" stroke="#fff" stroke-width="1.8" opacity="0.7"/>
  
  <g transform="translate(64, 64)">
    <ellipse cx="0" cy="8" rx="18" ry="6" fill="url(#waterGradient)" opacity="0.8"/>
    <ellipse cx="0" cy="4" rx="12" ry="8" fill="url(#islandGradient)"/>
    
    <rect x="-1" y="-8" width="2" height="12" rx="1" fill="#8D6E63"/>
    <g fill="url(#islandGradient)">
      <ellipse cx="0" cy="-12" rx="6" ry="2" transform="rotate(-30)"/>
      <ellipse cx="0" cy="-12" rx="6" ry="2" transform="rotate(30)"/>
      <ellipse cx="0" cy="-12" rx="6" ry="2"/>
    </g>
    
    <ellipse cx="0" cy="6" rx="10" ry="2" fill="#FFD54F" opacity="0.6"/>
  </g>
  
  <circle cx="64" cy="115" r="8" fill="none" stroke="#1E88E5" stroke-width="1.5"/>
  <text x="64" y="119" text-anchor="middle" fill="#083481" font-family="Arial, sans-serif" font-size="8" font-weight="bold">岛屿</text>
</svg>
