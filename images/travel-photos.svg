<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 旅行类普通级渐变 -->
    <linearGradient id="travelNormal" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4FC3F7" />
      <stop offset="50%" style="stop-color:#29B6F6" />
      <stop offset="100%" style="stop-color:#0288D1" />
    </linearGradient>
    
    <!-- 相机渐变 -->
    <linearGradient id="cameraGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#757575" />
      <stop offset="100%" style="stop-color:#424242" />
    </linearGradient>
    
    <!-- 镜头渐变 -->
    <radialGradient id="lensGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#263238" />
      <stop offset="70%" style="stop-color:#37474F" />
      <stop offset="100%" style="stop-color:#263238" />
    </radialGradient>
    
    <!-- 照片渐变 -->
    <linearGradient id="photoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFFFFF" />
      <stop offset="100%" style="stop-color:#F5F5F5" />
    </linearGradient>
  </defs>
  
  <!-- 外圈边框 -->
  <circle cx="64" cy="64" r="58" fill="none" stroke="url(#travelNormal)" stroke-width="2" opacity="0.6"/>
  <circle cx="64" cy="64" r="54" fill="none" stroke="#ffffff" stroke-width="1" opacity="0.4"/>
  
  <!-- 主体背景 -->
  <circle cx="64" cy="64" r="48" fill="url(#travelNormal)"/>
  
  <!-- 内圈装饰 -->
  <circle cx="64" cy="64" r="40" fill="none" stroke="#ffffff" stroke-width="1" opacity="0.6"/>
  
  <!-- 相机和照片图案 -->
  <g transform="translate(64, 64)">
    <!-- 相机主体 -->
    <rect x="-10" y="-6" width="20" height="12" rx="2" fill="url(#cameraGradient)"/>
    
    <!-- 相机顶部 -->
    <rect x="-8" y="-8" width="16" height="2" rx="1" fill="url(#cameraGradient)"/>
    
    <!-- 镜头 -->
    <circle cx="0" cy="0" r="6" fill="url(#lensGradient)"/>
    <circle cx="0" cy="0" r="4" fill="#263238"/>
    <circle cx="0" cy="0" r="2" fill="#37474F"/>
    
    <!-- 镜头反光 -->
    <ellipse cx="-1.5" cy="-1.5" rx="1" ry="0.5" fill="#FFFFFF" opacity="0.6"/>
    
    <!-- 取景器 -->
    <rect x="-2" y="-7" width="4" height="1" rx="0.5" fill="#37474F"/>
    
    <!-- 快门按钮 -->
    <circle cx="6" cy="-7" r="1" fill="#E91E63"/>
    
    <!-- 闪光灯 -->
    <rect x="-6" y="-7" width="2" height="1" rx="0.5" fill="#FFD54F"/>
    
    <!-- 照片散落效果 -->
    <g opacity="0.8">
      <!-- 照片1 -->
      <g transform="translate(-18, -8) rotate(-15)">
        <rect x="0" y="0" width="8" height="6" rx="0.5" fill="url(#photoGradient)" stroke="#E0E0E0" stroke-width="0.5"/>
        <rect x="0.5" y="0.5" width="7" height="4" fill="#81C784"/>
        <circle cx="2" cy="2" r="0.5" fill="#FFD54F"/>
        <path d="M4,3 L5,2 L6,3 L7,2" stroke="#4CAF50" stroke-width="0.3" fill="none"/>
      </g>
      
      <!-- 照片2 -->
      <g transform="translate(12, -10) rotate(20)">
        <rect x="0" y="0" width="8" height="6" rx="0.5" fill="url(#photoGradient)" stroke="#E0E0E0" stroke-width="0.5"/>
        <rect x="0.5" y="0.5" width="7" height="4" fill="#29B6F6"/>
        <circle cx="6" cy="2" r="0.8" fill="#FFFFFF"/>
        <path d="M1,4 Q3,2 5,4" stroke="#0288D1" stroke-width="0.3" fill="none"/>
      </g>
      
      <!-- 照片3 -->
      <g transform="translate(-8, 12) rotate(-8)">
        <rect x="0" y="0" width="8" height="6" rx="0.5" fill="url(#photoGradient)" stroke="#E0E0E0" stroke-width="0.5"/>
        <rect x="0.5" y="0.5" width="7" height="4" fill="#FF9800"/>
        <circle cx="4" cy="2.5" r="1" fill="#FFD54F"/>
        <path d="M1,1 L2,2 L1,3" stroke="#F57C00" stroke-width="0.3" fill="none"/>
      </g>
    </g>
    
    <!-- 拍照计数 -->
    <g transform="translate(0, 18)">
      <rect x="-8" y="0" width="16" height="5" rx="2.5" fill="#E91E63" opacity="0.8"/>
      <text x="0" y="3" text-anchor="middle" fill="#ffffff" font-family="Arial, sans-serif" font-size="4" font-weight="bold">+50</text>
    </g>
  </g>
  
  <!-- 底部标识 -->
  <circle cx="64" cy="108" r="10" fill="none" stroke="url(#travelNormal)" stroke-width="2" opacity="0.6"/>
  <text x="64" y="112" text-anchor="middle" fill="#0277BD" font-family="Arial, sans-serif" font-size="8" font-weight="bold" opacity="0.9">摄影</text>
</svg>
