<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 旅行类普通级渐变 -->
    <linearGradient id="travelNormal" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4FC3F7" />
      <stop offset="50%" style="stop-color:#29B6F6" />
      <stop offset="100%" style="stop-color:#0288D1" />
    </linearGradient>
    
    <!-- 地球渐变 -->
    <radialGradient id="earthGradient" cx="30%" cy="30%" r="70%">
      <stop offset="0%" style="stop-color:#81C784" />
      <stop offset="50%" style="stop-color:#4CAF50" />
      <stop offset="100%" style="stop-color:#2E7D32" />
    </radialGradient>
    
    <!-- 路径渐变 -->
    <linearGradient id="pathGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFD54F" />
      <stop offset="100%" style="stop-color:#FFC107" />
    </linearGradient>
  </defs>
  
  <!-- 外圈边框 -->
  <circle cx="64" cy="64" r="58" fill="none" stroke="url(#travelNormal)" stroke-width="2" opacity="0.6"/>
  <circle cx="64" cy="64" r="54" fill="none" stroke="#ffffff" stroke-width="1" opacity="0.4"/>
  
  <!-- 主体背景 -->
  <circle cx="64" cy="64" r="48" fill="url(#travelNormal)"/>
  
  <!-- 内圈装饰 -->
  <circle cx="64" cy="64" r="40" fill="none" stroke="#ffffff" stroke-width="1" opacity="0.6"/>
  
  <!-- 地球和路径图案 -->
  <g transform="translate(64, 64)">
    <!-- 地球主体 -->
    <circle cx="0" cy="0" r="16" fill="url(#earthGradient)"/>
    
    <!-- 大陆轮廓 -->
    <g fill="#2E7D32" opacity="0.8">
      <path d="M-8,-12 Q-4,-14 0,-12 Q4,-10 8,-12 Q6,-8 4,-6 Q0,-8 -4,-6 Q-8,-8 -8,-12 Z"/>
      <path d="M-6,2 Q-2,0 2,2 Q6,4 8,8 Q4,10 0,8 Q-4,10 -8,8 Q-6,4 -6,2 Z"/>
      <ellipse cx="-10" cy="-2" rx="3" ry="4"/>
      <ellipse cx="10" cy="6" rx="2" ry="3"/>
    </g>
    
    <!-- 旅行路径 -->
    <g stroke="url(#pathGradient)" stroke-width="2" fill="none" opacity="0.9">
      <path d="M-20,-10 Q-10,-15 0,-10 Q10,-5 20,-10">
        <animate attributeName="stroke-dasharray" values="0,100;50,50;100,0" dur="3s" repeatCount="indefinite"/>
      </path>
      <path d="M-18,8 Q-8,3 2,8 Q12,13 22,8">
        <animate attributeName="stroke-dasharray" values="0,100;50,50;100,0" dur="3.5s" repeatCount="indefinite"/>
      </path>
    </g>
    
    <!-- 起点和终点标记 -->
    <circle cx="-20" cy="-10" r="2" fill="#E91E63"/>
    <circle cx="20" cy="-10" r="2" fill="#4CAF50"/>
    <circle cx="-18" cy="8" r="2" fill="#FF9800"/>
    <circle cx="22" cy="8" r="2" fill="#9C27B0"/>
    
    <!-- 距离标识 -->
    <g transform="translate(0, 22)">
      <rect x="-12" y="0" width="24" height="6" rx="3" fill="url(#pathGradient)" opacity="0.8"/>
      <text x="0" y="4" text-anchor="middle" fill="#ffffff" font-family="Arial, sans-serif" font-size="5" font-weight="bold">1000KM</text>
    </g>
    
    <!-- 装饰箭头 -->
    <g fill="url(#pathGradient)" opacity="0.7">
      <path d="M-25,-8 L-22,-10 L-25,-12 L-23,-10 Z"/>
      <path d="M25,-8 L22,-10 L25,-12 L23,-10 Z"/>
    </g>
  </g>
  
  <!-- 底部标识 -->
  <circle cx="64" cy="108" r="10" fill="none" stroke="url(#travelNormal)" stroke-width="2" opacity="0.6"/>
  <text x="64" y="112" text-anchor="middle" fill="#0277BD" font-family="Arial, sans-serif" font-size="8" font-weight="bold" opacity="0.9">里程</text>
</svg>
