<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 使用类史诗级渐变 -->
    <linearGradient id="usageEpic" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#7B1FA2" />
      <stop offset="20%" style="stop-color:#6A1B9A" />
      <stop offset="40%" style="stop-color:#4A148C" />
      <stop offset="60%" style="stop-color:#38006B" />
      <stop offset="80%" style="stop-color:#2E0054" />
      <stop offset="100%" style="stop-color:#1A0033" />
    </linearGradient>
    
    <!-- 网格渐变 -->
    <linearGradient id="gridGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#E1BEE7" />
      <stop offset="100%" style="stop-color:#CE93D8" />
    </linearGradient>
    
    <!-- 活跃格子渐变 -->
    <radialGradient id="activeGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#4CAF50" />
      <stop offset="100%" style="stop-color:#2E7D32" />
    </radialGradient>
  </defs>
  
  <!-- 发光效果 - 多层模拟 -->
  <circle cx="64" cy="64" r="54" fill="url(#usageEpic)" opacity="0.1"/>
  <circle cx="64" cy="64" r="52" fill="url(#usageEpic)" opacity="0.15"/>
  <circle cx="64" cy="64" r="50" fill="url(#usageEpic)" opacity="0.2"/>
  
  <!-- 外层动态光环 -->
  <circle cx="64" cy="64" r="62" fill="none" stroke="url(#usageEpic)" stroke-width="1" opacity="0.3">
    <animate attributeName="r" values="60;66;60" dur="4s" repeatCount="indefinite"/>
    <animate attributeName="opacity" values="0.2;0.5;0.2" dur="4s" repeatCount="indefinite"/>
  </circle>
  
  <!-- 多层装饰边框 -->
  <circle cx="64" cy="64" r="58" fill="none" stroke="url(#usageEpic)" stroke-width="2" opacity="0.6"/>
  <circle cx="64" cy="64" r="54" fill="none" stroke="#fff" stroke-width="1" opacity="0.4"/>
  
  <!-- 主体背景 -->
  <circle cx="64" cy="64" r="48" fill="url(#usageEpic)"/>
  
  <!-- 内圈装饰 -->
  <circle cx="64" cy="64" r="40" fill="none" stroke="#fff" stroke-width="1.8" opacity="0.7"/>
  <circle cx="64" cy="64" r="34" fill="none" stroke="#fff" stroke-width="1" opacity="0.5"/>
  
  <!-- 网格图案 - 史诗级设计 -->
  <g transform="translate(64, 64)">
    <!-- 中央网格 -->
    <g>
      <!-- 网格背景 -->
      <rect x="-15" y="-15" width="30" height="30" rx="3" fill="url(#gridGradient)" opacity="0.3"/>
      <rect x="-14" y="-14" width="28" height="28" rx="2.5" fill="url(#gridGradient)" opacity="0.5"/>
      <rect x="-13" y="-13" width="26" height="26" rx="2" fill="url(#gridGradient)"/>
      
      <!-- 网格线 -->
      <g stroke="#7B1FA2" stroke-width="0.5" opacity="0.8">
        <!-- 垂直线 -->
        <line x1="-8" y1="-13" x2="-8" y2="13"/>
        <line x1="-3" y1="-13" x2="-3" y2="13"/>
        <line x1="3" y1="-13" x2="3" y2="13"/>
        <line x1="8" y1="-13" x2="8" y2="13"/>
        
        <!-- 水平线 -->
        <line x1="-13" y1="-8" x2="13" y2="-8"/>
        <line x1="-13" y1="-3" x2="13" y2="-3"/>
        <line x1="-13" y1="3" x2="13" y2="3"/>
        <line x1="-13" y1="8" x2="13" y2="8"/>
      </g>
      
      <!-- 活跃格子 -->
      <g>
        <rect x="-12" y="-12" width="4" height="4" rx="0.5" fill="url(#activeGradient)" opacity="0.8">
          <animate attributeName="opacity" values="0.6;1;0.6" dur="2s" repeatCount="indefinite"/>
        </rect>
        <rect x="-2" y="-12" width="4" height="4" rx="0.5" fill="url(#activeGradient)" opacity="0.8">
          <animate attributeName="opacity" values="0.5;0.9;0.5" dur="2.3s" repeatCount="indefinite"/>
        </rect>
        <rect x="8" y="-12" width="4" height="4" rx="0.5" fill="url(#activeGradient)" opacity="0.8">
          <animate attributeName="opacity" values="0.7;1;0.7" dur="1.8s" repeatCount="indefinite"/>
        </rect>
        
        <rect x="-7" y="-7" width="4" height="4" rx="0.5" fill="url(#activeGradient)" opacity="0.8">
          <animate attributeName="opacity" values="0.4;0.8;0.4" dur="2.5s" repeatCount="indefinite"/>
        </rect>
        <rect x="3" y="-7" width="4" height="4" rx="0.5" fill="url(#activeGradient)" opacity="0.8">
          <animate attributeName="opacity" values="0.6;1;0.6" dur="2.1s" repeatCount="indefinite"/>
        </rect>
        
        <rect x="-12" y="-2" width="4" height="4" rx="0.5" fill="url(#activeGradient)" opacity="0.8">
          <animate attributeName="opacity" values="0.5;0.9;0.5" dur="1.9s" repeatCount="indefinite"/>
        </rect>
        <rect x="8" y="-2" width="4" height="4" rx="0.5" fill="url(#activeGradient)" opacity="0.8">
          <animate attributeName="opacity" values="0.7;1;0.7" dur="2.4s" repeatCount="indefinite"/>
        </rect>
        
        <rect x="-7" y="3" width="4" height="4" rx="0.5" fill="url(#activeGradient)" opacity="0.8">
          <animate attributeName="opacity" values="0.3;0.7;0.3" dur="2.7s" repeatCount="indefinite"/>
        </rect>
        <rect x="3" y="3" width="4" height="4" rx="0.5" fill="url(#activeGradient)" opacity="0.8">
          <animate attributeName="opacity" values="0.6;1;0.6" dur="1.6s" repeatCount="indefinite"/>
        </rect>
        
        <rect x="-12" y="8" width="4" height="4" rx="0.5" fill="url(#activeGradient)" opacity="0.8">
          <animate attributeName="opacity" values="0.5;0.9;0.5" dur="2.2s" repeatCount="indefinite"/>
        </rect>
        <rect x="-2" y="8" width="4" height="4" rx="0.5" fill="url(#activeGradient)" opacity="0.8">
          <animate attributeName="opacity" values="0.4;0.8;0.4" dur="2.6s" repeatCount="indefinite"/>
        </rect>
        <rect x="8" y="8" width="4" height="4" rx="0.5" fill="url(#activeGradient)" opacity="0.8">
          <animate attributeName="opacity" values="0.7;1;0.7" dur="1.7s" repeatCount="indefinite"/>
        </rect>
      </g>
    </g>
    
    <!-- 统计信息 -->
    <g transform="translate(0, 20)">
      <rect x="-12" y="0" width="24" height="6" rx="3" fill="url(#usageEpic)" opacity="0.8"/>
      <text x="0" y="4" text-anchor="middle" fill="#ffffff" font-family="Arial, sans-serif" font-size="4" font-weight="bold">365天</text>
    </g>
    
    <!-- 网格装饰 -->
    <g opacity="0.6">
      <!-- 外围小网格 -->
      <g transform="translate(-25, -20)">
        <rect x="0" y="0" width="8" height="8" rx="1" fill="none" stroke="url(#gridGradient)" stroke-width="0.5"/>
        <rect x="1" y="1" width="2" height="2" rx="0.2" fill="url(#activeGradient)" opacity="0.7">
          <animate attributeName="opacity" values="0.5;1;0.5" dur="3s" repeatCount="indefinite"/>
        </rect>
        <rect x="5" y="1" width="2" height="2" rx="0.2" fill="url(#activeGradient)" opacity="0.7">
          <animate attributeName="opacity" values="0.4;0.8;0.4" dur="3.5s" repeatCount="indefinite"/>
        </rect>
        <rect x="1" y="5" width="2" height="2" rx="0.2" fill="url(#activeGradient)" opacity="0.7">
          <animate attributeName="opacity" values="0.6;1;0.6" dur="2.8s" repeatCount="indefinite"/>
        </rect>
      </g>
      
      <g transform="translate(17, -18)">
        <rect x="0" y="0" width="6" height="6" rx="0.8" fill="none" stroke="url(#gridGradient)" stroke-width="0.5"/>
        <rect x="1" y="1" width="1.5" height="1.5" rx="0.2" fill="url(#activeGradient)" opacity="0.7">
          <animate attributeName="opacity" values="0.3;0.7;0.3" dur="2.5s" repeatCount="indefinite"/>
        </rect>
        <rect x="3.5" y="3.5" width="1.5" height="1.5" rx="0.2" fill="url(#activeGradient)" opacity="0.7">
          <animate attributeName="opacity" values="0.5;0.9;0.5" dur="3.2s" repeatCount="indefinite"/>
        </rect>
      </g>
      
      <g transform="translate(-22, 15)">
        <rect x="0" y="0" width="5" height="5" rx="0.6" fill="none" stroke="url(#gridGradient)" stroke-width="0.5"/>
        <rect x="0.5" y="0.5" width="1.2" height="1.2" rx="0.1" fill="url(#activeGradient)" opacity="0.7">
          <animate attributeName="opacity" values="0.4;0.8;0.4" dur="2.7s" repeatCount="indefinite"/>
        </rect>
        <rect x="3.3" y="0.5" width="1.2" height="1.2" rx="0.1" fill="url(#activeGradient)" opacity="0.7">
          <animate attributeName="opacity" values="0.6;1;0.6" dur="2.1s" repeatCount="indefinite"/>
        </rect>
        <rect x="0.5" y="3.3" width="1.2" height="1.2" rx="0.1" fill="url(#activeGradient)" opacity="0.7">
          <animate attributeName="opacity" values="0.3;0.7;0.3" dur="3.8s" repeatCount="indefinite"/>
        </rect>
      </g>
    </g>
    
    <!-- 网格标识 -->
    <g transform="translate(0, 28)">
      <rect x="-15" y="0" width="30" height="6" rx="3" fill="url(#activeGradient)" opacity="0.8"/>
      <text x="0" y="4" text-anchor="middle" fill="#ffffff" font-family="Arial, sans-serif" font-size="5" font-weight="bold">GRID MASTER</text>
    </g>
  </g>
  
  <!-- 底部华丽标识 -->
  <circle cx="64" cy="115" r="12" fill="none" stroke="url(#usageEpic)" stroke-width="2.5" opacity="0.7"/>
  <circle cx="64" cy="115" r="8" fill="none" stroke="#7B1FA2" stroke-width="1.5" opacity="0.9"/>
  <text x="64" y="119" text-anchor="middle" fill="#1A0033" font-family="Arial, sans-serif" font-size="8" font-weight="bold" opacity="0.9">网格</text>
</svg>
