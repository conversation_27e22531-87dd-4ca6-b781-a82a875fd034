<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 记账类稀有级渐变 - 奢华金属 -->
    <linearGradient id="accountingRare" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F39C12;stop-opacity:1" />
      <stop offset="25%" style="stop-color:#E67E22;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#D35400;stop-opacity:1" />
      <stop offset="75%" style="stop-color:#E67E22;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#F39C12;stop-opacity:1" />
    </linearGradient>
    
    <!-- 奢华金属光泽效果 -->
    <filter id="luxuryMetallic">
      <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
      <feOffset dx="0" dy="2" result="offsetBlur"/>
      <feFlood flood-color="#ffffff" flood-opacity="0.4"/>
      <feComposite in="SourceGraphic" in2="offsetBlur" operator="over"/>
      <feGaussianBlur stdDeviation="4" result="blur2"/>
      <feMerge>
        <feMergeNode in="blur2"/>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    
    <!-- 边缘高光升级 -->
    <filter id="premiumHighlight">
      <feGaussianBlur stdDeviation="1.5" result="coloredBlur"/>
      <feMorphology operator="dilate" radius="1"/>
      <feColorMatrix type="matrix" values="1.2 1.2 1.2 0 0  1.2 1.2 1.2 0 0  1.2 1.2 1.2 0 0  0 0 0 1 0"/>
    </filter>
    
    <!-- 标签立体效果 -->
    <filter id="tagDepth">
      <feGaussianBlur stdDeviation="1" result="blur"/>
      <feOffset dx="1" dy="2" result="offset"/>
      <feFlood flood-color="#A0522D" flood-opacity="0.3"/>
      <feComposite in="SourceGraphic" in2="offset" operator="over"/>
    </filter>
  </defs>
  
  <!-- 外圈奢华多层边框 -->
  <circle cx="64" cy="64" r="62" 
          fill="none" 
          stroke="url(#accountingRare)" 
          stroke-width="0.8" 
          opacity="0.3"/>
  <circle cx="64" cy="64" r="58" 
          fill="none" 
          stroke="url(#accountingRare)" 
          stroke-width="1.5" 
          opacity="0.5"/>
  <circle cx="64" cy="64" r="54" 
          fill="none" 
          stroke="url(#accountingRare)" 
          stroke-width="2.5" 
          opacity="0.7"/>
  
  <!-- 主体背景 - 奢华金属质感 -->
  <circle cx="64" cy="64" r="48" 
          fill="url(#accountingRare)" 
          filter="url(#luxuryMetallic)"/>
  
  <!-- 内圈精美装饰纹样 -->
  <circle cx="64" cy="64" r="40" 
          fill="none" 
          stroke="#fff" 
          stroke-width="1.8" 
          opacity="0.7"/>
  <circle cx="64" cy="64" r="34" 
          fill="none" 
          stroke="#fff" 
          stroke-width="1" 
          opacity="0.5"/>
  <circle cx="64" cy="64" r="28" 
          fill="none" 
          stroke="#fff" 
          stroke-width="0.6" 
          opacity="0.3"/>
  
  <!-- 传统云纹装饰 -->
  <g transform="translate(64, 64)" opacity="0.25">
    <path d="M-24,-24 Q-18,-30 -12,-24 Q-6,-18 0,-24 Q6,-30 12,-24 Q18,-18 24,-24" 
          fill="none" 
          stroke="#fff" 
          stroke-width="1.5"/>
    <path d="M-24,24 Q-18,18 -12,24 Q-6,30 0,24 Q6,18 12,24 Q18,30 24,24" 
          fill="none" 
          stroke="#fff" 
          stroke-width="1.5"/>
    
    <!-- 回纹装饰 -->
    <g stroke="#fff" stroke-width="1" fill="none" opacity="0.6">
      <rect x="-26" y="-2" width="4" height="4"/>
      <rect x="-18" y="-2" width="4" height="4"/>
      <rect x="-10" y="-2" width="4" height="4"/>
      <rect x="14" y="-2" width="4" height="4"/>
      <rect x="22" y="-2" width="4" height="4"/>
    </g>
  </g>
  
  <!-- 分类标签图案 - 奢华设计 -->
  <g transform="translate(64, 64)">
    <!-- 中心主标签 - 立体金属效果 -->
    <rect x="-14" y="-10" width="28" height="16" rx="3" 
          fill="#FFFACD" 
          filter="url(#tagDepth)"/>
    <rect x="-14" y="-10" width="28" height="16" rx="3" 
          fill="none" 
          stroke="#B8860B" 
          stroke-width="1.5" 
          opacity="0.8"/>
    <text x="0" y="-1" 
          text-anchor="middle" 
          fill="#8B4513" 
          font-family="Arial, sans-serif" 
          font-size="9" 
          font-weight="bold">分类</text>
    
    <!-- 周围分类标签 - 精美设计 -->
    <g opacity="0.9">
      <!-- 上方标签 - 餐饮 -->
      <rect x="-10" y="-24" width="20" height="10" rx="2" 
            fill="#FFFACD" 
            filter="url(#tagDepth)"/>
      <rect x="-10" y="-24" width="20" height="10" rx="2" 
            fill="none" 
            stroke="#B8860B" 
            stroke-width="1" 
            opacity="0.7"/>
      <text x="0" y="-16" 
            text-anchor="middle" 
            fill="#8B4513" 
            font-family="Arial, sans-serif" 
            font-size="7" 
            font-weight="600">餐饮</text>
      
      <!-- 左方标签 - 交通 -->
      <rect x="-28" y="-5" width="20" height="10" rx="2" 
            fill="#FFFACD" 
            filter="url(#tagDepth)"/>
      <rect x="-28" y="-5" width="20" height="10" rx="2" 
            fill="none" 
            stroke="#B8860B" 
            stroke-width="1" 
            opacity="0.7"/>
      <text x="-18" y="2" 
            text-anchor="middle" 
            fill="#8B4513" 
            font-family="Arial, sans-serif" 
            font-size="7" 
            font-weight="600">交通</text>
      
      <!-- 右方标签 - 购物 -->
      <rect x="8" y="-5" width="20" height="10" rx="2" 
            fill="#FFFACD" 
            filter="url(#tagDepth)"/>
      <rect x="8" y="-5" width="20" height="10" rx="2" 
            fill="none" 
            stroke="#B8860B" 
            stroke-width="1" 
            opacity="0.7"/>
      <text x="18" y="2" 
            text-anchor="middle" 
            fill="#8B4513" 
            font-family="Arial, sans-serif" 
            font-size="7" 
            font-weight="600">购物</text>
      
      <!-- 下方标签 - 娱乐 -->
      <rect x="-10" y="14" width="20" height="10" rx="2" 
            fill="#FFFACD" 
            filter="url(#tagDepth)"/>
      <rect x="-10" y="14" width="20" height="10" rx="2" 
            fill="none" 
            stroke="#B8860B" 
            stroke-width="1" 
            opacity="0.7"/>
      <text x="0" y="21" 
            text-anchor="middle" 
            fill="#8B4513" 
            font-family="Arial, sans-serif" 
            font-size="7" 
            font-weight="600">娱乐</text>
      
      <!-- 额外标签 - 医疗 -->
      <rect x="-22" y="12" width="16" height="8" rx="1.5" 
            fill="#FFFACD" 
            opacity="0.8"/>
      <text x="-14" y="17" 
            text-anchor="middle" 
            fill="#8B4513" 
            font-family="Arial, sans-serif" 
            font-size="6" 
            font-weight="500">医疗</text>
    </g>
    
    <!-- 精美连接线 -->
    <g stroke="#B8860B" stroke-width="1.2" opacity="0.6">
      <line x1="0" y1="-10" x2="0" y2="-14"/>
      <line x1="-14" y1="-2" x2="-18" y2="-2"/>
      <line x1="14" y1="-2" x2="18" y2="-2"/>
      <line x1="0" y1="6" x2="0" y2="10"/>
    </g>
    
    <!-- 装饰性小图标 -->
    <g fill="#B8860B" opacity="0.4">
      <circle cx="-30" cy="-20" r="1.5"/>
      <circle cx="30" cy="-20" r="1.5"/>
      <circle cx="-30" cy="20" r="1.5"/>
      <circle cx="30" cy="20" r="1.5"/>
    </g>
  </g>
  
  <!-- 底部奢华标识 -->
  <circle cx="64" cy="108" r="10" 
          fill="none" 
          stroke="url(#accountingRare)" 
          stroke-width="2" 
          opacity="0.6"/>
  <circle cx="64" cy="108" r="6" 
          fill="none" 
          stroke="#F39C12" 
          stroke-width="1" 
          opacity="0.8"/>
  <text x="64" y="112" 
        text-anchor="middle" 
        fill="#8B4513" 
        font-family="Arial, sans-serif" 
        font-size="8" 
        font-weight="bold"
        opacity="0.9">达人</text>
</svg>
