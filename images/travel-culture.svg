<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 旅行类稀有级渐变 -->
    <linearGradient id="travelRare" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#42A5F5" />
      <stop offset="25%" style="stop-color:#2196F3" />
      <stop offset="50%" style="stop-color:#1976D2" />
      <stop offset="75%" style="stop-color:#1565C0" />
      <stop offset="100%" style="stop-color:#0D47A1" />
    </linearGradient>
    
    <!-- 建筑渐变 -->
    <linearGradient id="buildingGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#D7CCC8" />
      <stop offset="100%" style="stop-color:#8D6E63" />
    </linearGradient>
    
    <!-- 艺术品渐变 -->
    <radialGradient id="artGradient" cx="30%" cy="30%" r="70%">
      <stop offset="0%" style="stop-color:#FFD54F" />
      <stop offset="50%" style="stop-color:#FFC107" />
      <stop offset="100%" style="stop-color:#FF9800" />
    </radialGradient>
    
    <!-- 文字渐变 -->
    <linearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8E24AA" />
      <stop offset="100%" style="stop-color:#5E35B1" />
    </linearGradient>
    
    <!-- 装饰渐变 -->
    <linearGradient id="decorGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#E91E63" />
      <stop offset="100%" style="stop-color:#AD1457" />
    </linearGradient>
  </defs>
  
  <!-- 发光效果 - 多层模拟 -->
  <circle cx="64" cy="64" r="52" fill="url(#travelRare)" opacity="0.1"/>
  <circle cx="64" cy="64" r="50" fill="url(#travelRare)" opacity="0.2"/>
  
  <!-- 外圈边框 -->
  <circle cx="64" cy="64" r="58" fill="none" stroke="url(#travelRare)" stroke-width="2" opacity="0.6"/>
  <circle cx="64" cy="64" r="54" fill="none" stroke="#ffffff" stroke-width="1" opacity="0.4"/>
  
  <!-- 主体背景 -->
  <circle cx="64" cy="64" r="48" fill="url(#travelRare)"/>
  
  <!-- 内圈装饰 -->
  <circle cx="64" cy="64" r="40" fill="none" stroke="#ffffff" stroke-width="1.5" opacity="0.6"/>
  <circle cx="64" cy="64" r="34" fill="none" stroke="#ffffff" stroke-width="1" opacity="0.4"/>
  
  <!-- 文化图案 -->
  <g transform="translate(64, 64)">
    <!-- 古典建筑 - 多层发光 -->
    <g transform="translate(0, -8)">
      <!-- 建筑发光底层 -->
      <rect x="-12" y="0" width="24" height="16" rx="2" fill="url(#buildingGradient)" opacity="0.3"/>
      <rect x="-10" y="2" width="20" height="14" rx="1" fill="url(#buildingGradient)"/>
      
      <!-- 柱子 -->
      <rect x="-8" y="2" width="2" height="14" fill="#A1887F"/>
      <rect x="-3" y="2" width="2" height="14" fill="#A1887F"/>
      <rect x="2" y="2" width="2" height="14" fill="#A1887F"/>
      <rect x="7" y="2" width="2" height="14" fill="#A1887F"/>
      
      <!-- 柱头装饰 -->
      <rect x="-9" y="1" width="4" height="1" rx="0.5" fill="#D7CCC8"/>
      <rect x="-4" y="1" width="4" height="1" rx="0.5" fill="#D7CCC8"/>
      <rect x="1" y="1" width="4" height="1" rx="0.5" fill="#D7CCC8"/>
      <rect x="6" y="1" width="4" height="1" rx="0.5" fill="#D7CCC8"/>
      
      <!-- 三角楣 -->
      <path d="M-12,2 L0,-6 L12,2 Z" fill="url(#buildingGradient)" opacity="0.9"/>
      <path d="M-10,2 L0,-4 L10,2 Z" fill="#A1887F"/>
      
      <!-- 楣饰 -->
      <circle cx="0" cy="-2" r="2" fill="url(#artGradient)" opacity="0.8">
        <animate attributeName="opacity" values="0.6;1;0.6" dur="3s" repeatCount="indefinite"/>
      </circle>
    </g>
    
    <!-- 艺术品展示 -->
    <g transform="translate(-18, 4)">
      <!-- 画框 -->
      <rect x="0" y="0" width="8" height="6" rx="0.5" fill="#8D6E63"/>
      <rect x="0.5" y="0.5" width="7" height="5" rx="0.3" fill="#FFFFFF"/>
      
      <!-- 画作内容 -->
      <rect x="1" y="1" width="6" height="4" fill="url(#artGradient)"/>
      <ellipse cx="3" cy="2.5" rx="1.5" ry="1" fill="#FFD54F"/>
      <path d="M1.5,4 Q3,3 4.5,4 Q5.5,3.5 6.5,4" stroke="#FF9800" stroke-width="0.5" fill="none"/>
    </g>
    
    <!-- 雕塑 -->
    <g transform="translate(16, 6)">
      <!-- 雕塑底座 -->
      <rect x="-2" y="4" width="4" height="2" rx="0.5" fill="#8D6E63"/>
      
      <!-- 雕塑主体 -->
      <ellipse cx="0" cy="2" rx="1.5" ry="2" fill="url(#buildingGradient)" opacity="0.3"/>
      <ellipse cx="0" cy="2" rx="1.2" ry="1.8" fill="url(#buildingGradient)"/>
      
      <!-- 雕塑细节 -->
      <circle cx="0" cy="0" r="1" fill="#A1887F"/>
      <ellipse cx="0" cy="3" rx="1" ry="1.5" fill="#A1887F"/>
    </g>
    
    <!-- 文字符号 -->
    <g transform="translate(-8, 12)" opacity="0.8">
      <!-- 古文字 -->
      <text x="0" y="0" fill="url(#textGradient)" font-family="serif" font-size="4" font-weight="bold">文</text>
      <text x="5" y="0" fill="url(#textGradient)" font-family="serif" font-size="4" font-weight="bold">化</text>
      <text x="10" y="0" fill="url(#textGradient)" font-family="serif" font-size="4" font-weight="bold">遗</text>
      <text x="15" y="0" fill="url(#textGradient)" font-family="serif" font-size="4" font-weight="bold">产</text>
    </g>
    
    <!-- 装饰图案 -->
    <g opacity="0.7">
      <!-- 传统纹样 -->
      <g transform="translate(-22, -12)" fill="url(#decorGradient)">
        <circle cx="0" cy="0" r="2" opacity="0.3">
          <animate attributeName="opacity" values="0.2;0.5;0.2" dur="2.5s" repeatCount="indefinite"/>
        </circle>
        <circle cx="0" cy="0" r="1.5" opacity="0.8"/>
        <path d="M0,-1 L0.5,0 L0,1 L-0.5,0 Z" fill="#FFFFFF" opacity="0.6"/>
      </g>
      
      <g transform="translate(22, -10)" fill="url(#decorGradient)">
        <path d="M0,-2 Q2,0 0,2 Q-2,0 0,-2 Z" opacity="0.3">
          <animate attributeName="opacity" values="0.2;0.4;0.2" dur="3s" repeatCount="indefinite"/>
        </path>
        <path d="M0,-1.5 Q1.5,0 0,1.5 Q-1.5,0 0,-1.5 Z" opacity="0.8"/>
      </g>
      
      <!-- 装饰线条 -->
      <g stroke="url(#decorGradient)" stroke-width="1" fill="none" opacity="0.6">
        <path d="M-25,8 Q-20,6 -15,8 Q-10,10 -5,8">
          <animate attributeName="opacity" values="0.4;0.8;0.4" dur="4s" repeatCount="indefinite"/>
        </path>
        <path d="M5,10 Q10,8 15,10 Q20,12 25,10">
          <animate attributeName="opacity" values="0.3;0.7;0.3" dur="3.5s" repeatCount="indefinite"/>
        </path>
      </g>
    </g>
    
    <!-- 博物馆标识 -->
    <g transform="translate(0, 20)">
      <rect x="-18" y="0" width="36" height="6" rx="3" fill="url(#artGradient)" opacity="0.8"/>
      <text x="0" y="4" text-anchor="middle" fill="#ffffff" font-family="Arial, sans-serif" font-size="5" font-weight="bold">MUSEUM</text>
    </g>
    
    <!-- 文化符号装饰 -->
    <g opacity="0.6">
      <!-- 书卷 -->
      <g transform="translate(-15, -18)">
        <rect x="0" y="0" width="6" height="1.5" rx="0.75" fill="#8D6E63"/>
        <rect x="0.5" y="0.2" width="5" height="1.1" rx="0.55" fill="#D7CCC8"/>
        <line x1="1" y1="0.4" x2="5" y2="0.4" stroke="#8D6E63" stroke-width="0.2"/>
        <line x1="1" y1="0.8" x2="4.5" y2="0.8" stroke="#8D6E63" stroke-width="0.2"/>
        <line x1="1" y1="1.2" x2="5" y2="1.2" stroke="#8D6E63" stroke-width="0.2"/>
      </g>
      
      <!-- 乐器 -->
      <g transform="translate(12, -16)">
        <ellipse cx="0" cy="0" rx="2" ry="3" fill="#8D6E63"/>
        <ellipse cx="0" cy="0" rx="1.5" ry="2.5" fill="#A1887F"/>
        <rect x="-0.2" y="-4" width="0.4" height="2" fill="#8D6E63"/>
        <circle cx="0" cy="-4" r="0.3" fill="#FFD54F"/>
      </g>
    </g>
  </g>
  
  <!-- 底部华丽标识 -->
  <circle cx="64" cy="110" r="12" fill="none" stroke="url(#travelRare)" stroke-width="2.5" opacity="0.7"/>
  <circle cx="64" cy="110" r="8" fill="none" stroke="#42A5F5" stroke-width="1.5" opacity="0.9"/>
  <text x="64" y="114" text-anchor="middle" fill="#0D47A1" font-family="Arial, sans-serif" font-size="8" font-weight="bold" opacity="0.9">文化</text>
</svg>
