<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 旅行类稀有级渐变 -->
    <linearGradient id="travelRare" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#42A5F5" />
      <stop offset="25%" style="stop-color:#2196F3" />
      <stop offset="50%" style="stop-color:#1976D2" />
      <stop offset="75%" style="stop-color:#1565C0" />
      <stop offset="100%" style="stop-color:#0D47A1" />
    </linearGradient>
    
    <!-- 餐具渐变 -->
    <linearGradient id="utensilGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#E0E0E0" />
      <stop offset="100%" style="stop-color:#9E9E9E" />
    </linearGradient>
    
    <!-- 食物渐变 -->
    <radialGradient id="foodGradient" cx="30%" cy="30%" r="70%">
      <stop offset="0%" style="stop-color:#FFD54F" />
      <stop offset="50%" style="stop-color:#FFC107" />
      <stop offset="100%" style="stop-color:#FF9800" />
    </radialGradient>
    
    <!-- 盘子渐变 -->
    <radialGradient id="plateGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#FFFFFF" />
      <stop offset="80%" style="stop-color:#F5F5F5" />
      <stop offset="100%" style="stop-color:#E0E0E0" />
    </radialGradient>
    
    <!-- 蒸汽渐变 -->
    <linearGradient id="steamGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#FFFFFF" opacity="0.8" />
      <stop offset="100%" style="stop-color:#E3F2FD" opacity="0.2" />
    </linearGradient>
  </defs>
  
  <!-- 发光效果 - 多层模拟 -->
  <circle cx="64" cy="64" r="52" fill="url(#travelRare)" opacity="0.1"/>
  <circle cx="64" cy="64" r="50" fill="url(#travelRare)" opacity="0.2"/>
  
  <!-- 外圈边框 -->
  <circle cx="64" cy="64" r="58" fill="none" stroke="url(#travelRare)" stroke-width="2" opacity="0.6"/>
  <circle cx="64" cy="64" r="54" fill="none" stroke="#ffffff" stroke-width="1" opacity="0.4"/>
  
  <!-- 主体背景 -->
  <circle cx="64" cy="64" r="48" fill="url(#travelRare)"/>
  
  <!-- 内圈装饰 -->
  <circle cx="64" cy="64" r="40" fill="none" stroke="#ffffff" stroke-width="1.5" opacity="0.6"/>
  <circle cx="64" cy="64" r="34" fill="none" stroke="#ffffff" stroke-width="1" opacity="0.4"/>
  
  <!-- 美食图案 -->
  <g transform="translate(64, 64)">
    <!-- 盘子 - 多层发光 -->
    <ellipse cx="0" cy="2" rx="20" ry="4" fill="url(#plateGradient)" opacity="0.3"/>
    <ellipse cx="0" cy="2" rx="18" ry="3.5" fill="url(#plateGradient)"/>
    <ellipse cx="0" cy="0" rx="16" ry="2" fill="url(#plateGradient)"/>
    
    <!-- 主菜 - 牛排 -->
    <g transform="translate(-6, -2)">
      <ellipse cx="0" cy="0" rx="6" ry="4" fill="#8D4E2A" opacity="0.3"/>
      <ellipse cx="0" cy="0" rx="5" ry="3" fill="#8D4E2A"/>
      <ellipse cx="1" cy="-0.5" rx="2" ry="1" fill="#A0522D"/>
      <ellipse cx="-1" cy="0.5" rx="1.5" ry="0.8" fill="#CD853F"/>
    </g>
    
    <!-- 配菜 - 蔬菜 -->
    <g transform="translate(8, -1)">
      <!-- 胡萝卜 -->
      <ellipse cx="0" cy="0" rx="2" ry="1" fill="#FF9800"/>
      <ellipse cx="0" cy="0" rx="1.5" ry="0.7" fill="#FFB74D"/>
      
      <!-- 豌豆 -->
      <circle cx="-3" cy="1" r="1" fill="#4CAF50"/>
      <circle cx="-1" cy="2" r="0.8" fill="#66BB6A"/>
      <circle cx="1" cy="1.5" r="0.9" fill="#4CAF50"/>
    </g>
    
    <!-- 餐具 - 叉子 -->
    <g transform="translate(-18, -8)">
      <!-- 叉子发光底层 -->
      <rect x="0" y="0" width="1.5" height="16" rx="0.75" fill="url(#utensilGradient)" opacity="0.3"/>
      <rect x="0.25" y="0" width="1" height="15" rx="0.5" fill="url(#utensilGradient)"/>
      
      <!-- 叉齿 -->
      <rect x="-0.5" y="0" width="0.5" height="4" rx="0.25" fill="url(#utensilGradient)"/>
      <rect x="0.25" y="0" width="0.5" height="4" rx="0.25" fill="url(#utensilGradient)"/>
      <rect x="1" y="0" width="0.5" height="4" rx="0.25" fill="url(#utensilGradient)"/>
      <rect x="1.75" y="0" width="0.5" height="4" rx="0.25" fill="url(#utensilGradient)"/>
    </g>
    
    <!-- 餐具 - 刀子 -->
    <g transform="translate(18, -8)">
      <!-- 刀子发光底层 -->
      <rect x="0" y="0" width="1.2" height="16" rx="0.6" fill="url(#utensilGradient)" opacity="0.3"/>
      <rect x="0.1" y="0" width="1" height="15" rx="0.5" fill="url(#utensilGradient)"/>
      
      <!-- 刀刃 -->
      <rect x="0.2" y="0" width="0.8" height="8" rx="0.4" fill="#E0E0E0"/>
      <rect x="0.3" y="1" width="0.6" height="6" rx="0.3" fill="#F5F5F5"/>
    </g>
    
    <!-- 蒸汽效果 -->
    <g opacity="0.6">
      <ellipse cx="-4" cy="-8" rx="1" ry="3" fill="url(#steamGradient)">
        <animate attributeName="ry" values="3;5;3" dur="2s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.6;0.2;0.6" dur="2s" repeatCount="indefinite"/>
      </ellipse>
      <ellipse cx="0" cy="-10" rx="0.8" ry="4" fill="url(#steamGradient)">
        <animate attributeName="ry" values="4;6;4" dur="2.5s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.5;0.1;0.5" dur="2.5s" repeatCount="indefinite"/>
      </ellipse>
      <ellipse cx="4" cy="-9" rx="0.9" ry="3.5" fill="url(#steamGradient)">
        <animate attributeName="ry" values="3.5;5.5;3.5" dur="2.2s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.4;0.1;0.4" dur="2.2s" repeatCount="indefinite"/>
      </ellipse>
    </g>
    
    <!-- 调料瓶 -->
    <g transform="translate(-12, 8)">
      <rect x="0" y="0" width="3" height="8" rx="1.5" fill="#FFB74D" opacity="0.8"/>
      <rect x="0.5" y="1" width="2" height="6" rx="1" fill="#FFC107"/>
      <circle cx="1.5" cy="-1" r="1" fill="#E0E0E0"/>
      <circle cx="1.5" cy="-1" r="0.5" fill="#9E9E9E"/>
      
      <!-- 调料颗粒 -->
      <circle cx="1.2" cy="3" r="0.2" fill="#D32F2F"/>
      <circle cx="1.8" cy="4" r="0.15" fill="#D32F2F"/>
      <circle cx="1.5" cy="5" r="0.18" fill="#D32F2F"/>
    </g>
    
    <!-- 酒杯 -->
    <g transform="translate(12, 6)">
      <!-- 杯子发光底层 -->
      <path d="M0,0 Q-2,4 -1,8 L1,8 Q2,4 0,0 Z" fill="#E3F2FD" opacity="0.3"/>
      <path d="M0,0 Q-1.5,3.5 -0.8,7 L0.8,7 Q1.5,3.5 0,0 Z" fill="#E3F2FD"/>
      
      <!-- 杯脚 -->
      <rect x="-0.5" y="7" width="1" height="3" fill="#E0E0E0"/>
      <ellipse cx="0" cy="10" rx="2" ry="0.5" fill="#E0E0E0"/>
      
      <!-- 液体 -->
      <path d="M0,2 Q-1,5 -0.6,6 L0.6,6 Q1,5 0,2 Z" fill="#E91E63" opacity="0.7"/>
    </g>
    
    <!-- 美食标识 -->
    <g transform="translate(0, 22)">
      <rect x="-15" y="0" width="30" height="6" rx="3" fill="url(#foodGradient)" opacity="0.8"/>
      <text x="0" y="4" text-anchor="middle" fill="#ffffff" font-family="Arial, sans-serif" font-size="5" font-weight="bold">GOURMET</text>
    </g>
    
    <!-- 装饰星星 -->
    <g fill="#FFD54F" opacity="0.7">
      <path d="M-22,-15 L-21,-13 L-19,-13 L-20.5,-11.5 L-20,-10 L-22,-11 L-24,-10 L-23.5,-11.5 L-25,-13 L-23,-13 Z">
        <animate attributeName="opacity" values="0.5;1;0.5" dur="2s" repeatCount="indefinite"/>
      </path>
      <path d="M22,-12 L22.5,-11 L23.5,-11 L22.8,-10.2 L23,-9 L22,-9.5 L21,-9 L21.2,-10.2 L20.5,-11 L21.5,-11 Z">
        <animate attributeName="opacity" values="0.4;0.9;0.4" dur="2.5s" repeatCount="indefinite"/>
      </path>
    </g>
  </g>
  
  <!-- 底部华丽标识 -->
  <circle cx="64" cy="110" r="12" fill="none" stroke="url(#travelRare)" stroke-width="2.5" opacity="0.7"/>
  <circle cx="64" cy="110" r="8" fill="none" stroke="#42A5F5" stroke-width="1.5" opacity="0.9"/>
  <text x="64" y="114" text-anchor="middle" fill="#0D47A1" font-family="Arial, sans-serif" font-size="8" font-weight="bold" opacity="0.9">美食</text>
</svg>
