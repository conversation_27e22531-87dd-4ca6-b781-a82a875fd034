<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 记账类稀有级渐变 -->
    <linearGradient id="accountingRare" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F39C12;stop-opacity:1" />
      <stop offset="25%" style="stop-color:#E67E22;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#D35400;stop-opacity:1" />
      <stop offset="75%" style="stop-color:#E67E22;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#F39C12;stop-opacity:1" />
    </linearGradient>
    
    <!-- 日历纸张渐变 -->
    <linearGradient id="calendarPaper" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFFEF7;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#FFF8DC;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#F5F5DC;stop-opacity:1" />
    </linearGradient>
    
    <!-- 链条金属渐变 -->
    <linearGradient id="chainMetal" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#B8860B;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FFD700;stop-opacity:1" />
    </linearGradient>
    
    <!-- 奢华金属光泽 -->
    <filter id="luxuryMetallic">
      <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
      <feOffset dx="0" dy="2" result="offsetBlur"/>
      <feFlood flood-color="#ffffff" flood-opacity="0.4"/>
      <feComposite in="SourceGraphic" in2="offsetBlur" operator="over"/>
      <feGaussianBlur stdDeviation="4" result="blur2"/>
      <feMerge>
        <feMergeNode in="blur2"/>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    
    <!-- 日历立体效果 -->
    <filter id="calendarDepth">
      <feGaussianBlur stdDeviation="1" result="blur"/>
      <feOffset dx="2" dy="3" result="offset"/>
      <feFlood flood-color="#DDD" flood-opacity="0.5"/>
      <feComposite in="SourceGraphic" in2="offset" operator="over"/>
    </filter>
  </defs>
  
  <!-- 外圈奢华边框 -->
  <circle cx="64" cy="64" r="62" fill="none" stroke="url(#accountingRare)" stroke-width="0.8" opacity="0.3"/>
  <circle cx="64" cy="64" r="58" fill="none" stroke="url(#accountingRare)" stroke-width="1.5" opacity="0.5"/>
  <circle cx="64" cy="64" r="54" fill="none" stroke="url(#accountingRare)" stroke-width="2.5" opacity="0.7"/>
  
  <!-- 主体背景 -->
  <circle cx="64" cy="64" r="48" fill="url(#accountingRare)" filter="url(#luxuryMetallic)"/>
  
  <!-- 内圈装饰 -->
  <circle cx="64" cy="64" r="40" fill="none" stroke="#fff" stroke-width="1.8" opacity="0.7"/>
  <circle cx="64" cy="64" r="34" fill="none" stroke="#fff" stroke-width="1" opacity="0.5"/>
  
  <!-- 链条纹样装饰 -->
  <g transform="translate(64, 64)" opacity="0.3">
    <path d="M-30,-15 Q-25,-20 -20,-15 Q-15,-10 -10,-15 Q-5,-20 0,-15 Q5,-10 10,-15 Q15,-20 20,-15 Q25,-10 30,-15" 
          fill="none" stroke="#fff" stroke-width="2"/>
    <path d="M-30,15 Q-25,10 -20,15 Q-15,20 -10,15 Q-5,10 0,15 Q5,20 10,15 Q15,10 20,15 Q25,20 30,15" 
          fill="none" stroke="#fff" stroke-width="2"/>
  </g>
  
  <!-- 日历图案 - 奢华设计 -->
  <g transform="translate(64, 64)">
    <!-- 日历主体 -->
    <rect x="-18" y="-16" width="36" height="28" rx="4" 
          fill="url(#calendarPaper)" 
          filter="url(#calendarDepth)"/>
    
    <!-- 日历边框 -->
    <rect x="-18" y="-16" width="36" height="28" rx="4" 
          fill="none" 
          stroke="#B8860B" 
          stroke-width="1.5" 
          opacity="0.8"/>
    
    <!-- 日历顶部装订环 -->
    <rect x="-16" y="-20" width="32" height="6" rx="3" 
          fill="url(#chainMetal)" 
          opacity="0.9"/>
    
    <!-- 装订孔 -->
    <circle cx="-10" cy="-17" r="1.5" fill="#8B4513" opacity="0.8"/>
    <circle cx="0" cy="-17" r="1.5" fill="#8B4513" opacity="0.8"/>
    <circle cx="10" cy="-17" r="1.5" fill="#8B4513" opacity="0.8"/>
    
    <!-- 月份标题 -->
    <rect x="-16" y="-12" width="32" height="6" rx="1" 
          fill="#F39C12" 
          opacity="0.8"/>
    <text x="0" y="-7" 
          text-anchor="middle" 
          fill="#fff" 
          font-family="Arial, sans-serif" 
          font-size="7" 
          font-weight="bold">七月</text>
    
    <!-- 日期网格 -->
    <g stroke="#B8860B" stroke-width="0.5" opacity="0.6">
      <!-- 横线 -->
      <line x1="-16" y1="-4" x2="16" y2="-4"/>
      <line x1="-16" y1="0" x2="16" y2="0"/>
      <line x1="-16" y1="4" x2="16" y2="4"/>
      <line x1="-16" y1="8" x2="16" y2="8"/>
      
      <!-- 竖线 -->
      <line x1="-12" y1="-6" x2="-12" y2="10"/>
      <line x1="-6" y1="-6" x2="-6" y2="10"/>
      <line x1="0" y1="-6" x2="0" y2="10"/>
      <line x1="6" y1="-6" x2="6" y2="10"/>
      <line x1="12" y1="-6" x2="12" y2="10"/>
    </g>
    
    <!-- 日期数字 -->
    <g fill="#8B4513" font-family="Arial, sans-serif" font-size="5" font-weight="600">
      <text x="-14" y="-1" text-anchor="middle">1</text>
      <text x="-9" y="-1" text-anchor="middle">2</text>
      <text x="-3" y="-1" text-anchor="middle">3</text>
      <text x="3" y="-1" text-anchor="middle">4</text>
      <text x="9" y="-1" text-anchor="middle">5</text>
      <text x="14" y="-1" text-anchor="middle">6</text>
      
      <text x="-14" y="3" text-anchor="middle">7</text>
      <text x="-9" y="3" text-anchor="middle">8</text>
      <text x="-3" y="3" text-anchor="middle">9</text>
      <text x="3" y="3" text-anchor="middle">10</text>
      <text x="9" y="3" text-anchor="middle">11</text>
      <text x="14" y="3" text-anchor="middle">12</text>
    </g>
    
    <!-- 连续标记 - 金色圆点 -->
    <g fill="#FFD700" opacity="0.9">
      <circle cx="-14" cy="1" r="1"/>
      <circle cx="-9" cy="1" r="1"/>
      <circle cx="-3" cy="1" r="1"/>
      <circle cx="3" cy="1" r="1"/>
      <circle cx="9" cy="1" r="1"/>
      <circle cx="14" cy="1" r="1"/>
      <circle cx="-14" cy="5" r="1"/>
    </g>
    
    <!-- 连接链条装饰 -->
    <g stroke="url(#chainMetal)" stroke-width="2" fill="none" opacity="0.7">
      <path d="M-12,1 Q-10.5,0 -9,1"/>
      <path d="M-7,1 Q-5.5,0 -4,1"/>
      <path d="M-1,1 Q0.5,0 2,1"/>
      <path d="M4,1 Q5.5,0 7,1"/>
      <path d="M10,1 Q11.5,0 13,1"/>
    </g>
  </g>
  
  <!-- 底部奢华标识 -->
  <circle cx="64" cy="108" r="10" fill="none" stroke="url(#accountingRare)" stroke-width="2" opacity="0.6"/>
  <circle cx="64" cy="108" r="6" fill="none" stroke="#F39C12" stroke-width="1" opacity="0.8"/>
  <text x="64" y="112" 
        text-anchor="middle" 
        fill="#8B4513" 
        font-family="Arial, sans-serif" 
        font-size="7" 
        font-weight="bold"
        opacity="0.9">连续</text>
</svg>
