<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 特殊类至尊级渐变 -->
    <linearGradient id="specialSupreme" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFD700" />
      <stop offset="12%" style="stop-color:#FFC107" />
      <stop offset="24%" style="stop-color:#FF9800" />
      <stop offset="36%" style="stop-color:#FF5722" />
      <stop offset="48%" style="stop-color:#E91E63" />
      <stop offset="60%" style="stop-color:#9C27B0" />
      <stop offset="72%" style="stop-color:#673AB7" />
      <stop offset="84%" style="stop-color:#3F51B5" />
      <stop offset="96%" style="stop-color:#2196F3" />
      <stop offset="100%" style="stop-color:#FFD700" />
    </linearGradient>
    
    <!-- 至尊彩虹光环效果 -->
    <linearGradient id="supremeAura" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ff6b6b" opacity="1" />
      <stop offset="6%" style="stop-color:#ff9ff3" opacity="1" />
      <stop offset="12%" style="stop-color:#feca57" opacity="1" />
      <stop offset="18%" style="stop-color:#48dbfb" opacity="1" />
      <stop offset="24%" style="stop-color:#00ffff" opacity="1" />
      <stop offset="30%" style="stop-color:#0abde3" opacity="1" />
      <stop offset="36%" style="stop-color:#006ba6" opacity="1" />
      <stop offset="42%" style="stop-color:#8e44ad" opacity="1" />
      <stop offset="48%" style="stop-color:#c44569" opacity="1" />
      <stop offset="54%" style="stop-color:#e74c3c" opacity="1" />
      <stop offset="60%" style="stop-color:#f39c12" opacity="1" />
      <stop offset="66%" style="stop-color:#2ecc71" opacity="1" />
      <stop offset="72%" style="stop-color:#3498db" opacity="1" />
      <stop offset="78%" style="stop-color:#9b59b6" opacity="1" />
      <stop offset="84%" style="stop-color:#e67e22" opacity="1" />
      <stop offset="90%" style="stop-color:#1abc9c" opacity="1" />
      <stop offset="96%" style="stop-color:#f1c40f" opacity="1" />
      <stop offset="100%" style="stop-color:#ff6b6b" opacity="1" />
    </linearGradient>
    
    <!-- 液态黄金效果 -->
    <radialGradient id="liquidGold" cx="15%" cy="15%" r="95%">
      <stop offset="0%" style="stop-color:#ffffff" />
      <stop offset="8%" style="stop-color:#fffde7" />
      <stop offset="16%" style="stop-color:#fff9c4" />
      <stop offset="24%" style="stop-color:#fff176" />
      <stop offset="32%" style="stop-color:#ffeb3b" />
      <stop offset="40%" style="stop-color:#ffc107" />
      <stop offset="48%" style="stop-color:#ff9800" />
      <stop offset="56%" style="stop-color:#ff5722" />
      <stop offset="64%" style="stop-color:#e91e63" />
      <stop offset="72%" style="stop-color:#9c27b0" />
      <stop offset="80%" style="stop-color:#673ab7" />
      <stop offset="88%" style="stop-color:#3f51b5" />
      <stop offset="96%" style="stop-color:#2196f3" />
      <stop offset="100%" style="stop-color:#1976d2" />
    </radialGradient>
    
    <!-- 至尊皇冠渐变 -->
    <radialGradient id="supremeCrown" cx="20%" cy="20%" r="80%">
      <stop offset="0%" style="stop-color:#FFFFFF" />
      <stop offset="15%" style="stop-color:#FFD700" />
      <stop offset="30%" style="stop-color:#FFC107" />
      <stop offset="45%" style="stop-color:#FF9800" />
      <stop offset="60%" style="stop-color:#FF5722" />
      <stop offset="75%" style="stop-color:#E91E63" />
      <stop offset="90%" style="stop-color:#9C27B0" />
      <stop offset="100%" style="stop-color:#3F51B5" />
    </radialGradient>
    
    <!-- 至尊光芒渐变 -->
    <linearGradient id="supremeRay" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFFFFF" />
      <stop offset="25%" style="stop-color:#FFD700" />
      <stop offset="50%" style="stop-color:#FF9800" />
      <stop offset="75%" style="stop-color:#E91E63" />
      <stop offset="100%" style="stop-color:#9C27B0" />
    </linearGradient>
  </defs>
  
  <!-- 发光效果 - 多层模拟 -->
  <circle cx="64" cy="64" r="58" fill="url(#specialSupreme)" opacity="0.06"/>
  <circle cx="64" cy="64" r="56" fill="url(#specialSupreme)" opacity="0.09"/>
  <circle cx="64" cy="64" r="54" fill="url(#specialSupreme)" opacity="0.12"/>
  <circle cx="64" cy="64" r="52" fill="url(#specialSupreme)" opacity="0.15"/>
  <circle cx="64" cy="64" r="50" fill="url(#specialSupreme)" opacity="0.18"/>
  
  <!-- 至尊彩虹光环 - 最强版 -->
  <circle cx="64" cy="64" r="63.8" fill="none" stroke="url(#supremeAura)" stroke-width="5" opacity="1" stroke-linecap="round">
    <animate attributeName="r" values="62.5;65.1;62.5" dur="2s" repeatCount="indefinite"/>
    <animate attributeName="stroke-width" values="4.5;5.5;4.5" dur="2s" repeatCount="indefinite"/>
    <animate attributeName="opacity" values="0.9;1;0.9" dur="2s" repeatCount="indefinite"/>
  </circle>
  
  <!-- 至尊级光环 -->
  <circle cx="64" cy="64" r="59.2" fill="none" stroke="url(#specialSupreme)" stroke-width="3" opacity="1" stroke-linecap="round">
    <animate attributeName="opacity" values="0.9;1;0.9" dur="1.5s" repeatCount="indefinite"/>
    <animate attributeName="stroke-width" values="2.5;3.5;2.5" dur="1.5s" repeatCount="indefinite"/>
  </circle>
  
  <!-- 黄金折射光环 -->
  <circle cx="64" cy="64" r="55.8" fill="none" stroke="url(#liquidGold)" stroke-width="2.2" opacity="0.8" stroke-linecap="round">
    <animate attributeName="opacity" values="0.6;1;0.6" dur="1.8s" repeatCount="indefinite"/>
  </circle>
  
  <!-- 边界过渡层 -->
  <circle cx="64" cy="64" r="53.5" fill="url(#liquidGold)" opacity="0.05"/>
  <circle cx="64" cy="64" r="52.2" fill="url(#liquidGold)" opacity="0.08"/>
  <circle cx="64" cy="64" r="51" fill="url(#liquidGold)" opacity="0.12"/>
  <circle cx="64" cy="64" r="49.5" fill="url(#liquidGold)" opacity="0.16"/>
  
  <!-- 主体背景 - 精确边界 -->
  <circle cx="64" cy="64" r="48" fill="url(#liquidGold)"/>
  
  <!-- 内圈装饰 - 精细化边界 -->
  <circle cx="64" cy="64" r="42.8" fill="none" stroke="#fff" stroke-width="2" opacity="0.9" stroke-linecap="round"/>
  <circle cx="64" cy="64" r="38.5" fill="none" stroke="#fff" stroke-width="1.5" opacity="0.7" stroke-linecap="round"/>
  <circle cx="64" cy="64" r="34.8" fill="none" stroke="#fff" stroke-width="1" opacity="0.5" stroke-linecap="round"/>
  <circle cx="64" cy="64" r="31.5" fill="none" stroke="#fff" stroke-width="0.8" opacity="0.3" stroke-linecap="round"/>
  <circle cx="64" cy="64" r="28.8" fill="none" stroke="#fff" stroke-width="0.5" opacity="0.2" stroke-linecap="round"/>
  
  <!-- 至尊皇冠图案 - 特殊级设计 -->
  <g transform="translate(64, 64)">
    <!-- 至尊皇冠 - 多层发光 -->
    <g transform="translate(0, -8)">
      <!-- 皇冠发光底层 - 精细化圆角 -->
      <rect x="-18" y="8" width="36" height="12" rx="6" fill="url(#supremeCrown)" opacity="0.15"/>
      <rect x="-16.5" y="9" width="33" height="10.5" rx="5.25" fill="url(#supremeCrown)" opacity="0.25"/>
      <rect x="-15" y="10" width="30" height="9" rx="4.5" fill="url(#supremeCrown)" opacity="0.35"/>
      <rect x="-13.5" y="11" width="27" height="7.5" rx="3.75" fill="url(#supremeCrown)" opacity="0.5"/>
      
      <!-- 皇冠底座 - 优化边角 -->
      <rect x="-12" y="12" width="24" height="6" rx="3" fill="url(#supremeCrown)"/>
      
      <!-- 皇冠主体发光 - 平滑边界 -->
      <path d="M-12,12 Q-10,-15 -9.5,-16 Q-6.5,11.5 -6,12 Q-3,-20 -2.5,-21 Q-1,11.5 0,12 Q2.5,-20 3,-21 Q6,11.5 6.5,12 Q9.5,-15 10,-16 Q12,11.5 12,12 Z" fill="url(#supremeCrown)" opacity="0.2"/>
      <path d="M-11.5,12 Q-8.8,-13.5 -8.5,-14.5 Q-5.8,11.5 -5.5,12 Q-2.2,-18.5 -1.8,-19.5 Q-0.5,11.5 0.5,12 Q3.2,-18.5 3.8,-19.5 Q6.8,11.5 7.5,12 Q9.8,-13.5 10.5,-14.5 Q11.8,11.5 11.5,12 Z" fill="url(#supremeCrown)" opacity="0.4"/>
      <path d="M-11,12 Q-7.5,-12 -7,-13 Q-4.5,11.5 -4,12 Q-1.5,-17 -1,-18 Q0,11.5 1,12 Q4,-17 4.5,-18 Q7.5,11.5 8,12 Q8.5,-12 9,-13 Q11,11.5 11,12 Z" fill="url(#supremeCrown)"/>
      
      <!-- 中央最高峰发光 - 精细化尖角 -->
      <path d="M-2,-18 Q0,-28 0,-29 Q0,-28 2,-18 Q1.2,-18.2 0,-18.5 Q-1.2,-18.2 -2,-18 Z" fill="url(#supremeCrown)" opacity="0.2"/>
      <path d="M-1.8,-18 Q0,-26.5 0,-27.5 Q0,-26.5 1.8,-18 Q1,-18.2 0,-18.3 Q-1,-18.2 -1.8,-18 Z" fill="url(#supremeCrown)" opacity="0.4"/>
      <path d="M-1.5,-18 Q0,-25 0,-26 Q0,-25 1.5,-18 Q0.8,-18.1 0,-18.2 Q-0.8,-18.1 -1.5,-18 Z" fill="url(#supremeCrown)"/>
      
      <!-- 皇冠宝石 - 精细化多层发光 -->
      <!-- 中央主宝石 - 优化边界 -->
      <circle cx="0" cy="-22" r="3.5" fill="url(#supremeCrown)" opacity="0.15">
        <animate attributeName="opacity" values="0.1;0.3;0.1" dur="2s" repeatCount="indefinite"/>
      </circle>
      <circle cx="0" cy="-22" r="3" fill="url(#supremeCrown)" opacity="0.3"/>
      <circle cx="0" cy="-22" r="2.5" fill="url(#supremeCrown)" opacity="0.6"/>
      <circle cx="0" cy="-22" r="2" fill="url(#supremeCrown)"/>
      <circle cx="0" cy="-22" r="1.5" fill="#FFD700"/>
      <circle cx="-0.8" cy="-22.8" r="0.8" fill="#FFFFFF" opacity="0.8"/>
      
      <!-- 侧面宝石 -->
      <circle cx="-8.5" cy="-13.5" r="2.5" fill="url(#supremeCrown)" opacity="0.15">
        <animate attributeName="opacity" values="0.1;0.25;0.1" dur="2.2s" repeatCount="indefinite"/>
      </circle>
      <circle cx="-8.5" cy="-13.5" r="2" fill="url(#supremeCrown)" opacity="0.3"/>
      <circle cx="-8.5" cy="-13.5" r="1.5" fill="url(#supremeCrown)" opacity="0.6"/>
      <circle cx="-8.5" cy="-13.5" r="1" fill="#E91E63"/>
      <circle cx="-9" cy="-14" r="0.5" fill="#FFFFFF" opacity="0.6"/>
      
      <circle cx="8.5" cy="-13.5" r="2.5" fill="url(#supremeCrown)" opacity="0.15">
        <animate attributeName="opacity" values="0.1;0.25;0.1" dur="2.4s" repeatCount="indefinite"/>
      </circle>
      <circle cx="8.5" cy="-13.5" r="2" fill="url(#supremeCrown)" opacity="0.3"/>
      <circle cx="8.5" cy="-13.5" r="1.5" fill="url(#supremeCrown)" opacity="0.6"/>
      <circle cx="8.5" cy="-13.5" r="1" fill="#9C27B0"/>
      <circle cx="8" cy="-14" r="0.5" fill="#FFFFFF" opacity="0.6"/>
      
      <!-- 次级宝石 -->
      <circle cx="-1.8" cy="-19" r="1.8" fill="url(#supremeCrown)" opacity="0.15">
        <animate attributeName="opacity" values="0.1;0.2;0.1" dur="2.6s" repeatCount="indefinite"/>
      </circle>
      <circle cx="-1.8" cy="-19" r="1.5" fill="url(#supremeCrown)" opacity="0.4"/>
      <circle cx="-1.8" cy="-19" r="1" fill="url(#supremeCrown)" opacity="0.7"/>
      <circle cx="-1.8" cy="-19" r="0.8" fill="#4CAF50"/>
      <circle cx="-2.2" cy="-19.4" r="0.4" fill="#FFFFFF" opacity="0.5"/>
      
      <circle cx="1.8" cy="-19" r="1.8" fill="url(#supremeCrown)" opacity="0.15">
        <animate attributeName="opacity" values="0.1;0.2;0.1" dur="2.8s" repeatCount="indefinite"/>
      </circle>
      <circle cx="1.8" cy="-19" r="1.5" fill="url(#supremeCrown)" opacity="0.4"/>
      <circle cx="1.8" cy="-19" r="1" fill="url(#supremeCrown)" opacity="0.7"/>
      <circle cx="1.8" cy="-19" r="0.8" fill="#FF9800"/>
      <circle cx="1.4" cy="-19.4" r="0.4" fill="#FFFFFF" opacity="0.5"/>
    </g>
    
    <!-- 至尊标识 */
    <g transform="translate(0, 8)" opacity="0.95">
      <rect x="-15" y="-3" width="30" height="6" rx="3" fill="url(#specialSupreme)" opacity="0.9"/>
      <text x="0" y="1" text-anchor="middle" fill="#ffffff" font-family="Arial, sans-serif" font-size="4" font-weight="bold">SUPREME</text>
    </g>
    
    <!-- 至尊光芒 - 边界控制优化 -->
    <g opacity="0.9">
      <!-- 主光芒 - 12方向 -->
      <line x1="0" y1="-35" x2="0" y2="-42" stroke="url(#supremeRay)" stroke-width="4" stroke-linecap="round">
        <animate attributeName="y2" values="-42;-46;-42" dur="2s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.7;1;0.7" dur="2s" repeatCount="indefinite"/>
      </line>
      <line x1="18" y1="-30" x2="24" y2="-38" stroke="url(#supremeRay)" stroke-width="3.5" stroke-linecap="round">
        <animate attributeName="x2" values="24;28;24" dur="2.1s" repeatCount="indefinite"/>
        <animate attributeName="y2" values="-38;-42;-38" dur="2.1s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.6;0.95;0.6" dur="2.1s" repeatCount="indefinite"/>
      </line>
      <line x1="30" y1="-18" x2="38" y2="-24" stroke="url(#supremeRay)" stroke-width="3" stroke-linecap="round">
        <animate attributeName="x2" values="38;42;38" dur="2.2s" repeatCount="indefinite"/>
        <animate attributeName="y2" values="-24;-28;-24" dur="2.2s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.5;0.9;0.5" dur="2.2s" repeatCount="indefinite"/>
      </line>
      <line x1="35" y1="0" x2="42" y2="0" stroke="url(#supremeRay)" stroke-width="4" stroke-linecap="round">
        <animate attributeName="x2" values="42;46;42" dur="1.8s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.7;1;0.7" dur="1.8s" repeatCount="indefinite"/>
      </line>
      <line x1="30" y1="18" x2="38" y2="24" stroke="url(#supremeRay)" stroke-width="3" stroke-linecap="round">
        <animate attributeName="x2" values="38;42;38" dur="2.3s" repeatCount="indefinite"/>
        <animate attributeName="y2" values="24;28;24" dur="2.3s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.5;0.9;0.5" dur="2.3s" repeatCount="indefinite"/>
      </line>
      <line x1="18" y1="30" x2="24" y2="38" stroke="url(#supremeRay)" stroke-width="3.5" stroke-linecap="round">
        <animate attributeName="x2" values="24;28;24" dur="2.4s" repeatCount="indefinite"/>
        <animate attributeName="y2" values="38;42;38" dur="2.4s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.6;0.95;0.6" dur="2.4s" repeatCount="indefinite"/>
      </line>
      <line x1="0" y1="35" x2="0" y2="42" stroke="url(#supremeRay)" stroke-width="4" stroke-linecap="round">
        <animate attributeName="y2" values="42;46;42" dur="2.1s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.7;1;0.7" dur="2.1s" repeatCount="indefinite"/>
      </line>
      <line x1="-18" y1="30" x2="-24" y2="38" stroke="url(#supremeRay)" stroke-width="3.5" stroke-linecap="round">
        <animate attributeName="x2" values="-24;-28;-24" dur="2.5s" repeatCount="indefinite"/>
        <animate attributeName="y2" values="38;42;38" dur="2.5s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.6;0.95;0.6" dur="2.5s" repeatCount="indefinite"/>
      </line>
      <line x1="-30" y1="18" x2="-38" y2="24" stroke="url(#supremeRay)" stroke-width="3" stroke-linecap="round">
        <animate attributeName="x2" values="-38;-42;-38" dur="2.6s" repeatCount="indefinite"/>
        <animate attributeName="y2" values="24;28;24" dur="2.6s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.5;0.9;0.5" dur="2.6s" repeatCount="indefinite"/>
      </line>
      <line x1="-35" y1="0" x2="-42" y2="0" stroke="url(#supremeRay)" stroke-width="4" stroke-linecap="round">
        <animate attributeName="x2" values="-42;-46;-42" dur="1.9s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.7;1;0.7" dur="1.9s" repeatCount="indefinite"/>
      </line>
      <line x1="-30" y1="-18" x2="-38" y2="-24" stroke="url(#supremeRay)" stroke-width="3" stroke-linecap="round">
        <animate attributeName="x2" values="-38;-42;-38" dur="2.7s" repeatCount="indefinite"/>
        <animate attributeName="y2" values="-24;-28;-24" dur="2.7s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.5;0.9;0.5" dur="2.7s" repeatCount="indefinite"/>
      </line>
      <line x1="-18" y1="-30" x2="-24" y2="-38" stroke="url(#supremeRay)" stroke-width="3.5" stroke-linecap="round">
        <animate attributeName="x2" values="-24;-28;-24" dur="2.8s" repeatCount="indefinite"/>
        <animate attributeName="y2" values="-38;-42;-38" dur="2.8s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.6;0.95;0.6" dur="2.8s" repeatCount="indefinite"/>
      </line>
    </g>
    
    <!-- 至尊标识 -->
    <g transform="translate(0, 38)">
      <rect x="-22" y="0" width="44" height="6" rx="3" fill="url(#supremeCrown)" opacity="0.95"/>
      <text x="0" y="4" text-anchor="middle" fill="#ffffff" font-family="Arial, sans-serif" font-size="5" font-weight="bold">ULTIMATE CROWN</text>
    </g>
  </g>
  
  <!-- 粒子效果 - 至尊版 -->
  <g opacity="1">
    <circle cx="30" cy="30" r="3" fill="#FFD700" opacity="0.3">
      <animate attributeName="cy" values="30;25;30" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;0" dur="3s" repeatCount="indefinite"/>
    </circle>
    <circle cx="30" cy="30" r="2.5" fill="#FFD700">
      <animate attributeName="cy" values="30;25;30" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;0" dur="3s" repeatCount="indefinite"/>
    </circle>
    
    <circle cx="98" cy="40" r="2.8" fill="#FF9800" opacity="0.3">
      <animate attributeName="cx" values="98;93;98" dur="2.5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;0.9;0" dur="2.5s" repeatCount="indefinite"/>
    </circle>
    <circle cx="98" cy="40" r="2.3" fill="#FF9800">
      <animate attributeName="cx" values="98;93;98" dur="2.5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;0" dur="2.5s" repeatCount="indefinite"/>
    </circle>
    
    <circle cx="25" cy="98" r="3.2" fill="#E91E63" opacity="0.3">
      <animate attributeName="cy" values="98;103;98" dur="2.8s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;0.8;0" dur="2.8s" repeatCount="indefinite"/>
    </circle>
    <circle cx="25" cy="98" r="2.7" fill="#E91E63">
      <animate attributeName="cy" values="98;103;98" dur="2.8s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;0" dur="2.8s" repeatCount="indefinite"/>
    </circle>
    
    <circle cx="103" cy="88" r="2.5" fill="#9C27B0" opacity="0.3">
      <animate attributeName="cx" values="103;108;103" dur="3.2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;0.7;0" dur="3.2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="103" cy="88" r="2" fill="#9C27B0">
      <animate attributeName="cx" values="103;108;103" dur="3.2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;0" dur="3.2s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- 底部华丽标识 -->
  <circle cx="64" cy="120" r="12" fill="none" stroke="url(#specialSupreme)" stroke-width="4" opacity="1"/>
  <circle cx="64" cy="120" r="8" fill="none" stroke="#FFD700" stroke-width="3" opacity="1"/>
  <text x="64" y="124" text-anchor="middle" fill="#FF5722" font-family="Arial, sans-serif" font-size="9" font-weight="bold">至尊</text>
</svg>
