<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 传说级渐变 -->
    <linearGradient id="legendaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fdcb6e" />
      <stop offset="25%" style="stop-color:#e17055" />
      <stop offset="50%" style="stop-color:#fdcb6e" />
      <stop offset="75%" style="stop-color:#e17055" />
      <stop offset="100%" style="stop-color:#fdcb6e" />
    </linearGradient>
    
    <!-- 至尊彩虹光环效果 - 升级版 -->
    <linearGradient id="rainbowAura" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ff6b6b" opacity="0.9" />
      <stop offset="12%" style="stop-color:#ff9ff3" opacity="0.9" />
      <stop offset="24%" style="stop-color:#feca57" opacity="0.9" />
      <stop offset="36%" style="stop-color:#48dbfb" opacity="0.9" />
      <stop offset="48%" style="stop-color:#0abde3" opacity="0.9" />
      <stop offset="60%" style="stop-color:#006ba6" opacity="0.9" />
      <stop offset="72%" style="stop-color:#8e44ad" opacity="0.9" />
      <stop offset="84%" style="stop-color:#c44569" opacity="0.9" />
      <stop offset="100%" style="stop-color:#ff6b6b" opacity="0.9" />
    </linearGradient>

    <!-- 帝王紫金渐变 -->
    <linearGradient id="imperialGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8e44ad" />
      <stop offset="20%" style="stop-color:#9b59b6" />
      <stop offset="40%" style="stop-color:#fdcb6e" />
      <stop offset="60%" style="stop-color:#f39c12" />
      <stop offset="80%" style="stop-color:#e74c3c" />
      <stop offset="100%" style="stop-color:#8e44ad" />
    </linearGradient>
    
    <!-- 至尊液态金属效果 - 升级版 -->
    <radialGradient id="liquidMetal" cx="25%" cy="25%" r="85%">
      <stop offset="0%" style="stop-color:#ffffff" />
      <stop offset="15%" style="stop-color:#f8f9fa" />
      <stop offset="30%" style="stop-color:#fdcb6e" opacity="0.95" />
      <stop offset="50%" style="stop-color:#f39c12" opacity="0.9" />
      <stop offset="70%" style="stop-color:#e17055" opacity="0.85" />
      <stop offset="85%" style="stop-color:#d63031" opacity="0.9" />
      <stop offset="100%" style="stop-color:#8e44ad" />
    </radialGradient>
    
    <!-- 皇冠金属渐变 -->
    <linearGradient id="crownGold" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFD700" />
      <stop offset="50%" style="stop-color:#FFA500" />
      <stop offset="100%" style="stop-color:#B8860B" />
    </linearGradient>
    
    <!-- 至尊宝石渐变 - 升级版 -->
    <radialGradient id="gemstone" cx="25%" cy="25%" r="75%">
      <stop offset="0%" style="stop-color:#FFFFFF" />
      <stop offset="20%" style="stop-color:#f8f9fa" />
      <stop offset="40%" style="stop-color:#fdcb6e" />
      <stop offset="60%" style="stop-color:#f39c12" />
      <stop offset="80%" style="stop-color:#e17055" />
      <stop offset="100%" style="stop-color:#c0392b" />
    </radialGradient>

    <!-- 钻石折射效果 -->
    <radialGradient id="diamondRefraction" cx="20%" cy="20%" r="60%">
      <stop offset="0%" style="stop-color:#ffffff" opacity="0.9" />
      <stop offset="30%" style="stop-color:#e3f2fd" opacity="0.7" />
      <stop offset="60%" style="stop-color:#bbdefb" opacity="0.5" />
      <stop offset="100%" style="stop-color:#2196f3" opacity="0.3" />
    </radialGradient>
  </defs>
  
  <!-- 发光效果 - 多层模拟 -->
  <circle cx="64" cy="64" r="52" fill="url(#legendaryGradient)" opacity="0.1"/>
  <circle cx="64" cy="64" r="50" fill="url(#legendaryGradient)" opacity="0.2"/>
  
  <!-- 至尊彩虹光环 - 微扩张优化 -->
  <circle cx="64" cy="64" r="61.5" fill="none" stroke="url(#rainbowAura)" stroke-width="3" opacity="0.8" stroke-linecap="round">
    <animate attributeName="r" values="60.8;62.2;60.8" dur="3s" repeatCount="indefinite"/>
    <animate attributeName="stroke-width" values="2.8;3.2;2.8" dur="3s" repeatCount="indefinite"/>
    <animate attributeName="opacity" values="0.7;0.9;0.7" dur="3s" repeatCount="indefinite"/>
  </circle>

  <!-- 帝王紫金光环 - 美学升级 -->
  <circle cx="64" cy="64" r="56.5" fill="none" stroke="url(#imperialGradient)" stroke-width="2.2" opacity="0.85" stroke-linecap="round">
    <animate attributeName="opacity" values="0.7;1;0.7" dur="2s" repeatCount="indefinite"/>
    <animate attributeName="stroke-width" values="1.8;2.8;1.8" dur="2s" repeatCount="indefinite"/>
  </circle>

  <!-- 边界过渡层 -->
  <circle cx="64" cy="64" r="52" fill="url(#liquidMetal)" opacity="0.1"/>
  <circle cx="64" cy="64" r="50" fill="url(#liquidMetal)" opacity="0.2"/>

  <!-- 主体背景 - 精确边界 -->
  <circle cx="64" cy="64" r="48" fill="url(#liquidMetal)"/>

  <!-- 内圈装饰 - 精细化边界 -->
  <circle cx="64" cy="64" r="40.5" fill="none" stroke="#fff" stroke-width="1.5" opacity="0.7" stroke-linecap="round"/>
  <circle cx="64" cy="64" r="35" fill="none" stroke="#fff" stroke-width="0.8" opacity="0.5" stroke-linecap="round"/>
  <circle cx="64" cy="64" r="30" fill="none" stroke="#fff" stroke-width="0.5" opacity="0.3" stroke-linecap="round"/>
  
  <!-- 帝王皇冠 - 传说级设计 -->
  <g transform="translate(64, 64)">
    <!-- 皇冠发光底层 - 精细化圆角 -->
    <rect x="-25.5" y="12.5" width="51" height="9" rx="4.5" fill="url(#crownGold)" opacity="0.2"/>
    <rect x="-24.5" y="13" width="49" height="8" rx="4" fill="url(#crownGold)" opacity="0.4"/>
    <rect x="-23.5" y="13.5" width="47" height="7" rx="3.5" fill="url(#crownGold)" opacity="0.6"/>

    <!-- 皇冠底座 - 优化边角 -->
    <rect x="-23" y="14" width="46" height="6" rx="3" fill="url(#crownGold)"/>

    <!-- 皇冠主体发光 - 平滑边界 -->
    <path d="M-23,14 Q-18.5,-11 -18,-12 Q-12.5,7.5 -12,8 Q-6.5,-15.5 -6,-16 Q-0.5,7.5 0,8 Q5.5,-15.5 6,-16 Q11.5,7.5 12,8 Q17.5,-11 18,-12 Q22.5,13.5 23,14 Z" fill="url(#crownGold)" opacity="0.3"/>
    <path d="M-21.5,14 Q-17.2,-9.8 -17,-10 Q-11.2,9.8 -11,10 Q-5.2,-13.8 -5,-14 Q0.8,9.8 1,10 Q6.8,-13.8 7,-14 Q12.8,9.8 13,10 Q18.8,-9.8 19,-10 Q21.2,13.8 21.5,14 Z" fill="url(#crownGold)"/>

    <!-- 中央最高峰发光 - 精细化尖角 -->
    <path d="M-2.5,-14 Q0,-21.5 0,-22 Q0,-21.5 2.5,-14 Q1.2,-14.2 0,-14.5 Q-1.2,-14.2 -2.5,-14 Z" fill="url(#crownGold)" opacity="0.3"/>
    <path d="M-1.8,-14 Q0,-19.8 0,-20 Q0,-19.8 1.8,-14 Q0.9,-14.1 0,-14.3 Q-0.9,-14.1 -1.8,-14 Z" fill="url(#crownGold)"/>
    
    <!-- 宝石装饰 - 精细化多层发光 -->
    <!-- 中央主宝石 - 优化边界 -->
    <circle cx="0" cy="-16.2" r="5.2" fill="url(#gemstone)" opacity="0.2">
      <animate attributeName="opacity" values="0.1;0.4;0.1" dur="2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="0" cy="-16.1" r="4.5" fill="url(#gemstone)" opacity="0.4"/>
    <circle cx="0" cy="-16" r="3.8" fill="url(#gemstone)" opacity="0.9"/>
    <circle cx="0" cy="-16" r="2.2" fill="#fdcb6e"/>
    <circle cx="-0.8" cy="-16.8" r="1.2" fill="#FFFFFF" opacity="0.6"/>

    <!-- 侧面宝石 - 精细化设计 -->
    <circle cx="-17.8" cy="-12.2" r="4.2" fill="url(#gemstone)" opacity="0.2">
      <animate attributeName="opacity" values="0.1;0.3;0.1" dur="2.2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="-17.9" cy="-12.1" r="3.5" fill="url(#gemstone)" opacity="0.4"/>
    <circle cx="-18" cy="-12" r="2.8" fill="url(#gemstone)" opacity="0.8"/>
    <circle cx="-18" cy="-12" r="1.8" fill="#e17055"/>
    <circle cx="-18.6" cy="-12.6" r="0.8" fill="#FFFFFF" opacity="0.5"/>

    <circle cx="17.8" cy="-12.2" r="4.2" fill="url(#gemstone)" opacity="0.2">
      <animate attributeName="opacity" values="0.1;0.3;0.1" dur="2.4s" repeatCount="indefinite"/>
    </circle>
    <circle cx="17.9" cy="-12.1" r="3.5" fill="url(#gemstone)" opacity="0.4"/>
    <circle cx="18" cy="-12" r="2.8" fill="url(#gemstone)" opacity="0.8"/>
    <circle cx="18" cy="-12" r="1.8" fill="#e17055"/>
    <circle cx="17.4" cy="-12.6" r="0.8" fill="#FFFFFF" opacity="0.5"/>

    <!-- 小宝石 - 精细化边界 -->
    <circle cx="-6.1" cy="-16.1" r="2.8" fill="#fdcb6e" opacity="0.3">
      <animate attributeName="opacity" values="0.2;0.5;0.2" dur="1.8s" repeatCount="indefinite"/>
    </circle>
    <circle cx="-6" cy="-16" r="2.3" fill="#fdcb6e" opacity="0.9">
      <animate attributeName="opacity" values="0.7;1;0.7" dur="1.8s" repeatCount="indefinite"/>
    </circle>
    <circle cx="-6.4" cy="-16.4" r="1" fill="#FFFFFF" opacity="0.4"/>

    <circle cx="6.1" cy="-16.1" r="2.8" fill="#fdcb6e" opacity="0.3">
      <animate attributeName="opacity" values="0.2;0.5;0.2" dur="2.1s" repeatCount="indefinite"/>
    </circle>
    <circle cx="6" cy="-16" r="2.3" fill="#fdcb6e" opacity="0.9">
      <animate attributeName="opacity" values="0.7;1;0.7" dur="2.1s" repeatCount="indefinite"/>
    </circle>
    <circle cx="5.6" cy="-16.4" r="1" fill="#FFFFFF" opacity="0.4"/>
    
    <!-- 皇冠装饰纹样 -->
    <g stroke="#ffffff" stroke-width="1" opacity="0.6">
      <line x1="-20" y1="0" x2="-15" y2="-5"/>
      <line x1="-8" y1="0" x2="-3" y2="-5"/>
      <line x1="8" y1="0" x2="13" y2="-5"/>
      <line x1="20" y1="0" x2="15" y2="-5"/>
    </g>
    
    <!-- 神圣光芒 - 边界控制优化 -->
    <g opacity="0.7">
      <!-- 主光芒 - 边界控制 -->
      <line x1="0" y1="-21.5" x2="0" y2="-28" stroke="#fdcb6e" stroke-width="2" stroke-linecap="round">
        <animate attributeName="y2" values="-28;-32;-28" dur="2s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.5;1;0.5" dur="2s" repeatCount="indefinite"/>
      </line>

      <!-- 侧光芒 - 边界控制 -->
      <line x1="-2.8" y1="-19.5" x2="-5.5" y2="-26" stroke="#fdcb6e" stroke-width="1.5" stroke-linecap="round">
        <animate attributeName="opacity" values="0.4;0.9;0.4" dur="1.8s" repeatCount="indefinite"/>
      </line>
      <line x1="2.8" y1="-19.5" x2="5.5" y2="-26" stroke="#fdcb6e" stroke-width="1.5" stroke-linecap="round">
        <animate attributeName="opacity" values="0.4;0.9;0.4" dur="2.2s" repeatCount="indefinite"/>
      </line>

      <!-- 扩散光芒 - 边界控制 -->
      <line x1="-4.5" y1="-17.8" x2="-8.5" y2="-23.5" stroke="#e17055" stroke-width="1" stroke-linecap="round">
        <animate attributeName="opacity" values="0.3;0.7;0.3" dur="2.5s" repeatCount="indefinite"/>
      </line>
      <line x1="4.5" y1="-17.8" x2="8.5" y2="-23.5" stroke="#e17055" stroke-width="1" stroke-linecap="round">
        <animate attributeName="opacity" values="0.3;0.7;0.3" dur="1.9s" repeatCount="indefinite"/>
      </line>
    </g>
    
    <!-- 皇权标识 -->
    <g transform="translate(0, 26)" opacity="0.9">
      <rect x="-20" y="0" width="40" height="6" rx="3" fill="url(#legendaryGradient)" opacity="0.8"/>
      <text x="0" y="4" text-anchor="middle" fill="#fff" font-family="Arial, sans-serif" font-size="6" font-weight="bold">EMPEROR</text>
    </g>
  </g>
  
  <!-- 粒子效果 - 增强版 -->
  <g opacity="0.9">
    <circle cx="20" cy="20" r="2" fill="#fdcb6e" opacity="0.3">
      <animate attributeName="cy" values="20;15;20" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;0.8;0" dur="3s" repeatCount="indefinite"/>
    </circle>
    <circle cx="20" cy="20" r="1.5" fill="#fdcb6e">
      <animate attributeName="cy" values="20;15;20" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;0" dur="3s" repeatCount="indefinite"/>
    </circle>
    
    <circle cx="108" cy="30" r="1.5" fill="#e17055" opacity="0.3">
      <animate attributeName="cx" values="108;103;108" dur="2.5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;0.7;0" dur="2.5s" repeatCount="indefinite"/>
    </circle>
    <circle cx="108" cy="30" r="1" fill="#e17055">
      <animate attributeName="cx" values="108;103;108" dur="2.5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;0" dur="2.5s" repeatCount="indefinite"/>
    </circle>
    
    <circle cx="15" cy="100" r="2" fill="#fdcb6e" opacity="0.3">
      <animate attributeName="cy" values="100;105;100" dur="2.8s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;0.6;0" dur="2.8s" repeatCount="indefinite"/>
    </circle>
    <circle cx="15" cy="100" r="1.5" fill="#fdcb6e">
      <animate attributeName="cy" values="100;105;100" dur="2.8s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;0" dur="2.8s" repeatCount="indefinite"/>
    </circle>
    
    <circle cx="110" cy="95" r="1.5" fill="#e17055" opacity="0.3">
      <animate attributeName="cx" values="110;115;110" dur="3.2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;0.5;0" dur="3.2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="110" cy="95" r="1" fill="#e17055">
      <animate attributeName="cx" values="110;115;110" dur="3.2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;0" dur="3.2s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- 底部传说标识 -->
  <circle cx="64" cy="115" r="12" fill="none" stroke="url(#legendaryGradient)" stroke-width="3" opacity="0.8"/>
  <circle cx="64" cy="115" r="8" fill="none" stroke="#fdcb6e" stroke-width="2" opacity="0.9"/>
  <text x="64" y="119" text-anchor="middle" fill="url(#legendaryGradient)" font-family="Arial, sans-serif" font-size="9" font-weight="bold">记账之王</text>
</svg>
