<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 使用类普通级渐变 -->
    <linearGradient id="usageNormal" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#9C27B0" />
      <stop offset="50%" style="stop-color:#7B1FA2" />
      <stop offset="100%" style="stop-color:#4A148C" />
    </linearGradient>
    
    <!-- 日历渐变 -->
    <linearGradient id="calendarGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFFFFF" />
      <stop offset="100%" style="stop-color:#F3E5F5" />
    </linearGradient>
    
    <!-- 打卡渐变 -->
    <radialGradient id="checkGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#4CAF50" />
      <stop offset="100%" style="stop-color:#2E7D32" />
    </radialGradient>
    
    <!-- 时钟渐变 -->
    <linearGradient id="clockGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFD54F" />
      <stop offset="100%" style="stop-color:#FF9800" />
    </linearGradient>
  </defs>
  
  <!-- 外圈边框 -->
  <circle cx="64" cy="64" r="58" fill="none" stroke="url(#usageNormal)" stroke-width="2" opacity="0.6"/>
  <circle cx="64" cy="64" r="54" fill="none" stroke="#ffffff" stroke-width="1" opacity="0.4"/>
  
  <!-- 主体背景 -->
  <circle cx="64" cy="64" r="48" fill="url(#usageNormal)"/>
  
  <!-- 内圈装饰 -->
  <circle cx="64" cy="64" r="40" fill="none" stroke="#ffffff" stroke-width="1" opacity="0.6"/>
  
  <!-- 日常使用图案 -->
  <g transform="translate(64, 64)">
    <!-- 日历主体 -->
    <rect x="-12" y="-8" width="24" height="16" rx="2" fill="url(#calendarGradient)"/>
    
    <!-- 日历顶部 -->
    <rect x="-12" y="-8" width="24" height="4" rx="2" fill="url(#usageNormal)" opacity="0.8"/>
    
    <!-- 日历装订孔 -->
    <circle cx="-8" cy="-6" r="1" fill="#9C27B0"/>
    <circle cx="0" cy="-6" r="1" fill="#9C27B0"/>
    <circle cx="8" cy="-6" r="1" fill="#9C27B0"/>
    
    <!-- 日期网格 -->
    <g stroke="#E1BEE7" stroke-width="0.5" opacity="0.6">
      <line x1="-8" y1="-2" x2="-8" y2="6"/>
      <line x1="-4" y1="-2" x2="-4" y2="6"/>
      <line x1="0" y1="-2" x2="0" y2="6"/>
      <line x1="4" y1="-2" x2="4" y2="6"/>
      <line x1="8" y1="-2" x2="8" y2="6"/>
      
      <line x1="-10" y1="-2" x2="10" y2="-2"/>
      <line x1="-10" y1="0" x2="10" y2="0"/>
      <line x1="-10" y1="2" x2="10" y2="2"/>
      <line x1="-10" y1="4" x2="10" y2="4"/>
    </g>
    
    <!-- 打卡标记 -->
    <g fill="url(#checkGradient)">
      <circle cx="-6" cy="-1" r="1.5" opacity="0.8">
        <animate attributeName="opacity" values="0.6;1;0.6" dur="2s" repeatCount="indefinite"/>
      </circle>
      <path d="M-6.8,-1 L-6.2,-0.4 L-5.2,-1.4" stroke="#FFFFFF" stroke-width="0.5" fill="none"/>
      
      <circle cx="-2" cy="1" r="1.5" opacity="0.8">
        <animate attributeName="opacity" values="0.5;0.9;0.5" dur="2.3s" repeatCount="indefinite"/>
      </circle>
      <path d="M-2.8,1 L-2.2,1.6 L-1.2,0.6" stroke="#FFFFFF" stroke-width="0.5" fill="none"/>
      
      <circle cx="2" cy="3" r="1.5" opacity="0.8">
        <animate attributeName="opacity" values="0.7;1;0.7" dur="1.8s" repeatCount="indefinite"/>
      </circle>
      <path d="M1.2,3 L1.8,3.6 L2.8,2.6" stroke="#FFFFFF" stroke-width="0.5" fill="none"/>
      
      <circle cx="6" cy="1" r="1.5" opacity="0.8">
        <animate attributeName="opacity" values="0.4;0.8;0.4" dur="2.5s" repeatCount="indefinite"/>
      </circle>
      <path d="M5.2,1 L5.8,1.6 L6.8,0.6" stroke="#FFFFFF" stroke-width="0.5" fill="none"/>
    </g>
    
    <!-- 今日标记 -->
    <rect x="5" y="2" width="3" height="3" rx="0.5" fill="#E91E63" opacity="0.8"/>
    <text x="6.5" y="4" text-anchor="middle" fill="#FFFFFF" font-family="Arial, sans-serif" font-size="2" font-weight="bold">今</text>
    
    <!-- 时钟 -->
    <g transform="translate(16, -12)">
      <circle cx="0" cy="0" r="6" fill="url(#clockGradient)" opacity="0.8"/>
      <circle cx="0" cy="0" r="5" fill="#FFFFFF"/>
      
      <!-- 时钟刻度 -->
      <g stroke="#9C27B0" stroke-width="0.5" opacity="0.6">
        <line x1="0" y1="-4" x2="0" y2="-3"/>
        <line x1="4" y1="0" x2="3" y2="0"/>
        <line x1="0" y1="4" x2="0" y2="3"/>
        <line x1="-4" y1="0" x2="-3" y2="0"/>
      </g>
      
      <!-- 时钟指针 -->
      <line x1="0" y1="0" x2="0" y2="-2" stroke="#9C27B0" stroke-width="1"/>
      <line x1="0" y1="0" x2="1.5" y2="0" stroke="#9C27B0" stroke-width="0.8"/>
      <circle cx="0" cy="0" r="0.5" fill="#9C27B0"/>
    </g>
    
    <!-- 使用统计 -->
    <g transform="translate(-16, 12)">
      <rect x="0" y="0" width="12" height="6" rx="3" fill="url(#usageNormal)" opacity="0.8"/>
      <text x="6" y="4" text-anchor="middle" fill="#FFFFFF" font-family="Arial, sans-serif" font-size="3" font-weight="bold">30天</text>
    </g>
    
    <!-- 连续使用标记 -->
    <g transform="translate(0, 18)">
      <rect x="-8" y="0" width="16" height="4" rx="2" fill="#4CAF50" opacity="0.8"/>
      <text x="0" y="2.5" text-anchor="middle" fill="#FFFFFF" font-family="Arial, sans-serif" font-size="2.5" font-weight="bold">连续7天</text>
    </g>
    
    <!-- 装饰星星 -->
    <g fill="#FFD54F" opacity="0.7">
      <circle cx="-18" cy="-15" r="1">
        <animate attributeName="opacity" values="0.5;1;0.5" dur="2s" repeatCount="indefinite"/>
      </circle>
      <circle cx="18" cy="-8" r="0.8">
        <animate attributeName="opacity" values="0.4;0.9;0.4" dur="2.5s" repeatCount="indefinite"/>
      </circle>
      <circle cx="-12" cy="18" r="0.6">
        <animate attributeName="opacity" values="0.6;1;0.6" dur="1.8s" repeatCount="indefinite"/>
      </circle>
    </g>
  </g>
  
  <!-- 底部标识 -->
  <circle cx="64" cy="108" r="10" fill="none" stroke="url(#usageNormal)" stroke-width="2" opacity="0.6"/>
  <text x="64" y="112" text-anchor="middle" fill="#4A148C" font-family="Arial, sans-serif" font-size="8" font-weight="bold" opacity="0.9">日常</text>
</svg>
