<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="travelLegendary" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0D47A1" />
      <stop offset="100%" style="stop-color:#64B5F6" />
    </linearGradient>
    <radialGradient id="crownGradient" cx="30%" cy="30%" r="70%">
      <stop offset="0%" style="stop-color:#FFD700" />
      <stop offset="100%" style="stop-color:#E65100" />
    </radialGradient>
  </defs>
  
  <circle cx="64" cy="64" r="56" fill="url(#travelLegendary)" opacity="0.1"/>
  <circle cx="64" cy="64" r="48" fill="url(#travelLegendary)"/>
  <circle cx="64" cy="64" r="42" fill="none" stroke="#fff" stroke-width="2" opacity="0.9"/>
  
  <g transform="translate(64, 64)">
    <rect x="-12" y="4" width="24" height="6" rx="3" fill="url(#crownGradient)"/>
    <path d="M-12,4 Q-8,-12 -6,-14 Q-3,3 0,4 Q3,-16 4,-18 Q6,3 8,4 Q10,-12 12,-14 Q12,3 12,4 Z" fill="url(#crownGradient)"/>
    
    <circle cx="0" cy="-14" r="3" fill="url(#crownGradient)"/>
    <circle cx="0" cy="-14" r="2" fill="#FFD700"/>
    <circle cx="-1" cy="-15" r="1" fill="#FFFFFF" opacity="0.8"/>
    
    <circle cx="-8" cy="-10" r="2" fill="#E91E63"/>
    <circle cx="8" cy="-10" r="2" fill="#9C27B0"/>
  </g>
  
  <circle cx="64" cy="115" r="8" fill="none" stroke="#FFD700" stroke-width="2"/>
  <text x="64" y="119" text-anchor="middle" fill="#0D47A1" font-family="Arial, sans-serif" font-size="8" font-weight="bold">皇冠</text>
</svg>
