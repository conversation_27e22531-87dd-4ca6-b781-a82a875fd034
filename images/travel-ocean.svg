<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 旅行类史诗级渐变 -->
    <linearGradient id="travelEpic" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1E88E5" />
      <stop offset="20%" style="stop-color:#1976D2" />
      <stop offset="40%" style="stop-color:#1565C0" />
      <stop offset="60%" style="stop-color:#0D47A1" />
      <stop offset="80%" style="stop-color:#0A3D91" />
      <stop offset="100%" style="stop-color:#083481" />
    </linearGradient>
    
    <!-- 海洋渐变 -->
    <radialGradient id="oceanGradient" cx="50%" cy="30%" r="80%">
      <stop offset="0%" style="stop-color:#4FC3F7" />
      <stop offset="30%" style="stop-color:#29B6F6" />
      <stop offset="60%" style="stop-color:#2196F3" />
      <stop offset="100%" style="stop-color:#1565C0" />
    </radialGradient>
    
    <!-- 帆船渐变 -->
    <linearGradient id="sailGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFFFFF" />
      <stop offset="100%" style="stop-color:#ECEFF1" />
    </linearGradient>
    
    <!-- 船体渐变 -->
    <linearGradient id="hullGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8D6E63" />
      <stop offset="100%" style="stop-color:#5D4037" />
    </linearGradient>
    
    <!-- 海浪渐变 -->
    <linearGradient id="waveGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#E3F2FD" opacity="0.8" />
      <stop offset="100%" style="stop-color:#BBDEFB" opacity="0.4" />
    </linearGradient>
    
    <!-- 海豚渐变 -->
    <linearGradient id="dolphinGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#607D8B" />
      <stop offset="100%" style="stop-color:#37474F" />
    </linearGradient>
  </defs>
  
  <!-- 发光效果 - 多层模拟 -->
  <circle cx="64" cy="64" r="54" fill="url(#travelEpic)" opacity="0.1"/>
  <circle cx="64" cy="64" r="52" fill="url(#travelEpic)" opacity="0.15"/>
  <circle cx="64" cy="64" r="50" fill="url(#travelEpic)" opacity="0.2"/>
  
  <!-- 外层动态光环 -->
  <circle cx="64" cy="64" r="62" fill="none" stroke="url(#travelEpic)" stroke-width="1" opacity="0.3">
    <animate attributeName="r" values="60;66;60" dur="5s" repeatCount="indefinite"/>
    <animate attributeName="opacity" values="0.2;0.5;0.2" dur="5s" repeatCount="indefinite"/>
  </circle>
  
  <!-- 多层装饰边框 -->
  <circle cx="64" cy="64" r="58" fill="none" stroke="url(#travelEpic)" stroke-width="2" opacity="0.6"/>
  <circle cx="64" cy="64" r="54" fill="none" stroke="#fff" stroke-width="1" opacity="0.4"/>
  
  <!-- 主体背景 -->
  <circle cx="64" cy="64" r="48" fill="url(#travelEpic)"/>
  
  <!-- 海洋背景 -->
  <circle cx="64" cy="64" r="42" fill="url(#oceanGradient)" opacity="0.6"/>
  
  <!-- 内圈装饰 -->
  <circle cx="64" cy="64" r="40" fill="none" stroke="#fff" stroke-width="1.8" opacity="0.7"/>
  <circle cx="64" cy="64" r="34" fill="none" stroke="#fff" stroke-width="1" opacity="0.5"/>
  
  <!-- 海洋图案 - 史诗级设计 -->
  <g transform="translate(64, 64)">
    <!-- 帆船 - 多层发光 -->
    <g transform="translate(0, -5)">
      <!-- 船体发光底层 -->
      <ellipse cx="0" cy="8" rx="12" ry="4" fill="url(#hullGradient)" opacity="0.3"/>
      <ellipse cx="0" cy="8" rx="10" ry="3" fill="url(#hullGradient)"/>
      
      <!-- 船桅 -->
      <rect x="-0.5" y="-8" width="1" height="16" fill="#8D6E63"/>
      
      <!-- 主帆 - 发光效果 -->
      <path d="M0,-8 Q-8,-6 -10,0 Q-8,6 0,4 Z" fill="url(#sailGradient)" opacity="0.3"/>
      <path d="M0,-6 Q-6,-4 -8,0 Q-6,4 0,2 Z" fill="url(#sailGradient)">
        <animate attributeName="d" values="M0,-6 Q-6,-4 -8,0 Q-6,4 0,2 Z;M0,-6 Q-7,-3 -9,0 Q-7,5 0,2 Z;M0,-6 Q-6,-4 -8,0 Q-6,4 0,2 Z" dur="4s" repeatCount="indefinite"/>
      </path>
      
      <!-- 前帆 -->
      <path d="M0,-8 Q6,-6 8,0 Q6,6 0,4 Z" fill="url(#sailGradient)" opacity="0.3"/>
      <path d="M0,-6 Q4,-4 6,0 Q4,4 0,2 Z" fill="url(#sailGradient)">
        <animate attributeName="d" values="M0,-6 Q4,-4 6,0 Q4,4 0,2 Z;M0,-6 Q5,-3 7,0 Q5,5 0,2 Z;M0,-6 Q4,-4 6,0 Q4,4 0,2 Z" dur="3.5s" repeatCount="indefinite"/>
      </path>
      
      <!-- 船旗 -->
      <rect x="0" y="-10" width="4" height="2" fill="#E91E63">
        <animate attributeName="width" values="4;5;4" dur="2s" repeatCount="indefinite"/>
      </rect>
    </g>
    
    <!-- 海浪 - 动态效果 -->
    <g opacity="0.8">
      <path d="M-30,12 Q-25,8 -20,12 Q-15,16 -10,12 Q-5,8 0,12 Q5,16 10,12 Q15,8 20,12 Q25,16 30,12" 
            stroke="url(#waveGradient)" 
            stroke-width="2" 
            fill="none">
        <animate attributeName="d" values="M-30,12 Q-25,8 -20,12 Q-15,16 -10,12 Q-5,8 0,12 Q5,16 10,12 Q15,8 20,12 Q25,16 30,12;M-30,12 Q-25,16 -20,12 Q-15,8 -10,12 Q-5,16 0,12 Q5,8 10,12 Q15,16 20,12 Q25,8 30,12;M-30,12 Q-25,8 -20,12 Q-15,16 -10,12 Q-5,8 0,12 Q5,16 10,12 Q15,8 20,12 Q25,16 30,12" dur="3s" repeatCount="indefinite"/>
      </path>
      
      <path d="M-30,16 Q-25,12 -20,16 Q-15,20 -10,16 Q-5,12 0,16 Q5,20 10,16 Q15,12 20,16 Q25,20 30,16" 
            stroke="url(#waveGradient)" 
            stroke-width="1.5" 
            fill="none" 
            opacity="0.6">
        <animate attributeName="d" values="M-30,16 Q-25,12 -20,16 Q-15,20 -10,16 Q-5,12 0,16 Q5,20 10,16 Q15,12 20,16 Q25,20 30,16;M-30,16 Q-25,20 -20,16 Q-15,12 -10,16 Q-5,20 0,16 Q5,12 10,16 Q15,20 20,16 Q25,12 30,16;M-30,16 Q-25,12 -20,16 Q-15,20 -10,16 Q-5,12 0,16 Q5,20 10,16 Q15,12 20,16 Q25,20 30,16" dur="3.5s" repeatCount="indefinite"/>
      </path>
    </g>
    
    <!-- 海豚 - 跳跃动画 -->
    <g transform="translate(-20, 10)" opacity="0.9">
      <g>
        <ellipse cx="0" cy="0" rx="6" ry="2" fill="url(#dolphinGradient)" opacity="0.3"/>
        <ellipse cx="0" cy="0" rx="5" ry="1.5" fill="url(#dolphinGradient)"/>
        <path d="M-5,0 Q-7,-1 -6,-2 Q-5,-1 -4,0" fill="url(#dolphinGradient)"/>
        <path d="M5,0 Q6,-0.5 7,0 Q6,0.5 5,0" fill="url(#dolphinGradient)"/>
        <circle cx="-2" cy="-0.5" r="0.3" fill="#FFFFFF"/>
        
        <animateTransform attributeName="transform" 
                         type="translate" 
                         values="0,0;-2,-8;0,0" 
                         dur="4s" 
                         repeatCount="indefinite"/>
      </g>
    </g>
    
    <!-- 海鸥 -->
    <g transform="translate(18, -15)" opacity="0.7">
      <g>
        <path d="M-2,0 Q0,-1 2,0" stroke="#FFFFFF" stroke-width="1" fill="none"/>
        <path d="M-1,0.5 Q0,0 1,0.5" stroke="#FFFFFF" stroke-width="0.8" fill="none"/>
        
        <animateTransform attributeName="transform" 
                         type="translate" 
                         values="18,-15;22,-18;18,-15" 
                         dur="6s" 
                         repeatCount="indefinite"/>
      </g>
    </g>
    
    <!-- 灯塔 -->
    <g transform="translate(22, -8)">
      <!-- 灯塔发光底层 -->
      <rect x="0" y="0" width="3" height="16" rx="1.5" fill="#ECEFF1" opacity="0.3"/>
      <rect x="0.2" y="0.2" width="2.6" height="15.6" rx="1.3" fill="#ECEFF1"/>
      
      <!-- 灯塔装饰条纹 -->
      <rect x="0.2" y="3" width="2.6" height="2" fill="#E53935"/>
      <rect x="0.2" y="8" width="2.6" height="2" fill="#E53935"/>
      <rect x="0.2" y="13" width="2.6" height="2" fill="#E53935"/>
      
      <!-- 灯塔顶部 -->
      <rect x="-0.5" y="-2" width="4" height="2" rx="2" fill="#FFD54F"/>
      
      <!-- 灯塔光束 -->
      <g opacity="0.6">
        <path d="M1.5,-2 L-5,-10 L8,-10 Z" fill="#FFD54F" opacity="0.2">
          <animate attributeName="opacity" values="0.1;0.4;0.1" dur="2s" repeatCount="indefinite"/>
        </path>
      </g>
    </g>
    
    <!-- 海底宝藏 -->
    <g transform="translate(-18, 18)" opacity="0.8">
      <ellipse cx="0" cy="0" rx="4" ry="2" fill="#FFD700" opacity="0.3"/>
      <ellipse cx="0" cy="0" rx="3" ry="1.5" fill="#FFD700"/>
      <circle cx="-1" cy="-0.5" r="0.5" fill="#E91E63">
        <animate attributeName="opacity" values="0.7;1;0.7" dur="2.5s" repeatCount="indefinite"/>
      </circle>
      <circle cx="1" cy="-0.5" r="0.4" fill="#9C27B0">
        <animate attributeName="opacity" values="0.6;1;0.6" dur="2.8s" repeatCount="indefinite"/>
      </circle>
    </g>
    
    <!-- 海洋标识 -->
    <g transform="translate(0, 28)">
      <rect x="-15" y="0" width="30" height="6" rx="3" fill="url(#oceanGradient)" opacity="0.8"/>
      <text x="0" y="4" text-anchor="middle" fill="#ffffff" font-family="Arial, sans-serif" font-size="5" font-weight="bold">OCEAN</text>
    </g>
    
    <!-- 气泡效果 -->
    <g opacity="0.6">
      <circle cx="-12" cy="20" r="1" fill="#E3F2FD">
        <animate attributeName="cy" values="20;10;20" dur="3s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.6;0.1;0.6" dur="3s" repeatCount="indefinite"/>
      </circle>
      <circle cx="-8" cy="22" r="0.8" fill="#E3F2FD">
        <animate attributeName="cy" values="22;8;22" dur="3.5s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.5;0.1;0.5" dur="3.5s" repeatCount="indefinite"/>
      </circle>
      <circle cx="-15" cy="24" r="0.6" fill="#E3F2FD">
        <animate attributeName="cy" values="24;12;24" dur="4s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.4;0.1;0.4" dur="4s" repeatCount="indefinite"/>
      </circle>
    </g>
  </g>
  
  <!-- 底部华丽标识 -->
  <circle cx="64" cy="115" r="12" fill="none" stroke="url(#travelEpic)" stroke-width="2.5" opacity="0.7"/>
  <circle cx="64" cy="115" r="8" fill="none" stroke="#1E88E5" stroke-width="1.5" opacity="0.9"/>
  <text x="64" y="119" text-anchor="middle" fill="#083481" font-family="Arial, sans-serif" font-size="8" font-weight="bold" opacity="0.9">海洋</text>
</svg>
