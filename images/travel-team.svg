<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="travelRare" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#42A5F5" />
      <stop offset="100%" style="stop-color:#0D47A1" />
    </linearGradient>
    <linearGradient id="personGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFDBCB" />
      <stop offset="100%" style="stop-color:#FFAB91" />
    </linearGradient>
  </defs>
  
  <circle cx="64" cy="64" r="52" fill="url(#travelRare)" opacity="0.1"/>
  <circle cx="64" cy="64" r="48" fill="url(#travelRare)"/>
  <circle cx="64" cy="64" r="40" fill="none" stroke="#ffffff" stroke-width="1.5" opacity="0.6"/>
  
  <g transform="translate(64, 64)">
    <!-- 中央人物 -->
    <circle cx="0" cy="-8" r="3" fill="url(#personGradient)"/>
    <ellipse cx="0" cy="-2" rx="2.5" ry="4" fill="#2196F3"/>
    
    <!-- 左侧人物 -->
    <circle cx="-8" cy="-6" r="2.5" fill="url(#personGradient)"/>
    <ellipse cx="-8" cy="-1" rx="2" ry="3" fill="#4CAF50"/>
    
    <!-- 右侧人物 -->
    <circle cx="8" cy="-6" r="2.5" fill="url(#personGradient)"/>
    <ellipse cx="8" cy="-1" rx="2" ry="3" fill="#FF9800"/>
    
    <g transform="translate(0, 12)">
      <rect x="-6" y="0" width="12" height="4" rx="2" fill="#4CAF50" opacity="0.8"/>
      <text x="0" y="2.5" text-anchor="middle" fill="#FFFFFF" font-family="Arial, sans-serif" font-size="2.5" font-weight="bold">团队</text>
    </g>
  </g>
  
  <circle cx="64" cy="110" r="8" fill="none" stroke="#42A5F5" stroke-width="1.5"/>
  <text x="64" y="114" text-anchor="middle" fill="#0D47A1" font-family="Arial, sans-serif" font-size="8" font-weight="bold">团队</text>
</svg>
