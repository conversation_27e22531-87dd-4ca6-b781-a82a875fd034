<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 旅行类稀有级渐变 -->
    <linearGradient id="travelRare" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#42A5F5" />
      <stop offset="25%" style="stop-color:#2196F3" />
      <stop offset="50%" style="stop-color:#1976D2" />
      <stop offset="75%" style="stop-color:#1565C0" />
      <stop offset="100%" style="stop-color:#0D47A1" />
    </linearGradient>
    
    <!-- 太阳渐变 -->
    <radialGradient id="sunGradient" cx="30%" cy="30%" r="70%">
      <stop offset="0%" style="stop-color:#FFEB3B" />
      <stop offset="50%" style="stop-color:#FFC107" />
      <stop offset="100%" style="stop-color:#FF9800" />
    </radialGradient>
    
    <!-- 云朵渐变 -->
    <linearGradient id="cloudGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFFFFF" />
      <stop offset="100%" style="stop-color:#ECEFF1" />
    </linearGradient>
    
    <!-- 雨滴渐变 -->
    <linearGradient id="rainGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#81D4FA" />
      <stop offset="100%" style="stop-color:#29B6F6" />
    </linearGradient>
    
    <!-- 雪花渐变 -->
    <radialGradient id="snowGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#FFFFFF" />
      <stop offset="100%" style="stop-color:#E3F2FD" />
    </radialGradient>
  </defs>
  
  <!-- 发光效果 - 多层模拟 -->
  <circle cx="64" cy="64" r="52" fill="url(#travelRare)" opacity="0.1"/>
  <circle cx="64" cy="64" r="50" fill="url(#travelRare)" opacity="0.2"/>
  
  <!-- 外圈边框 -->
  <circle cx="64" cy="64" r="58" fill="none" stroke="url(#travelRare)" stroke-width="2" opacity="0.6"/>
  <circle cx="64" cy="64" r="54" fill="none" stroke="#ffffff" stroke-width="1" opacity="0.4"/>
  
  <!-- 主体背景 -->
  <circle cx="64" cy="64" r="48" fill="url(#travelRare)"/>
  
  <!-- 内圈装饰 -->
  <circle cx="64" cy="64" r="40" fill="none" stroke="#ffffff" stroke-width="1.5" opacity="0.6"/>
  <circle cx="64" cy="64" r="34" fill="none" stroke="#ffffff" stroke-width="1" opacity="0.4"/>
  
  <!-- 天气图案 -->
  <g transform="translate(64, 64)">
    <!-- 太阳 - 多层发光 -->
    <g transform="translate(-12, -12)">
      <circle cx="0" cy="0" r="8" fill="url(#sunGradient)" opacity="0.3">
        <animate attributeName="r" values="7;9;7" dur="4s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.2;0.4;0.2" dur="4s" repeatCount="indefinite"/>
      </circle>
      <circle cx="0" cy="0" r="6" fill="url(#sunGradient)">
        <animate attributeName="r" values="5.5;6.5;5.5" dur="4s" repeatCount="indefinite"/>
      </circle>
      
      <!-- 太阳光芒 -->
      <g stroke="#FFEB3B" stroke-width="1.5" opacity="0.8">
        <line x1="0" y1="-10" x2="0" y2="-12">
          <animate attributeName="opacity" values="0.6;1;0.6" dur="2s" repeatCount="indefinite"/>
        </line>
        <line x1="7" y1="-7" x2="8.5" y2="-8.5">
          <animate attributeName="opacity" values="0.6;1;0.6" dur="2.2s" repeatCount="indefinite"/>
        </line>
        <line x1="10" y1="0" x2="12" y2="0">
          <animate attributeName="opacity" values="0.6;1;0.6" dur="2.4s" repeatCount="indefinite"/>
        </line>
        <line x1="7" y1="7" x2="8.5" y2="8.5">
          <animate attributeName="opacity" values="0.6;1;0.6" dur="2.6s" repeatCount="indefinite"/>
        </line>
        <line x1="0" y1="10" x2="0" y2="12">
          <animate attributeName="opacity" values="0.6;1;0.6" dur="2.8s" repeatCount="indefinite"/>
        </line>
        <line x1="-7" y1="7" x2="-8.5" y2="8.5">
          <animate attributeName="opacity" values="0.6;1;0.6" dur="3s" repeatCount="indefinite"/>
        </line>
        <line x1="-10" y1="0" x2="-12" y2="0">
          <animate attributeName="opacity" values="0.6;1;0.6" dur="3.2s" repeatCount="indefinite"/>
        </line>
        <line x1="-7" y1="-7" x2="-8.5" y2="-8.5">
          <animate attributeName="opacity" values="0.6;1;0.6" dur="3.4s" repeatCount="indefinite"/>
        </line>
      </g>
    </g>
    
    <!-- 云朵 - 多层发光 -->
    <g transform="translate(8, -8)">
      <ellipse cx="0" cy="0" rx="10" ry="6" fill="url(#cloudGradient)" opacity="0.3"/>
      <ellipse cx="0" cy="0" rx="8" ry="5" fill="url(#cloudGradient)"/>
      <ellipse cx="-4" cy="-2" rx="5" ry="3" fill="url(#cloudGradient)"/>
      <ellipse cx="4" cy="-1" rx="4" ry="2.5" fill="url(#cloudGradient)"/>
      <ellipse cx="0" cy="2" rx="6" ry="3" fill="url(#cloudGradient)"/>
    </g>
    
    <!-- 雨滴 - 动态效果 -->
    <g opacity="0.8">
      <ellipse cx="12" cy="2" rx="1" ry="3" fill="url(#rainGradient)">
        <animate attributeName="cy" values="2;18;2" dur="1.5s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.8;0.3;0.8" dur="1.5s" repeatCount="indefinite"/>
      </ellipse>
      <ellipse cx="16" cy="0" rx="0.8" ry="2.5" fill="url(#rainGradient)">
        <animate attributeName="cy" values="0;16;0" dur="1.8s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.7;0.2;0.7" dur="1.8s" repeatCount="indefinite"/>
      </ellipse>
      <ellipse cx="8" cy="4" rx="0.9" ry="2.8" fill="url(#rainGradient)">
        <animate attributeName="cy" values="4;20;4" dur="1.3s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.6;0.2;0.6" dur="1.3s" repeatCount="indefinite"/>
      </ellipse>
      <ellipse cx="20" cy="3" rx="0.7" ry="2.2" fill="url(#rainGradient)">
        <animate attributeName="cy" values="3;19;3" dur="1.6s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.5;0.1;0.5" dur="1.6s" repeatCount="indefinite"/>
      </ellipse>
    </g>
    
    <!-- 雪花 - 旋转动画 -->
    <g transform="translate(-18, 8)" opacity="0.7">
      <g>
        <path d="M0,-4 L0,4 M-4,0 L4,0 M-3,-3 L3,3 M-3,3 L3,-3" stroke="url(#snowGradient)" stroke-width="1" fill="none"/>
        <circle cx="0" cy="0" r="0.5" fill="url(#snowGradient)"/>
        <animate attributeName="transform" values="rotate(0);rotate(360)" dur="8s" repeatCount="indefinite"/>
        <animateTransform attributeName="transform" type="translate" values="0,0;0,3;0,0" dur="3s" repeatCount="indefinite"/>
      </g>
    </g>
    
    <g transform="translate(-22, 12)" opacity="0.6">
      <g>
        <path d="M0,-3 L0,3 M-3,0 L3,0 M-2,-2 L2,2 M-2,2 L2,-2" stroke="url(#snowGradient)" stroke-width="0.8" fill="none"/>
        <circle cx="0" cy="0" r="0.3" fill="url(#snowGradient)"/>
        <animate attributeName="transform" values="rotate(0);rotate(-360)" dur="10s" repeatCount="indefinite"/>
        <animateTransform attributeName="transform" type="translate" values="0,0;0,4;0,0" dur="4s" repeatCount="indefinite"/>
      </g>
    </g>
    
    <!-- 温度计 -->
    <g transform="translate(0, 12)">
      <rect x="-1" y="0" width="2" height="12" rx="1" fill="#E0E0E0"/>
      <circle cx="0" cy="14" r="2.5" fill="#FF5722"/>
      <rect x="-0.5" y="2" width="1" height="8" fill="#FF5722"/>
      
      <!-- 温度刻度 -->
      <g stroke="#757575" stroke-width="0.5">
        <line x1="1.5" y1="3" x2="2.5" y2="3"/>
        <line x1="1.5" y1="5" x2="2.5" y2="5"/>
        <line x1="1.5" y1="7" x2="2.5" y2="7"/>
        <line x1="1.5" y1="9" x2="2.5" y2="9"/>
      </g>
      
      <text x="4" y="8" fill="#ffffff" font-family="Arial, sans-serif" font-size="4" font-weight="bold">25°C</text>
    </g>
    
    <!-- 天气标识 -->
    <g transform="translate(0, 26)">
      <rect x="-18" y="0" width="36" height="6" rx="3" fill="url(#travelRare)" opacity="0.8"/>
      <text x="0" y="4" text-anchor="middle" fill="#ffffff" font-family="Arial, sans-serif" font-size="5" font-weight="bold">ALL WEATHER</text>
    </g>
  </g>
  
  <!-- 底部华丽标识 -->
  <circle cx="64" cy="110" r="12" fill="none" stroke="url(#travelRare)" stroke-width="2.5" opacity="0.7"/>
  <circle cx="64" cy="110" r="8" fill="none" stroke="#42A5F5" stroke-width="1.5" opacity="0.9"/>
  <text x="64" y="114" text-anchor="middle" fill="#0D47A1" font-family="Arial, sans-serif" font-size="8" font-weight="bold" opacity="0.9">天气</text>
</svg>
