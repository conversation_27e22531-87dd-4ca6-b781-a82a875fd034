<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 旅行类稀有级渐变 -->
    <linearGradient id="travelRare" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#42A5F5" />
      <stop offset="25%" style="stop-color:#2196F3" />
      <stop offset="50%" style="stop-color:#1976D2" />
      <stop offset="75%" style="stop-color:#1565C0" />
      <stop offset="100%" style="stop-color:#0D47A1" />
    </linearGradient>
    
    <!-- 飞机渐变 -->
    <linearGradient id="planeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ECEFF1" />
      <stop offset="100%" style="stop-color:#90A4AE" />
    </linearGradient>
    
    <!-- 火车渐变 -->
    <linearGradient id="trainGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4CAF50" />
      <stop offset="100%" style="stop-color:#2E7D32" />
    </linearGradient>
    
    <!-- 汽车渐变 -->
    <linearGradient id="carGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF9800" />
      <stop offset="100%" style="stop-color:#E65100" />
    </linearGradient>
    
    <!-- 轨迹渐变 -->
    <linearGradient id="trailGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFFFFF" opacity="0.8" />
      <stop offset="100%" style="stop-color:#E3F2FD" opacity="0.4" />
    </linearGradient>
  </defs>
  
  <!-- 发光效果 - 多层模拟 -->
  <circle cx="64" cy="64" r="52" fill="url(#travelRare)" opacity="0.1"/>
  <circle cx="64" cy="64" r="50" fill="url(#travelRare)" opacity="0.2"/>
  
  <!-- 外圈边框 -->
  <circle cx="64" cy="64" r="58" fill="none" stroke="url(#travelRare)" stroke-width="2" opacity="0.6"/>
  <circle cx="64" cy="64" r="54" fill="none" stroke="#ffffff" stroke-width="1" opacity="0.4"/>
  
  <!-- 主体背景 -->
  <circle cx="64" cy="64" r="48" fill="url(#travelRare)"/>
  
  <!-- 内圈装饰 -->
  <circle cx="64" cy="64" r="40" fill="none" stroke="#ffffff" stroke-width="1.5" opacity="0.6"/>
  <circle cx="64" cy="64" r="34" fill="none" stroke="#ffffff" stroke-width="1" opacity="0.4"/>
  
  <!-- 交通工具图案 -->
  <g transform="translate(64, 64)">
    <!-- 飞机 - 多层发光升级 -->
    <g transform="translate(0, -12)">
      <!-- 飞机发光底层 -->
      <ellipse cx="0" cy="0" rx="9" ry="2.5" fill="url(#planeGradient)" opacity="0.3"/>
      <ellipse cx="0" cy="0" rx="13" ry="1.3" fill="url(#planeGradient)" opacity="0.2"/>

      <!-- 飞机主体 -->
      <ellipse cx="0" cy="0" rx="8" ry="2" fill="url(#planeGradient)"/>
      <!-- 机翼 -->
      <ellipse cx="0" cy="0" rx="12" ry="1" fill="url(#planeGradient)" opacity="0.8"/>
      <!-- 尾翼 -->
      <path d="M-6,0 L-8,-2 L-6,-1 Z" fill="url(#planeGradient)"/>
      <path d="M-6,0 L-8,2 L-6,1 Z" fill="url(#planeGradient)"/>

      <!-- 窗户 - 发光效果 -->
      <circle cx="2" cy="0" r="1" fill="#81D4FA" opacity="0.3">
        <animate attributeName="opacity" values="0.2;0.5;0.2" dur="2.5s" repeatCount="indefinite"/>
      </circle>
      <circle cx="2" cy="0" r="0.8" fill="#81D4FA">
        <animate attributeName="opacity" values="0.7;1;0.7" dur="2.5s" repeatCount="indefinite"/>
      </circle>

      <circle cx="0" cy="0" r="1" fill="#81D4FA" opacity="0.3">
        <animate attributeName="opacity" values="0.2;0.5;0.2" dur="2.8s" repeatCount="indefinite"/>
      </circle>
      <circle cx="0" cy="0" r="0.8" fill="#81D4FA">
        <animate attributeName="opacity" values="0.7;1;0.7" dur="2.8s" repeatCount="indefinite"/>
      </circle>

      <circle cx="-2" cy="0" r="1" fill="#81D4FA" opacity="0.3">
        <animate attributeName="opacity" values="0.2;0.5;0.2" dur="3.1s" repeatCount="indefinite"/>
      </circle>
      <circle cx="-2" cy="0" r="0.8" fill="#81D4FA">
        <animate attributeName="opacity" values="0.7;1;0.7" dur="3.1s" repeatCount="indefinite"/>
      </circle>

      <!-- 飞行轨迹 - 增强版 -->
      <path d="M12,0 Q18,-3 24,0" stroke="url(#trailGradient)" stroke-width="2" fill="none" opacity="0.2">
        <animate attributeName="opacity" values="0.1;0.4;0.1" dur="2s" repeatCount="indefinite"/>
      </path>
      <path d="M12,0 Q18,-3 24,0" stroke="url(#trailGradient)" stroke-width="1.5" fill="none">
        <animate attributeName="opacity" values="0.3;0.8;0.3" dur="2s" repeatCount="indefinite"/>
      </path>
    </g>
    
    <!-- 火车 -->
    <g transform="translate(-8, 4)">
      <!-- 火车头 -->
      <rect x="0" y="0" width="8" height="4" rx="2" fill="url(#trainGradient)"/>
      <!-- 车厢 -->
      <rect x="9" y="0" width="6" height="4" rx="1" fill="url(#trainGradient)" opacity="0.8"/>
      <!-- 车轮 -->
      <circle cx="2" cy="5" r="1" fill="#424242"/>
      <circle cx="6" cy="5" r="1" fill="#424242"/>
      <circle cx="11" cy="5" r="1" fill="#424242"/>
      <circle cx="13" cy="5" r="1" fill="#424242"/>
      <!-- 窗户 -->
      <rect x="1" y="1" width="1.5" height="1.5" rx="0.3" fill="#81C784"/>
      <rect x="4" y="1" width="1.5" height="1.5" rx="0.3" fill="#81C784"/>
      <rect x="10" y="1" width="1.5" height="1.5" rx="0.3" fill="#81C784"/>
      
      <!-- 铁轨 -->
      <line x1="-5" y1="6" x2="20" y2="6" stroke="#795548" stroke-width="1"/>
      <line x1="-5" y1="7" x2="20" y2="7" stroke="#795548" stroke-width="1"/>
    </g>
    
    <!-- 汽车 -->
    <g transform="translate(8, 12)">
      <!-- 车身 -->
      <rect x="0" y="0" width="10" height="4" rx="2" fill="url(#carGradient)"/>
      <!-- 车顶 -->
      <rect x="2" y="-2" width="6" height="2" rx="1" fill="url(#carGradient)" opacity="0.9"/>
      <!-- 车轮 -->
      <circle cx="2" cy="5" r="1.5" fill="#424242"/>
      <circle cx="8" cy="5" r="1.5" fill="#424242"/>
      <!-- 轮毂 -->
      <circle cx="2" cy="5" r="0.8" fill="#757575"/>
      <circle cx="8" cy="5" r="0.8" fill="#757575"/>
      <!-- 车窗 -->
      <rect x="3" y="-1" width="4" height="1" rx="0.3" fill="#81D4FA"/>
      <!-- 车灯 -->
      <circle cx="10" cy="1" r="0.5" fill="#FFD54F"/>
      <circle cx="10" cy="3" r="0.5" fill="#FF5722"/>
      
      <!-- 道路 -->
      <rect x="-8" y="7" width="25" height="2" fill="#616161"/>
      <rect x="-8" y="8" width="4" height="0.5" fill="#FFFFFF"/>
      <rect x="0" y="8" width="4" height="0.5" fill="#FFFFFF"/>
      <rect x="8" y="8" width="4" height="0.5" fill="#FFFFFF"/>
      <rect x="16" y="8" width="4" height="0.5" fill="#FFFFFF"/>
    </g>
    
    <!-- 交通标识 -->
    <g transform="translate(0, 24)">
      <rect x="-18" y="0" width="36" height="6" rx="3" fill="url(#travelRare)" opacity="0.8"/>
      <text x="0" y="4" text-anchor="middle" fill="#ffffff" font-family="Arial, sans-serif" font-size="5" font-weight="bold">TRANSPORT</text>
    </g>
    
    <!-- 装饰元素 -->
    <g opacity="0.6">
      <!-- 云朵 -->
      <ellipse cx="-20" cy="-18" rx="3" ry="1.5" fill="#FFFFFF">
        <animate attributeName="cx" values="-20;-15;-20" dur="6s" repeatCount="indefinite"/>
      </ellipse>
      <ellipse cx="22" cy="-15" rx="2.5" ry="1.2" fill="#FFFFFF">
        <animate attributeName="cx" values="22;25;22" dur="8s" repeatCount="indefinite"/>
      </ellipse>
      
      <!-- 速度线 -->
      <g stroke="#FFFFFF" stroke-width="0.8" opacity="0.4">
        <line x1="-25" y1="-10" x2="-20" y2="-10">
          <animate attributeName="opacity" values="0;0.6;0" dur="1s" repeatCount="indefinite"/>
        </line>
        <line x1="-25" y1="6" x2="-20" y2="6">
          <animate attributeName="opacity" values="0;0.6;0" dur="1.2s" repeatCount="indefinite"/>
        </line>
        <line x1="20" y1="14" x2="25" y2="14">
          <animate attributeName="opacity" values="0;0.6;0" dur="0.8s" repeatCount="indefinite"/>
        </line>
      </g>
    </g>
  </g>
  
  <!-- 底部华丽标识 -->
  <circle cx="64" cy="110" r="12" fill="none" stroke="url(#travelRare)" stroke-width="2.5" opacity="0.7"/>
  <circle cx="64" cy="110" r="8" fill="none" stroke="#42A5F5" stroke-width="1.5" opacity="0.9"/>
  <text x="64" y="114" text-anchor="middle" fill="#0D47A1" font-family="Arial, sans-serif" font-size="8" font-weight="bold" opacity="0.9">交通</text>
</svg>
