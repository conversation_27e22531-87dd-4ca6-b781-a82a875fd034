<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 使用类稀有级渐变 -->
    <linearGradient id="usageRare" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8E24AA" />
      <stop offset="25%" style="stop-color:#7B1FA2" />
      <stop offset="50%" style="stop-color:#6A1B9A" />
      <stop offset="75%" style="stop-color:#4A148C" />
      <stop offset="100%" style="stop-color:#38006B" />
    </linearGradient>
    
    <!-- 心形渐变 -->
    <radialGradient id="heartGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#E91E63" />
      <stop offset="100%" style="stop-color:#AD1457" />
    </radialGradient>
    
    <!-- 爱心波纹渐变 -->
    <linearGradient id="rippleGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F8BBD9" />
      <stop offset="100%" style="stop-color:#E1BEE7" />
    </linearGradient>
  </defs>
  
  <!-- 发光效果 - 多层模拟 -->
  <circle cx="64" cy="64" r="52" fill="url(#usageRare)" opacity="0.1"/>
  <circle cx="64" cy="64" r="50" fill="url(#usageRare)" opacity="0.2"/>
  
  <!-- 外圈边框 -->
  <circle cx="64" cy="64" r="58" fill="none" stroke="url(#usageRare)" stroke-width="2" opacity="0.6"/>
  <circle cx="64" cy="64" r="54" fill="none" stroke="#ffffff" stroke-width="1" opacity="0.4"/>
  
  <!-- 主体背景 -->
  <circle cx="64" cy="64" r="48" fill="url(#usageRare)"/>
  
  <!-- 内圈装饰 -->
  <circle cx="64" cy="64" r="40" fill="none" stroke="#ffffff" stroke-width="1.5" opacity="0.6"/>
  <circle cx="64" cy="64" r="34" fill="none" stroke="#ffffff" stroke-width="1" opacity="0.4"/>
  
  <!-- 忠实用户图案 -->
  <g transform="translate(64, 64)">
    <!-- 中央爱心 - 多层发光 -->
    <g transform="translate(0, -2)">
      <!-- 爱心发光底层 -->
      <path d="M0,8 C-8,0 -16,-8 -8,-16 C-4,-20 0,-16 0,-12 C0,-16 4,-20 8,-16 C16,-8 8,0 0,8 Z" fill="url(#heartGradient)" opacity="0.3">
        <animate attributeName="opacity" values="0.2;0.4;0.2" dur="1.5s" repeatCount="indefinite"/>
      </path>
      
      <!-- 主爱心 -->
      <path d="M0,6 C-6,0 -12,-6 -6,-12 C-3,-15 0,-12 0,-9 C0,-12 3,-15 6,-12 C12,-6 6,0 0,6 Z" fill="url(#heartGradient)">
        <animate attributeName="transform" values="scale(1);scale(1.1);scale(1)" dur="1.2s" repeatCount="indefinite"/>
      </path>
      
      <!-- 爱心高光 -->
      <ellipse cx="-2" cy="-6" rx="2" ry="1" fill="#FFFFFF" opacity="0.6">
        <animate attributeName="opacity" values="0.4;0.8;0.4" dur="1s" repeatCount="indefinite"/>
      </ellipse>
    </g>
    
    <!-- 忠诚度环 -->
    <g opacity="0.8">
      <circle cx="0" cy="0" r="20" fill="none" stroke="url(#heartGradient)" stroke-width="2" stroke-dasharray="4,2">
        <animate attributeName="stroke-dashoffset" values="0;-12" dur="3s" repeatCount="indefinite"/>
      </circle>
      
      <!-- 忠诚度标记 -->
      <g>
        <circle cx="0" cy="-20" r="2" fill="url(#heartGradient)">
          <animate attributeName="opacity" values="0.6;1;0.6" dur="1.5s" repeatCount="indefinite"/>
        </circle>
        <circle cx="20" cy="0" r="2" fill="url(#heartGradient)">
          <animate attributeName="opacity" values="0.5;0.9;0.5" dur="1.8s" repeatCount="indefinite"/>
        </circle>
        <circle cx="0" cy="20" r="2" fill="url(#heartGradient)">
          <animate attributeName="opacity" values="0.7;1;0.7" dur="1.3s" repeatCount="indefinite"/>
        </circle>
        <circle cx="-20" cy="0" r="2" fill="url(#heartGradient)">
          <animate attributeName="opacity" values="0.4;0.8;0.4" dur="2s" repeatCount="indefinite"/>
        </circle>
        
        <animateTransform attributeName="transform" type="rotate" values="0;360" dur="8s" repeatCount="indefinite"/>
      </g>
    </g>
    
    <!-- 使用天数显示 -->
    <g transform="translate(0, 12)">
      <rect x="-8" y="0" width="16" height="6" rx="3" fill="url(#heartGradient)" opacity="0.8"/>
      <text x="0" y="4" text-anchor="middle" fill="#FFFFFF" font-family="Arial, sans-serif" font-size="4" font-weight="bold">15天</text>
    </g>
    
    <!-- 爱心波纹 -->
    <g opacity="0.6">
      <circle cx="0" cy="0" r="25" fill="none" stroke="url(#rippleGradient)" stroke-width="1">
        <animate attributeName="r" values="20;30;20" dur="2s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.8;0.2;0.8" dur="2s" repeatCount="indefinite"/>
      </circle>
      <circle cx="0" cy="0" r="20" fill="none" stroke="url(#rippleGradient)" stroke-width="0.8">
        <animate attributeName="r" values="15;25;15" dur="2.5s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.6;0.1;0.6" dur="2.5s" repeatCount="indefinite"/>
      </circle>
    </g>
    
    <!-- 小爱心装饰 -->
    <g opacity="0.7">
      <g transform="translate(-18, -12)">
        <path d="M0,2 C-2,0 -4,-2 -2,-4 C-1,-5 0,-4 0,-3 C0,-4 1,-5 2,-4 C4,-2 2,0 0,2 Z" fill="url(#heartGradient)">
          <animate attributeName="opacity" values="0.5;1;0.5" dur="2s" repeatCount="indefinite"/>
        </path>
      </g>
      
      <g transform="translate(18, -8)">
        <path d="M0,1.5 C-1.5,0 -3,-1.5 -1.5,-3 C-0.75,-3.75 0,-3 0,-2.25 C0,-3 0.75,-3.75 1.5,-3 C3,-1.5 1.5,0 0,1.5 Z" fill="url(#heartGradient)">
          <animate attributeName="opacity" values="0.4;0.9;0.4" dur="2.5s" repeatCount="indefinite"/>
        </path>
      </g>
      
      <g transform="translate(-12, 18)">
        <path d="M0,1.2 C-1.2,0 -2.4,-1.2 -1.2,-2.4 C-0.6,-3 0,-2.4 0,-1.8 C0,-2.4 0.6,-3 1.2,-2.4 C2.4,-1.2 1.2,0 0,1.2 Z" fill="url(#heartGradient)">
          <animate attributeName="opacity" values="0.6;1;0.6" dur="1.8s" repeatCount="indefinite"/>
        </path>
      </g>
      
      <g transform="translate(15, 15)">
        <path d="M0,1.8 C-1.8,0 -3.6,-1.8 -1.8,-3.6 C-0.9,-4.5 0,-3.6 0,-2.7 C0,-3.6 0.9,-4.5 1.8,-3.6 C3.6,-1.8 1.8,0 0,1.8 Z" fill="url(#heartGradient)">
          <animate attributeName="opacity" values="0.3;0.8;0.3" dur="2.2s" repeatCount="indefinite"/>
        </path>
      </g>
    </g>
    
    <!-- 忠实标识 -->
    <g transform="translate(0, 22)">
      <rect x="-12" y="0" width="24" height="5" rx="2.5" fill="url(#heartGradient)" opacity="0.8"/>
      <text x="0" y="3" text-anchor="middle" fill="#FFFFFF" font-family="Arial, sans-serif" font-size="3" font-weight="bold">LOYAL</text>
    </g>
  </g>
  
  <!-- 底部华丽标识 -->
  <circle cx="64" cy="110" r="12" fill="none" stroke="url(#usageRare)" stroke-width="2.5" opacity="0.7"/>
  <circle cx="64" cy="110" r="8" fill="none" stroke="#8E24AA" stroke-width="1.5" opacity="0.9"/>
  <text x="64" y="114" text-anchor="middle" fill="#38006B" font-family="Arial, sans-serif" font-size="8" font-weight="bold" opacity="0.9">忠实</text>
</svg>
