<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 使用类传说级渐变 -->
    <linearGradient id="usageLegendary" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4A148C" />
      <stop offset="15%" style="stop-color:#6A1B9A" />
      <stop offset="30%" style="stop-color:#7B1FA2" />
      <stop offset="45%" style="stop-color:#8E24AA" />
      <stop offset="60%" style="stop-color:#9C27B0" />
      <stop offset="75%" style="stop-color:#AB47BC" />
      <stop offset="90%" style="stop-color:#BA68C8" />
      <stop offset="100%" style="stop-color:#4A148C" />
    </linearGradient>
    
    <!-- 彩虹光环效果 -->
    <linearGradient id="rainbowAura" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ff6b6b" opacity="0.9" />
      <stop offset="12%" style="stop-color:#ff9ff3" opacity="0.9" />
      <stop offset="24%" style="stop-color:#feca57" opacity="0.9" />
      <stop offset="36%" style="stop-color:#48dbfb" opacity="0.9" />
      <stop offset="48%" style="stop-color:#0abde3" opacity="0.9" />
      <stop offset="60%" style="stop-color:#006ba6" opacity="0.9" />
      <stop offset="72%" style="stop-color:#8e44ad" opacity="0.9" />
      <stop offset="84%" style="stop-color:#c44569" opacity="0.9" />
      <stop offset="100%" style="stop-color:#ff6b6b" opacity="0.9" />
    </linearGradient>
    
    <!-- 液态金属效果 -->
    <radialGradient id="liquidMetal" cx="25%" cy="25%" r="85%">
      <stop offset="0%" style="stop-color:#ffffff" />
      <stop offset="15%" style="stop-color:#f3e5f5" />
      <stop offset="30%" style="stop-color:#e1bee7" />
      <stop offset="50%" style="stop-color:#ce93d8" />
      <stop offset="70%" style="stop-color:#ba68c8" />
      <stop offset="85%" style="stop-color:#ab47bc" />
      <stop offset="100%" style="stop-color:#9c27b0" />
    </radialGradient>
    
    <!-- 传奇使用者渐变 -->
    <radialGradient id="legendUser" cx="30%" cy="30%" r="70%">
      <stop offset="0%" style="stop-color:#FFFFFF" />
      <stop offset="30%" style="stop-color:#E91E63" />
      <stop offset="70%" style="stop-color:#9C27B0" />
      <stop offset="100%" style="stop-color:#4A148C" />
    </radialGradient>
    
    <!-- 应用图标渐变 -->
    <linearGradient id="appGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF9800" />
      <stop offset="50%" style="stop-color:#E91E63" />
      <stop offset="100%" style="stop-color:#9C27B0" />
    </linearGradient>
  </defs>
  
  <!-- 发光效果 - 多层模拟 -->
  <circle cx="64" cy="64" r="54" fill="url(#usageLegendary)" opacity="0.1"/>
  <circle cx="64" cy="64" r="52" fill="url(#usageLegendary)" opacity="0.15"/>
  <circle cx="64" cy="64" r="50" fill="url(#usageLegendary)" opacity="0.2"/>
  
  <!-- 至尊彩虹光环 - 微扩张优化 -->
  <circle cx="64" cy="64" r="61.5" fill="none" stroke="url(#rainbowAura)" stroke-width="3" opacity="0.8" stroke-linecap="round">
    <animate attributeName="r" values="60.8;62.2;60.8" dur="3s" repeatCount="indefinite"/>
    <animate attributeName="stroke-width" values="2.8;3.2;2.8" dur="3s" repeatCount="indefinite"/>
    <animate attributeName="opacity" values="0.7;0.9;0.7" dur="3s" repeatCount="indefinite"/>
  </circle>
  
  <!-- 传说级光环 -->
  <circle cx="64" cy="64" r="56.5" fill="none" stroke="url(#usageLegendary)" stroke-width="2.2" opacity="0.85" stroke-linecap="round">
    <animate attributeName="opacity" values="0.7;1;0.7" dur="2s" repeatCount="indefinite"/>
    <animate attributeName="stroke-width" values="1.8;2.8;1.8" dur="2s" repeatCount="indefinite"/>
  </circle>
  
  <!-- 边界过渡层 -->
  <circle cx="64" cy="64" r="52.5" fill="url(#liquidMetal)" opacity="0.08"/>
  <circle cx="64" cy="64" r="50.5" fill="url(#liquidMetal)" opacity="0.15"/>
  
  <!-- 主体背景 - 精确边界 -->
  <circle cx="64" cy="64" r="48" fill="url(#liquidMetal)"/>
  
  <!-- 内圈装饰 - 精细化边界 -->
  <circle cx="64" cy="64" r="40.3" fill="none" stroke="#fff" stroke-width="1.6" opacity="0.7" stroke-linecap="round"/>
  <circle cx="64" cy="64" r="35.2" fill="none" stroke="#fff" stroke-width="0.9" opacity="0.5" stroke-linecap="round"/>
  <circle cx="64" cy="64" r="30.5" fill="none" stroke="#fff" stroke-width="0.6" opacity="0.3" stroke-linecap="round"/>
  
  <!-- 传奇使用者图案 - 传说级设计 -->
  <g transform="translate(64, 64)">
    <!-- 中央应用图标 - 多层发光 -->
    <rect x="-12" y="-12" width="24" height="24" rx="6" fill="url(#appGradient)" opacity="0.3">
      <animate attributeName="width" values="24;26;24" dur="4s" repeatCount="indefinite"/>
      <animate attributeName="height" values="24;26;24" dur="4s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.2;0.4;0.2" dur="4s" repeatCount="indefinite"/>
    </rect>
    <rect x="-10" y="-10" width="20" height="20" rx="5" fill="url(#appGradient)" opacity="0.5"/>
    <rect x="-8" y="-8" width="16" height="16" rx="4" fill="url(#appGradient)"/>
    
    <!-- 应用内容 -->
    <g opacity="0.9">
      <!-- 应用图标 -->
      <circle cx="0" cy="-3" r="3" fill="#FFFFFF" opacity="0.8"/>
      <rect x="-2" y="-1" width="4" height="1" rx="0.5" fill="#FFFFFF" opacity="0.6"/>
      <rect x="-3" y="1" width="6" height="1" rx="0.5" fill="#FFFFFF" opacity="0.6"/>
      <rect x="-2" y="3" width="4" height="1" rx="0.5" fill="#FFFFFF" opacity="0.6"/>
    </g>
    
    <!-- 使用统计环 -->
    <g opacity="0.8">
      <!-- 外环 - 总使用时间 -->
      <circle cx="0" cy="0" r="20" fill="none" stroke="url(#legendUser)" stroke-width="3">
        <animate attributeName="r" values="18;22;18" dur="5s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.6;1;0.6" dur="5s" repeatCount="indefinite"/>
      </circle>
      
      <!-- 中环 - 活跃度 -->
      <circle cx="0" cy="0" r="16" fill="none" stroke="url(#legendUser)" stroke-width="2" opacity="0.7">
        <animate attributeName="r" values="14;18;14" dur="4s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.5;0.9;0.5" dur="4s" repeatCount="indefinite"/>
      </circle>
      
      <!-- 内环 - 连续使用 -->
      <circle cx="0" cy="0" r="12" fill="none" stroke="url(#legendUser)" stroke-width="1.5" opacity="0.6">
        <animate attributeName="r" values="10;14;10" dur="3s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.4;0.8;0.4" dur="3s" repeatCount="indefinite"/>
      </circle>
    </g>
    
    <!-- 成就标记 -->
    <g opacity="0.8">
      <!-- 1000天成就 -->
      <g transform="translate(-22, -8)">
        <circle cx="0" cy="0" r="4" fill="url(#legendUser)" opacity="0.3">
          <animate attributeName="opacity" values="0.2;0.5;0.2" dur="2s" repeatCount="indefinite"/>
        </circle>
        <circle cx="0" cy="0" r="3.5" fill="url(#legendUser)"/>
        <text x="0" y="1" text-anchor="middle" fill="#fff" font-family="Arial, sans-serif" font-size="2" font-weight="bold">1K</text>
      </g>
      
      <!-- 完美使用成就 -->
      <g transform="translate(22, -8)">
        <circle cx="0" cy="0" r="4" fill="url(#legendUser)" opacity="0.3">
          <animate attributeName="opacity" values="0.2;0.5;0.2" dur="2.3s" repeatCount="indefinite"/>
        </circle>
        <circle cx="0" cy="0" r="3.5" fill="url(#legendUser)"/>
        <path d="M-1.5,-0.5 L-0.5,0.5 L1.5,-1.5" stroke="#fff" stroke-width="0.8" fill="none"/>
      </g>
      
      <!-- 分享成就 -->
      <g transform="translate(-22, 8)">
        <circle cx="0" cy="0" r="4" fill="url(#legendUser)" opacity="0.3">
          <animate attributeName="opacity" values="0.2;0.5;0.2" dur="2.5s" repeatCount="indefinite"/>
        </circle>
        <circle cx="0" cy="0" r="3.5" fill="url(#legendUser)"/>
        <path d="M-1,0 L1,-1 L1,1 Z" fill="#fff"/>
      </g>
      
      <!-- 专家成就 -->
      <g transform="translate(22, 8)">
        <circle cx="0" cy="0" r="4" fill="url(#legendUser)" opacity="0.3">
          <animate attributeName="opacity" values="0.2;0.5;0.2" dur="1.8s" repeatCount="indefinite"/>
        </circle>
        <circle cx="0" cy="0" r="3.5" fill="url(#legendUser)"/>
        <path d="M0,-2 L1,0 L0,2 L-1,0 Z" fill="#fff"/>
      </g>
    </g>
    
    <!-- 传奇等级显示 -->
    <g transform="translate(0, 0)" opacity="0.9">
      <rect x="-10" y="-3" width="20" height="6" rx="3" fill="url(#usageLegendary)" opacity="0.8"/>
      <text x="0" y="1" text-anchor="middle" fill="#ffffff" font-family="Arial, sans-serif" font-size="4" font-weight="bold">LEGEND</text>
    </g>
    
    <!-- 神圣光芒 - 边界控制优化 -->
    <g opacity="0.7">
      <!-- 主光芒 - 边界控制 -->
      <line x1="0" y1="-21.5" x2="0" y2="-28" stroke="#E91E63" stroke-width="2" stroke-linecap="round">
        <animate attributeName="y2" values="-28;-32;-28" dur="2s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.5;1;0.5" dur="2s" repeatCount="indefinite"/>
      </line>
      
      <!-- 侧光芒 - 边界控制 -->
      <line x1="-2.8" y1="-19.5" x2="-5.5" y2="-26" stroke="#E91E63" stroke-width="1.5" stroke-linecap="round">
        <animate attributeName="opacity" values="0.4;0.9;0.4" dur="1.8s" repeatCount="indefinite"/>
      </line>
      <line x1="2.8" y1="-19.5" x2="5.5" y2="-26" stroke="#E91E63" stroke-width="1.5" stroke-linecap="round">
        <animate attributeName="opacity" values="0.4;0.9;0.4" dur="2.2s" repeatCount="indefinite"/>
      </line>
      
      <!-- 扩散光芒 - 边界控制 -->
      <line x1="-4.5" y1="-17.8" x2="-8.5" y2="-23.5" stroke="#9C27B0" stroke-width="1" stroke-linecap="round">
        <animate attributeName="opacity" values="0.3;0.7;0.3" dur="2.5s" repeatCount="indefinite"/>
      </line>
      <line x1="4.5" y1="-17.8" x2="8.5" y2="-23.5" stroke="#9C27B0" stroke-width="1" stroke-linecap="round">
        <animate attributeName="opacity" values="0.3;0.7;0.3" dur="1.9s" repeatCount="indefinite"/>
      </line>
    </g>
    
    <!-- 使用数据显示 -->
    <g transform="translate(0, 24)" opacity="0.9">
      <rect x="-15" y="0" width="30" height="5" rx="2.5" fill="url(#usageLegendary)" opacity="0.8"/>
      <text x="0" y="3" text-anchor="middle" fill="#ffffff" font-family="Arial, sans-serif" font-size="3" font-weight="bold">365天 · 10000小时</text>
    </g>
    
    <!-- 传奇标识 -->
    <g transform="translate(0, 32)">
      <rect x="-18" y="0" width="36" height="6" rx="3" fill="url(#legendUser)" opacity="0.8"/>
      <text x="0" y="4" text-anchor="middle" fill="#ffffff" font-family="Arial, sans-serif" font-size="5" font-weight="bold">POWER USER</text>
    </g>
  </g>
  
  <!-- 粒子效果 - 增强版 -->
  <g opacity="0.8">
    <circle cx="20" cy="20" r="2" fill="#E91E63" opacity="0.3">
      <animate attributeName="cy" values="20;15;20" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;0.8;0" dur="3s" repeatCount="indefinite"/>
    </circle>
    <circle cx="20" cy="20" r="1.5" fill="#E91E63">
      <animate attributeName="cy" values="20;15;20" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;0" dur="3s" repeatCount="indefinite"/>
    </circle>
    
    <circle cx="108" cy="30" r="1.8" fill="#9C27B0" opacity="0.3">
      <animate attributeName="cx" values="108;103;108" dur="2.5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;0.7;0" dur="2.5s" repeatCount="indefinite"/>
    </circle>
    <circle cx="108" cy="30" r="1.2" fill="#9C27B0">
      <animate attributeName="cx" values="108;103;108" dur="2.5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;0" dur="2.5s" repeatCount="indefinite"/>
    </circle>
    
    <circle cx="15" cy="100" r="2.2" fill="#AB47BC" opacity="0.3">
      <animate attributeName="cy" values="100;105;100" dur="2.8s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;0.6;0" dur="2.8s" repeatCount="indefinite"/>
    </circle>
    <circle cx="15" cy="100" r="1.6" fill="#AB47BC">
      <animate attributeName="cy" values="100;105;100" dur="2.8s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;0" dur="2.8s" repeatCount="indefinite"/>
    </circle>
    
    <circle cx="110" cy="95" r="1.5" fill="#BA68C8" opacity="0.3">
      <animate attributeName="cx" values="110;115;110" dur="3.2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;0.5;0" dur="3.2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="110" cy="95" r="1" fill="#BA68C8">
      <animate attributeName="cx" values="110;115;110" dur="3.2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;0" dur="3.2s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- 底部华丽标识 -->
  <circle cx="64" cy="115" r="12" fill="none" stroke="url(#usageLegendary)" stroke-width="3" opacity="0.8"/>
  <circle cx="64" cy="115" r="8" fill="none" stroke="#E91E63" stroke-width="2" opacity="0.9"/>
  <text x="64" y="119" text-anchor="middle" fill="#4A148C" font-family="Arial, sans-serif" font-size="9" font-weight="bold">传奇</text>
</svg>
