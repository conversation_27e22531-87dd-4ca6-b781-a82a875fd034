<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 特殊类完美级渐变 -->
    <linearGradient id="specialPerfect" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFD700" />
      <stop offset="15%" style="stop-color:#FFC107" />
      <stop offset="30%" style="stop-color:#FF9800" />
      <stop offset="45%" style="stop-color:#FF5722" />
      <stop offset="60%" style="stop-color:#E91E63" />
      <stop offset="75%" style="stop-color:#9C27B0" />
      <stop offset="90%" style="stop-color:#3F51B5" />
      <stop offset="100%" style="stop-color:#FFD700" />
    </linearGradient>
    
    <!-- 彩虹光环效果 - 完美版 -->
    <linearGradient id="perfectAura" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ff6b6b" opacity="1" />
      <stop offset="8%" style="stop-color:#ff9ff3" opacity="1" />
      <stop offset="16%" style="stop-color:#feca57" opacity="1" />
      <stop offset="24%" style="stop-color:#48dbfb" opacity="1" />
      <stop offset="32%" style="stop-color:#00ffff" opacity="1" />
      <stop offset="40%" style="stop-color:#0abde3" opacity="1" />
      <stop offset="48%" style="stop-color:#006ba6" opacity="1" />
      <stop offset="56%" style="stop-color:#8e44ad" opacity="1" />
      <stop offset="64%" style="stop-color:#c44569" opacity="1" />
      <stop offset="72%" style="stop-color:#e74c3c" opacity="1" />
      <stop offset="80%" style="stop-color:#f39c12" opacity="1" />
      <stop offset="88%" style="stop-color:#2ecc71" opacity="1" />
      <stop offset="96%" style="stop-color:#3498db" opacity="1" />
      <stop offset="100%" style="stop-color:#ff6b6b" opacity="1" />
    </linearGradient>
    
    <!-- 液态钻石效果 -->
    <radialGradient id="liquidDiamond" cx="20%" cy="20%" r="90%">
      <stop offset="0%" style="stop-color:#ffffff" />
      <stop offset="10%" style="stop-color:#f8f9fa" />
      <stop offset="20%" style="stop-color:#e3f2fd" />
      <stop offset="35%" style="stop-color:#bbdefb" />
      <stop offset="50%" style="stop-color:#90caf9" />
      <stop offset="65%" style="stop-color:#64b5f6" />
      <stop offset="80%" style="stop-color:#42a5f5" />
      <stop offset="95%" style="stop-color:#2196f3" />
      <stop offset="100%" style="stop-color:#1976d2" />
    </radialGradient>
    
    <!-- 完美宝石渐变 -->
    <radialGradient id="perfectGem" cx="25%" cy="25%" r="75%">
      <stop offset="0%" style="stop-color:#FFFFFF" />
      <stop offset="20%" style="stop-color:#FFD700" />
      <stop offset="40%" style="stop-color:#FF9800" />
      <stop offset="60%" style="stop-color:#E91E63" />
      <stop offset="80%" style="stop-color:#9C27B0" />
      <stop offset="100%" style="stop-color:#3F51B5" />
    </radialGradient>
    
    <!-- 完美光芒渐变 -->
    <linearGradient id="perfectRay" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFFFFF" />
      <stop offset="50%" style="stop-color:#FFD700" />
      <stop offset="100%" style="stop-color:#FF9800" />
    </linearGradient>
  </defs>
  
  <!-- 发光效果 - 多层模拟 -->
  <circle cx="64" cy="64" r="56" fill="url(#specialPerfect)" opacity="0.08"/>
  <circle cx="64" cy="64" r="54" fill="url(#specialPerfect)" opacity="0.12"/>
  <circle cx="64" cy="64" r="52" fill="url(#specialPerfect)" opacity="0.16"/>
  <circle cx="64" cy="64" r="50" fill="url(#specialPerfect)" opacity="0.2"/>
  
  <!-- 至尊彩虹光环 - 完美版 -->
  <circle cx="64" cy="64" r="62.5" fill="none" stroke="url(#perfectAura)" stroke-width="4" opacity="0.9" stroke-linecap="round">
    <animate attributeName="r" values="61.8;63.2;61.8" dur="2.5s" repeatCount="indefinite"/>
    <animate attributeName="stroke-width" values="3.5;4.5;3.5" dur="2.5s" repeatCount="indefinite"/>
    <animate attributeName="opacity" values="0.8;1;0.8" dur="2.5s" repeatCount="indefinite"/>
  </circle>
  
  <!-- 完美级光环 -->
  <circle cx="64" cy="64" r="57.8" fill="none" stroke="url(#specialPerfect)" stroke-width="2.5" opacity="0.9" stroke-linecap="round">
    <animate attributeName="opacity" values="0.8;1;0.8" dur="1.8s" repeatCount="indefinite"/>
    <animate attributeName="stroke-width" values="2;3;2" dur="1.8s" repeatCount="indefinite"/>
  </circle>
  
  <!-- 钻石折射光环 -->
  <circle cx="64" cy="64" r="54.5" fill="none" stroke="url(#liquidDiamond)" stroke-width="1.8" opacity="0.7" stroke-linecap="round">
    <animate attributeName="opacity" values="0.5;0.9;0.5" dur="2.2s" repeatCount="indefinite"/>
  </circle>
  
  <!-- 边界过渡层 -->
  <circle cx="64" cy="64" r="52.8" fill="url(#liquidDiamond)" opacity="0.06"/>
  <circle cx="64" cy="64" r="51.2" fill="url(#liquidDiamond)" opacity="0.12"/>
  <circle cx="64" cy="64" r="49.8" fill="url(#liquidDiamond)" opacity="0.18"/>
  
  <!-- 主体背景 - 精确边界 -->
  <circle cx="64" cy="64" r="48" fill="url(#liquidDiamond)"/>
  
  <!-- 内圈装饰 - 精细化边界 -->
  <circle cx="64" cy="64" r="41.5" fill="none" stroke="#fff" stroke-width="1.8" opacity="0.8" stroke-linecap="round"/>
  <circle cx="64" cy="64" r="36.8" fill="none" stroke="#fff" stroke-width="1.2" opacity="0.6" stroke-linecap="round"/>
  <circle cx="64" cy="64" r="32.5" fill="none" stroke="#fff" stroke-width="0.8" opacity="0.4" stroke-linecap="round"/>
  <circle cx="64" cy="64" r="28.8" fill="none" stroke="#fff" stroke-width="0.5" opacity="0.2" stroke-linecap="round"/>
  
  <!-- 完美图案 - 特殊级设计 -->
  <g transform="translate(64, 64)">
    <!-- 中央完美宝石 - 多层发光 -->
    <g>
      <!-- 宝石发光底层 -->
      <circle cx="0" cy="0" r="25" fill="url(#perfectGem)" opacity="0.2">
        <animate attributeName="r" values="23;27;23" dur="4s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.15;0.3;0.15" dur="4s" repeatCount="indefinite"/>
      </circle>
      <circle cx="0" cy="0" r="22" fill="url(#perfectGem)" opacity="0.4"/>
      <circle cx="0" cy="0" r="20" fill="url(#perfectGem)" opacity="0.6"/>
      <circle cx="0" cy="0" r="18" fill="url(#perfectGem)"/>
      
      <!-- 宝石切面 -->
      <g opacity="0.8">
        <path d="M0,-18 L-9,0 L0,18 L9,0 Z" fill="url(#perfectGem)" opacity="0.3"/>
        <path d="M0,-15 L-7.5,0 L0,15 L7.5,0 Z" fill="url(#perfectGem)" opacity="0.5"/>
        <path d="M0,-12 L-6,0 L0,12 L6,0 Z" fill="url(#perfectGem)" opacity="0.7"/>
        
        <!-- 宝石反光 -->
        <ellipse cx="-6" cy="-6" rx="3" ry="1.5" fill="#FFFFFF" opacity="0.8">
          <animate attributeName="opacity" values="0.6;1;0.6" dur="2s" repeatCount="indefinite"/>
        </ellipse>
        <ellipse cx="4" cy="-8" rx="2" ry="1" fill="#FFFFFF" opacity="0.6">
          <animate attributeName="opacity" values="0.4;0.8;0.4" dur="2.5s" repeatCount="indefinite"/>
        </ellipse>
      </g>
    </g>
    
    <!-- 完美标识 -->
    <g transform="translate(0, 0)" opacity="0.95">
      <rect x="-12" y="-3" width="24" height="6" rx="3" fill="url(#specialPerfect)" opacity="0.9"/>
      <text x="0" y="1" text-anchor="middle" fill="#ffffff" font-family="Arial, sans-serif" font-size="4" font-weight="bold">PERFECT</text>
    </g>
    
    <!-- 完美光芒 - 边界控制优化 -->
    <g opacity="0.8">
      <!-- 主光芒 - 8方向 -->
      <line x1="0" y1="-25" x2="0" y2="-32" stroke="url(#perfectRay)" stroke-width="3" stroke-linecap="round">
        <animate attributeName="y2" values="-32;-36;-32" dur="2s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.6;1;0.6" dur="2s" repeatCount="indefinite"/>
      </line>
      <line x1="18" y1="-18" x2="25" y2="-25" stroke="url(#perfectRay)" stroke-width="2.5" stroke-linecap="round">
        <animate attributeName="x2" values="25;28;25" dur="2.2s" repeatCount="indefinite"/>
        <animate attributeName="y2" values="-25;-28;-25" dur="2.2s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.5;0.9;0.5" dur="2.2s" repeatCount="indefinite"/>
      </line>
      <line x1="25" y1="0" x2="32" y2="0" stroke="url(#perfectRay)" stroke-width="3" stroke-linecap="round">
        <animate attributeName="x2" values="32;36;32" dur="1.8s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.6;1;0.6" dur="1.8s" repeatCount="indefinite"/>
      </line>
      <line x1="18" y1="18" x2="25" y2="25" stroke="url(#perfectRay)" stroke-width="2.5" stroke-linecap="round">
        <animate attributeName="x2" values="25;28;25" dur="2.4s" repeatCount="indefinite"/>
        <animate attributeName="y2" values="25;28;25" dur="2.4s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.5;0.9;0.5" dur="2.4s" repeatCount="indefinite"/>
      </line>
      <line x1="0" y1="25" x2="0" y2="32" stroke="url(#perfectRay)" stroke-width="3" stroke-linecap="round">
        <animate attributeName="y2" values="32;36;32" dur="2.1s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.6;1;0.6" dur="2.1s" repeatCount="indefinite"/>
      </line>
      <line x1="-18" y1="18" x2="-25" y2="25" stroke="url(#perfectRay)" stroke-width="2.5" stroke-linecap="round">
        <animate attributeName="x2" values="-25;-28;-25" dur="2.3s" repeatCount="indefinite"/>
        <animate attributeName="y2" values="25;28;25" dur="2.3s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.5;0.9;0.5" dur="2.3s" repeatCount="indefinite"/>
      </line>
      <line x1="-25" y1="0" x2="-32" y2="0" stroke="url(#perfectRay)" stroke-width="3" stroke-linecap="round">
        <animate attributeName="x2" values="-32;-36;-32" dur="1.9s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.6;1;0.6" dur="1.9s" repeatCount="indefinite"/>
      </line>
      <line x1="-18" y1="-18" x2="-25" y2="-25" stroke="url(#perfectRay)" stroke-width="2.5" stroke-linecap="round">
        <animate attributeName="x2" values="-25;-28;-25" dur="2.5s" repeatCount="indefinite"/>
        <animate attributeName="y2" values="-25;-28;-25" dur="2.5s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.5;0.9;0.5" dur="2.5s" repeatCount="indefinite"/>
      </line>
    </g>
    
    <!-- 完美成就环 -->
    <g opacity="0.9">
      <!-- 100%完成 -->
      <g transform="translate(-28, -15)">
        <circle cx="0" cy="0" r="5" fill="url(#perfectGem)" opacity="0.3">
          <animate attributeName="opacity" values="0.2;0.5;0.2" dur="2s" repeatCount="indefinite"/>
        </circle>
        <circle cx="0" cy="0" r="4.5" fill="url(#perfectGem)"/>
        <text x="0" y="1" text-anchor="middle" fill="#fff" font-family="Arial, sans-serif" font-size="2.5" font-weight="bold">100%</text>
      </g>
      
      <!-- 零错误 -->
      <g transform="translate(28, -15)">
        <circle cx="0" cy="0" r="5" fill="url(#perfectGem)" opacity="0.3">
          <animate attributeName="opacity" values="0.2;0.5;0.2" dur="2.3s" repeatCount="indefinite"/>
        </circle>
        <circle cx="0" cy="0" r="4.5" fill="url(#perfectGem)"/>
        <text x="0" y="1" text-anchor="middle" fill="#fff" font-family="Arial, sans-serif" font-size="3" font-weight="bold">0</text>
        <circle cx="0" cy="0" r="3" fill="none" stroke="#fff" stroke-width="0.5"/>
        <line x1="-2" y1="-2" x2="2" y2="2" stroke="#fff" stroke-width="0.8"/>
      </g>
      
      <!-- 满分评价 -->
      <g transform="translate(-28, 15)">
        <circle cx="0" cy="0" r="5" fill="url(#perfectGem)" opacity="0.3">
          <animate attributeName="opacity" values="0.2;0.5;0.2" dur="2.5s" repeatCount="indefinite"/>
        </circle>
        <circle cx="0" cy="0" r="4.5" fill="url(#perfectGem)"/>
        <path d="M0,-3 L1,-1 L3,0 L1,1 L0,3 L-1,1 L-3,0 L-1,-1 Z" fill="#fff"/>
      </g>
      
      <!-- 完美时机 -->
      <g transform="translate(28, 15)">
        <circle cx="0" cy="0" r="5" fill="url(#perfectGem)" opacity="0.3">
          <animate attributeName="opacity" values="0.2;0.5;0.2" dur="1.8s" repeatCount="indefinite"/>
        </circle>
        <circle cx="0" cy="0" r="4.5" fill="url(#perfectGem)"/>
        <circle cx="0" cy="0" r="3" fill="none" stroke="#fff" stroke-width="0.8"/>
        <line x1="0" y1="0" x2="0" y2="-2" stroke="#fff" stroke-width="0.8"/>
        <line x1="0" y1="0" x2="1.5" y2="0" stroke="#fff" stroke-width="0.6"/>
      </g>
    </g>
    
    <!-- 完美标识 -->
    <g transform="translate(0, 35)">
      <rect x="-20" y="0" width="40" height="6" rx="3" fill="url(#perfectGem)" opacity="0.9"/>
      <text x="0" y="4" text-anchor="middle" fill="#ffffff" font-family="Arial, sans-serif" font-size="5" font-weight="bold">FLAWLESS</text>
    </g>
  </g>
  
  <!-- 粒子效果 - 完美版 -->
  <g opacity="0.9">
    <circle cx="25" cy="25" r="2.5" fill="#FFD700" opacity="0.3">
      <animate attributeName="cy" values="25;20;25" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;0" dur="3s" repeatCount="indefinite"/>
    </circle>
    <circle cx="25" cy="25" r="2" fill="#FFD700">
      <animate attributeName="cy" values="25;20;25" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;0" dur="3s" repeatCount="indefinite"/>
    </circle>
    
    <circle cx="103" cy="35" r="2.2" fill="#FF9800" opacity="0.3">
      <animate attributeName="cx" values="103;98;103" dur="2.5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;0.8;0" dur="2.5s" repeatCount="indefinite"/>
    </circle>
    <circle cx="103" cy="35" r="1.8" fill="#FF9800">
      <animate attributeName="cx" values="103;98;103" dur="2.5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;0" dur="2.5s" repeatCount="indefinite"/>
    </circle>
    
    <circle cx="20" cy="103" r="2.8" fill="#E91E63" opacity="0.3">
      <animate attributeName="cy" values="103;108;103" dur="2.8s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;0.7;0" dur="2.8s" repeatCount="indefinite"/>
    </circle>
    <circle cx="20" cy="103" r="2.3" fill="#E91E63">
      <animate attributeName="cy" values="103;108;103" dur="2.8s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;0" dur="2.8s" repeatCount="indefinite"/>
    </circle>
    
    <circle cx="108" cy="93" r="2" fill="#9C27B0" opacity="0.3">
      <animate attributeName="cx" values="108;113;108" dur="3.2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;0.6;0" dur="3.2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="108" cy="93" r="1.5" fill="#9C27B0">
      <animate attributeName="cx" values="108;113;108" dur="3.2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;0" dur="3.2s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- 底部华丽标识 -->
  <circle cx="64" cy="118" r="12" fill="none" stroke="url(#specialPerfect)" stroke-width="3.5" opacity="0.9"/>
  <circle cx="64" cy="118" r="8" fill="none" stroke="#FFD700" stroke-width="2.5" opacity="1"/>
  <text x="64" y="122" text-anchor="middle" fill="#FF5722" font-family="Arial, sans-serif" font-size="9" font-weight="bold">完美</text>
</svg>
