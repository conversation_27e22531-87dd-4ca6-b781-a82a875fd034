<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 记账类史诗级渐变 -->
    <linearGradient id="accountingEpic" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#D4AF37" />
      <stop offset="25%" style="stop-color:#FFD700" />
      <stop offset="50%" style="stop-color:#B8860B" />
      <stop offset="75%" style="stop-color:#FFD700" />
      <stop offset="100%" style="stop-color:#D4AF37" />
    </linearGradient>
    
    <!-- 月亮渐变 -->
    <radialGradient id="moonGradient" cx="30%" cy="30%" r="70%">
      <stop offset="0%" style="stop-color:#FFFFFF" />
      <stop offset="30%" style="stop-color:#F0F8FF" />
      <stop offset="60%" style="stop-color:#E6E6FA" />
      <stop offset="100%" style="stop-color:#C0C0C0" />
    </radialGradient>
    
    <!-- 夜空渐变 -->
    <radialGradient id="nightSky" cx="50%" cy="50%" r="80%">
      <stop offset="0%" style="stop-color:#191970" opacity="0.8" />
      <stop offset="50%" style="stop-color:#000080" opacity="0.6" />
      <stop offset="100%" style="stop-color:#000000" opacity="0.4" />
    </radialGradient>
    
    <!-- 星光渐变 -->
    <radialGradient id="starlight" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#FFFFFF" />
      <stop offset="50%" style="stop-color:#FFFF99" />
      <stop offset="100%" style="stop-color:#FFD700" />
    </radialGradient>
    
    <!-- 猫头鹰纹样渐变 -->
    <linearGradient id="owlPattern" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8B4513" />
      <stop offset="50%" style="stop-color:#D2691E" />
      <stop offset="100%" style="stop-color:#A0522D" />
    </linearGradient>
  </defs>
  
  <!-- 发光效果 - 多层模拟 -->
  <circle cx="64" cy="64" r="52" fill="url(#accountingEpic)" opacity="0.1"/>
  <circle cx="64" cy="64" r="50" fill="url(#accountingEpic)" opacity="0.2"/>
  
  <!-- 外层动态光环 -->
  <circle cx="64" cy="64" r="62" fill="none" stroke="url(#accountingEpic)" stroke-width="1" opacity="0.3">
    <animate attributeName="r" values="60;66;60" dur="6s" repeatCount="indefinite"/>
    <animate attributeName="opacity" values="0.2;0.4;0.2" dur="6s" repeatCount="indefinite"/>
  </circle>
  
  <!-- 多层装饰边框 -->
  <circle cx="64" cy="64" r="58" fill="none" stroke="url(#accountingEpic)" stroke-width="2" opacity="0.6"/>
  <circle cx="64" cy="64" r="54" fill="none" stroke="#fff" stroke-width="1" opacity="0.4"/>
  
  <!-- 主体背景 -->
  <circle cx="64" cy="64" r="48" fill="url(#accountingEpic)"/>
  
  <!-- 夜空背景装饰 -->
  <circle cx="64" cy="64" r="42" fill="url(#nightSky)" opacity="0.6"/>
  
  <!-- 内圈装饰 -->
  <circle cx="64" cy="64" r="40" fill="none" stroke="#fff" stroke-width="1.8" opacity="0.7"/>
  <circle cx="64" cy="64" r="34" fill="none" stroke="#fff" stroke-width="1" opacity="0.5"/>
  
  <!-- 月亮图案 - 史诗级设计 -->
  <g transform="translate(64, 64)">
    <!-- 月亮发光底层 -->
    <circle cx="-4" cy="-8" r="18" fill="url(#moonGradient)" opacity="0.3">
      <animate attributeName="opacity" values="0.2;0.4;0.2" dur="4s" repeatCount="indefinite"/>
    </circle>
    <circle cx="-4" cy="-8" r="16" fill="url(#moonGradient)" opacity="0.5">
      <animate attributeName="opacity" values="0.4;0.6;0.4" dur="4s" repeatCount="indefinite"/>
    </circle>
    
    <!-- 月亮主体 -->
    <circle cx="-4" cy="-8" r="14" fill="url(#moonGradient)">
      <animate attributeName="opacity" values="0.8;1;0.8" dur="4s" repeatCount="indefinite"/>
    </circle>
    
    <!-- 月亮阴影部分 -->
    <path d="M-4,-22 A14,14 0 0,1 -4,6 A10,10 0 0,0 -4,-22 Z" fill="url(#nightSky)" opacity="0.6"/>
    
    <!-- 月亮表面纹理 -->
    <g opacity="0.4">
      <circle cx="-8" cy="-12" r="2" fill="#C0C0C0"/>
      <circle cx="0" cy="-6" r="1.5" fill="#C0C0C0"/>
      <circle cx="-6" cy="-2" r="1" fill="#C0C0C0"/>
      <ellipse cx="2" cy="-10" rx="1.5" ry="1" fill="#C0C0C0"/>
    </g>
    
    <!-- 月光光晕 - 多层效果 -->
    <circle cx="-4" cy="-8" r="22" fill="none" stroke="#FFFFFF" stroke-width="1" opacity="0.2">
      <animate attributeName="r" values="20;24;20" dur="5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.1;0.3;0.1" dur="5s" repeatCount="indefinite"/>
    </circle>
    <circle cx="-4" cy="-8" r="18" fill="none" stroke="#FFFFFF" stroke-width="1" opacity="0.3">
      <animate attributeName="r" values="16;20;16" dur="5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.2;0.5;0.2" dur="5s" repeatCount="indefinite"/>
    </circle>
    
    <!-- 星星装饰 - 增强版 -->
    <g opacity="0.9">
      <!-- 大星星 - 多层发光 -->
      <g transform="translate(15, -20)">
        <path d="M0,-5 L1.2,-1.2 L5,0 L1.2,1.2 L0,5 L-1.2,1.2 L-5,0 L-1.2,-1.2 Z" fill="url(#starlight)" opacity="0.3">
          <animate attributeName="opacity" values="0.2;0.5;0.2" dur="2s" repeatCount="indefinite"/>
        </path>
        <path d="M0,-4 L1,-1 L4,0 L1,1 L0,4 L-1,1 L-4,0 L-1,-1 Z" fill="url(#starlight)">
          <animate attributeName="opacity" values="0.6;1;0.6" dur="2s" repeatCount="indefinite"/>
        </path>
      </g>
      
      <g transform="translate(-20, -15)">
        <path d="M0,-4 L1,-1 L4,0 L1,1 L0,4 L-1,1 L-4,0 L-1,-1 Z" fill="url(#starlight)" opacity="0.3">
          <animate attributeName="opacity" values="0.2;0.4;0.2" dur="2.5s" repeatCount="indefinite"/>
        </path>
        <path d="M0,-3 L0.8,-0.8 L3,0 L0.8,0.8 L0,3 L-0.8,0.8 L-3,0 L-0.8,-0.8 Z" fill="url(#starlight)">
          <animate attributeName="opacity" values="0.5;0.9;0.5" dur="2.5s" repeatCount="indefinite"/>
        </path>
      </g>
      
      <!-- 小星星群 - 发光效果 -->
      <circle cx="20" cy="8" r="1.5" fill="#FFFFFF" opacity="0.3">
        <animate attributeName="opacity" values="0.2;0.5;0.2" dur="1.5s" repeatCount="indefinite"/>
      </circle>
      <circle cx="20" cy="8" r="1" fill="#FFFFFF" opacity="0.8">
        <animate attributeName="opacity" values="0.4;1;0.4" dur="1.5s" repeatCount="indefinite"/>
      </circle>
      
      <circle cx="-18" cy="12" r="1.3" fill="#FFFF99" opacity="0.3">
        <animate attributeName="opacity" values="0.2;0.4;0.2" dur="2.2s" repeatCount="indefinite"/>
      </circle>
      <circle cx="-18" cy="12" r="0.8" fill="#FFFF99" opacity="0.7">
        <animate attributeName="opacity" values="0.3;0.9;0.3" dur="2.2s" repeatCount="indefinite"/>
      </circle>
      
      <circle cx="12" cy="15" r="1.1" fill="#FFD700" opacity="0.3">
        <animate attributeName="opacity" values="0.1;0.3;0.1" dur="1.8s" repeatCount="indefinite"/>
      </circle>
      <circle cx="12" cy="15" r="0.6" fill="#FFD700" opacity="0.6">
        <animate attributeName="opacity" values="0.2;0.8;0.2" dur="1.8s" repeatCount="indefinite"/>
      </circle>
    </g>
    
    <!-- 猫头鹰剪影 - 增强版 -->
    <g transform="translate(8, 8)" opacity="0.7">
      <!-- 猫头鹰身体发光 -->
      <ellipse cx="0" cy="0" rx="7" ry="9" fill="url(#owlPattern)" opacity="0.3"/>
      <ellipse cx="0" cy="0" rx="6" ry="8" fill="url(#owlPattern)" opacity="0.9"/>
      
      <!-- 猫头鹰头部发光 -->
      <circle cx="0" cy="-6" r="5" fill="url(#owlPattern)" opacity="0.3"/>
      <circle cx="0" cy="-6" r="4" fill="url(#owlPattern)" opacity="0.9"/>
      
      <!-- 猫头鹰耳朵 -->
      <path d="M-3,-9 L-2,-11 L-1,-9 Z" fill="url(#owlPattern)"/>
      <path d="M1,-9 L2,-11 L3,-9 Z" fill="url(#owlPattern)"/>
      
      <!-- 猫头鹰眼睛 - 发光效果 -->
      <circle cx="-1.5" cy="-6" r="2" fill="#FFD700" opacity="0.3">
        <animate attributeName="opacity" values="0.2;0.5;0.2" dur="3s" repeatCount="indefinite"/>
      </circle>
      <circle cx="-1.5" cy="-6" r="1.2" fill="#FFD700" opacity="0.9">
        <animate attributeName="opacity" values="0.7;1;0.7" dur="3s" repeatCount="indefinite"/>
      </circle>
      
      <circle cx="1.5" cy="-6" r="2" fill="#FFD700" opacity="0.3">
        <animate attributeName="opacity" values="0.2;0.5;0.2" dur="3s" repeatCount="indefinite"/>
      </circle>
      <circle cx="1.5" cy="-6" r="1.2" fill="#FFD700" opacity="0.9">
        <animate attributeName="opacity" values="0.7;1;0.7" dur="3s" repeatCount="indefinite"/>
      </circle>
      
      <!-- 猫头鹰瞳孔 -->
      <circle cx="-1.5" cy="-6" r="0.6" fill="#000"/>
      <circle cx="1.5" cy="-6" r="0.6" fill="#000"/>
      
      <!-- 猫头鹰喙 -->
      <path d="M0,-4 L-0.5,-3 L0.5,-3 Z" fill="#FF8C00"/>
    </g>
    
    <!-- 夜猫子时间显示 -->
    <g transform="translate(0, 22)" opacity="0.9">
      <rect x="-16" y="0" width="32" height="8" rx="4" fill="url(#accountingEpic)" opacity="0.8"/>
      <text x="0" y="5" text-anchor="middle" fill="#fff" font-family="Arial, sans-serif" font-size="7" font-weight="bold">23:30 PM</text>
    </g>
    
    <!-- 夜间光粒子 - 增强版 -->
    <g opacity="0.6">
      <circle cx="-22" cy="-25" r="1.5" fill="#E6E6FA" opacity="0.3">
        <animate attributeName="opacity" values="0.1;0.5;0.1" dur="3s" repeatCount="indefinite"/>
        <animate attributeName="cy" values="-25;-23;-25" dur="4s" repeatCount="indefinite"/>
      </circle>
      <circle cx="-22" cy="-25" r="1" fill="#E6E6FA">
        <animate attributeName="opacity" values="0.2;0.8;0.2" dur="3s" repeatCount="indefinite"/>
        <animate attributeName="cy" values="-25;-23;-25" dur="4s" repeatCount="indefinite"/>
      </circle>
      
      <circle cx="25" cy="-18" r="1.3" fill="#F0F8FF" opacity="0.3">
        <animate attributeName="opacity" values="0.2;0.4;0.2" dur="2.5s" repeatCount="indefinite"/>
        <animate attributeName="cx" values="25;27;25" dur="5s" repeatCount="indefinite"/>
      </circle>
      <circle cx="25" cy="-18" r="0.8" fill="#F0F8FF">
        <animate attributeName="opacity" values="0.3;0.7;0.3" dur="2.5s" repeatCount="indefinite"/>
        <animate attributeName="cx" values="25;27;25" dur="5s" repeatCount="indefinite"/>
      </circle>
    </g>
  </g>
  
  <!-- 底部华丽标识 -->
  <circle cx="64" cy="110" r="12" fill="none" stroke="url(#accountingEpic)" stroke-width="2.5" opacity="0.7"/>
  <circle cx="64" cy="110" r="8" fill="none" stroke="#C0C0C0" stroke-width="1.5" opacity="0.9"/>
  <text x="64" y="114" text-anchor="middle" fill="#8B4513" font-family="Arial, sans-serif" font-size="8" font-weight="bold" opacity="0.9">夜猫</text>
</svg>
