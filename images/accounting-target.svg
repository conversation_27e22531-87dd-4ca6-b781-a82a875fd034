<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 记账类史诗级渐变 -->
    <linearGradient id="accountingEpic" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#D4AF37" />
      <stop offset="25%" style="stop-color:#FFD700" />
      <stop offset="50%" style="stop-color:#B8860B" />
      <stop offset="75%" style="stop-color:#FFD700" />
      <stop offset="100%" style="stop-color:#D4AF37" />
    </linearGradient>
    
    <!-- 靶心渐变 -->
    <radialGradient id="targetGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#FFFFFF" />
      <stop offset="20%" style="stop-color:#FFD700" />
      <stop offset="40%" style="stop-color:#FF6347" />
      <stop offset="60%" style="stop-color:#FFD700" />
      <stop offset="80%" style="stop-color:#FF6347" />
      <stop offset="100%" style="stop-color:#8B0000" />
    </radialGradient>
    
    <!-- 箭矢渐变 -->
    <linearGradient id="arrowGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8B4513" />
      <stop offset="50%" style="stop-color:#D2691E" />
      <stop offset="100%" style="stop-color:#A0522D" />
    </linearGradient>
    
    <!-- 金属箭头渐变 -->
    <linearGradient id="arrowheadMetal" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#C0C0C0" />
      <stop offset="50%" style="stop-color:#808080" />
      <stop offset="100%" style="stop-color:#696969" />
    </linearGradient>
    
    <!-- 龙纹渐变 -->
    <linearGradient id="dragonPattern" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF6347" />
      <stop offset="50%" style="stop-color:#FFD700" />
      <stop offset="100%" style="stop-color:#FF4500" />
    </linearGradient>
  </defs>
  
  <!-- 发光效果 - 多层模拟 -->
  <circle cx="64" cy="64" r="52" fill="url(#accountingEpic)" opacity="0.1"/>
  <circle cx="64" cy="64" r="50" fill="url(#accountingEpic)" opacity="0.2"/>
  
  <!-- 外层动态光环 -->
  <circle cx="64" cy="64" r="62" fill="none" stroke="url(#accountingEpic)" stroke-width="1" opacity="0.3">
    <animate attributeName="r" values="60;66;60" dur="4s" repeatCount="indefinite"/>
    <animate attributeName="opacity" values="0.2;0.5;0.2" dur="4s" repeatCount="indefinite"/>
  </circle>
  
  <!-- 多层装饰边框 -->
  <circle cx="64" cy="64" r="58" fill="none" stroke="url(#accountingEpic)" stroke-width="2" opacity="0.6"/>
  <circle cx="64" cy="64" r="54" fill="none" stroke="#fff" stroke-width="1" opacity="0.4"/>
  
  <!-- 主体背景 -->
  <circle cx="64" cy="64" r="48" fill="url(#accountingEpic)"/>
  
  <!-- 内圈装饰 -->
  <circle cx="64" cy="64" r="40" fill="none" stroke="#fff" stroke-width="1.8" opacity="0.7"/>
  <circle cx="64" cy="64" r="34" fill="none" stroke="#fff" stroke-width="1" opacity="0.5"/>
  
  <!-- 靶心图案 - 史诗级设计 -->
  <g transform="translate(64, 64)">
    <!-- 靶心发光底层 -->
    <circle cx="0" cy="0" r="24" fill="url(#targetGradient)" opacity="0.2">
      <animate attributeName="r" values="22;26;22" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.1;0.3;0.1" dur="3s" repeatCount="indefinite"/>
    </circle>
    <circle cx="0" cy="0" r="22" fill="url(#targetGradient)" opacity="0.4"/>
    
    <!-- 靶心主体 - 多层同心圆 -->
    <circle cx="0" cy="0" r="20" fill="url(#targetGradient)"/>
    
    <!-- 靶心环 - 增强发光 -->
    <circle cx="0" cy="0" r="19" fill="none" stroke="#8B0000" stroke-width="3" opacity="0.3">
      <animate attributeName="opacity" values="0.2;0.5;0.2" dur="2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="0" cy="0" r="18" fill="none" stroke="#8B0000" stroke-width="2" opacity="0.8"/>
    
    <circle cx="0" cy="0" r="15" fill="none" stroke="#FF6347" stroke-width="2.5" opacity="0.3">
      <animate attributeName="opacity" values="0.2;0.4;0.2" dur="2.2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="0" cy="0" r="14" fill="none" stroke="#FF6347" stroke-width="1.5" opacity="0.7"/>
    
    <circle cx="0" cy="0" r="11" fill="none" stroke="#FFD700" stroke-width="2" opacity="0.3">
      <animate attributeName="opacity" values="0.2;0.4;0.2" dur="2.4s" repeatCount="indefinite"/>
    </circle>
    <circle cx="0" cy="0" r="10" fill="none" stroke="#FFD700" stroke-width="1.2" opacity="0.6"/>
    
    <circle cx="0" cy="0" r="7" fill="none" stroke="#FFFFFF" stroke-width="1.5" opacity="0.3">
      <animate attributeName="opacity" values="0.2;0.4;0.2" dur="2.6s" repeatCount="indefinite"/>
    </circle>
    <circle cx="0" cy="0" r="6" fill="none" stroke="#FFFFFF" stroke-width="1" opacity="0.5"/>
    
    <!-- 靶心中心点 -->
    <circle cx="0" cy="0" r="4" fill="#8B0000" opacity="0.3">
      <animate attributeName="r" values="3;5;3" dur="2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.2;0.5;0.2" dur="2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="0" cy="0" r="3" fill="#8B0000" opacity="0.9">
      <animate attributeName="r" values="2.5;3.5;2.5" dur="2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.7;1;0.7" dur="2s" repeatCount="indefinite"/>
    </circle>
    
    <!-- 瞄准十字线 -->
    <g stroke="#8B0000" stroke-width="1" opacity="0.8">
      <line x1="-25" y1="0" x2="-22" y2="0"/>
      <line x1="22" y1="0" x2="25" y2="0"/>
      <line x1="0" y1="-25" x2="0" y2="-22"/>
      <line x1="0" y1="22" x2="0" y2="25"/>
    </g>
    
    <!-- 精密刻度 -->
    <g stroke="#FFD700" stroke-width="0.8" opacity="0.7">
      <!-- 主刻度 -->
      <line x1="20" y1="0" x2="22" y2="0"/>
      <line x1="-20" y1="0" x2="-22" y2="0"/>
      <line x1="0" y1="20" x2="0" y2="22"/>
      <line x1="0" y1="-20" x2="0" y2="-22"/>
      
      <!-- 副刻度 -->
      <line x1="14" y1="14" x2="15.5" y2="15.5"/>
      <line x1="-14" y1="14" x2="-15.5" y2="15.5"/>
      <line x1="14" y1="-14" x2="15.5" y2="-15.5"/>
      <line x1="-14" y1="-14" x2="-15.5" y2="-15.5"/>
    </g>
    
    <!-- 箭矢 - 精准命中 -->
    <g transform="rotate(-30)" opacity="0.9">
      <!-- 箭杆 -->
      <rect x="-2" y="-25" width="4" height="20" rx="2" fill="url(#arrowGradient)"/>
      
      <!-- 箭头 -->
      <path d="M0,-25 L-3,-20 L3,-20 Z" fill="url(#arrowheadMetal)"/>
      
      <!-- 箭羽 -->
      <g fill="url(#dragonPattern)" opacity="0.8">
        <path d="M-2,-8 L-4,-12 L-2,-10 Z"/>
        <path d="M2,-8 L4,-12 L2,-10 Z"/>
        <path d="M-1.5,-6 L-3.5,-10 L-1.5,-8 Z"/>
        <path d="M1.5,-6 L3.5,-10 L1.5,-8 Z"/>
      </g>
      
      <!-- 箭杆装饰纹理 -->
      <g stroke="#A0522D" stroke-width="0.3" opacity="0.6">
        <line x1="-1.5" y1="-20" x2="-1.5" y2="-8"/>
        <line x1="0" y1="-20" x2="0" y2="-8"/>
        <line x1="1.5" y1="-20" x2="1.5" y2="-8"/>
      </g>
    </g>
    
    <!-- 命中效果 - 动态光环 -->
    <circle cx="0" cy="0" r="28" fill="none" stroke="#FFD700" stroke-width="3" opacity="0.3">
      <animate attributeName="r" values="25;31;25" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.2;0.5;0.2" dur="3s" repeatCount="indefinite"/>
    </circle>
    <circle cx="0" cy="0" r="25" fill="none" stroke="#FFD700" stroke-width="2" opacity="0.6">
      <animate attributeName="r" values="22;28;22" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.4;0.8;0.4" dur="3s" repeatCount="indefinite"/>
    </circle>
    
    <!-- 精准度指示器 -->
    <g transform="translate(0, 28)" opacity="0.9">
      <rect x="-18" y="0" width="36" height="6" rx="3" fill="url(#accountingEpic)" opacity="0.8"/>
      <text x="0" y="4" text-anchor="middle" fill="#fff" font-family="Arial, sans-serif" font-size="6" font-weight="bold">BULLSEYE!</text>
    </g>
    
    <!-- 分数显示 -->
    <g transform="translate(22, -22)" opacity="0.8">
      <circle cx="0" cy="0" r="9" fill="url(#accountingEpic)" opacity="0.3"/>
      <circle cx="0" cy="0" r="8" fill="url(#accountingEpic)" opacity="0.9"/>
      <text x="0" y="2" text-anchor="middle" fill="#fff" font-family="Arial, sans-serif" font-size="8" font-weight="bold">100</text>
    </g>
    
    <!-- 装饰性光芒 -->
    <g opacity="0.7">
      <line x1="0" y1="-30" x2="0" y2="-35" stroke="#FFD700" stroke-width="2">
        <animate attributeName="opacity" values="0.5;1;0.5" dur="1.5s" repeatCount="indefinite"/>
      </line>
      <line x1="-3" y1="-28" x2="-6" y2="-32" stroke="#FF6347" stroke-width="1.5">
        <animate attributeName="opacity" values="0.4;0.9;0.4" dur="1.8s" repeatCount="indefinite"/>
      </line>
      <line x1="3" y1="-28" x2="6" y2="-32" stroke="#FF6347" stroke-width="1.5">
        <animate attributeName="opacity" values="0.4;0.9;0.4" dur="2.2s" repeatCount="indefinite"/>
      </line>
    </g>
    
    <!-- 成就火花 -->
    <g opacity="0.8">
      <circle cx="-28" cy="-15" r="2" fill="#FFD700" opacity="0.3">
        <animate attributeName="opacity" values="0.2;0.8;0.2" dur="1s" repeatCount="indefinite"/>
        <animate attributeName="r" values="1.5;2.5;1.5" dur="1s" repeatCount="indefinite"/>
      </circle>
      <circle cx="-28" cy="-15" r="1.5" fill="#FFD700">
        <animate attributeName="opacity" values="0.3;1;0.3" dur="1s" repeatCount="indefinite"/>
        <animate attributeName="r" values="1;2;1" dur="1s" repeatCount="indefinite"/>
      </circle>
      
      <circle cx="25" cy="-18" r="1.8" fill="#FF6347" opacity="0.3">
        <animate attributeName="opacity" values="0.1;0.7;0.1" dur="1.3s" repeatCount="indefinite"/>
        <animate attributeName="r" values="1.2;2.2;1.2" dur="1.3s" repeatCount="indefinite"/>
      </circle>
      <circle cx="25" cy="-18" r="1.2" fill="#FF6347">
        <animate attributeName="opacity" values="0.2;0.9;0.2" dur="1.3s" repeatCount="indefinite"/>
        <animate attributeName="r" values="0.8;1.8;0.8" dur="1.3s" repeatCount="indefinite"/>
      </circle>
      
      <circle cx="-22" cy="20" r="1.5" fill="#FFFFFF" opacity="0.3">
        <animate attributeName="opacity" values="0.2;0.6;0.2" dur="1.6s" repeatCount="indefinite"/>
        <animate attributeName="r" values="1;2;1" dur="1.6s" repeatCount="indefinite"/>
      </circle>
      <circle cx="-22" cy="20" r="1" fill="#FFFFFF">
        <animate attributeName="opacity" values="0.4;0.8;0.4" dur="1.6s" repeatCount="indefinite"/>
        <animate attributeName="r" values="0.5;1.5;0.5" dur="1.6s" repeatCount="indefinite"/>
      </circle>
    </g>
  </g>
  
  <!-- 底部华丽标识 -->
  <circle cx="64" cy="110" r="12" fill="none" stroke="url(#accountingEpic)" stroke-width="2.5" opacity="0.7"/>
  <circle cx="64" cy="110" r="8" fill="none" stroke="#FF6347" stroke-width="1.5" opacity="0.9"/>
  <text x="64" y="114" text-anchor="middle" fill="#8B4513" font-family="Arial, sans-serif" font-size="8" font-weight="bold" opacity="0.9">目标</text>
</svg>
