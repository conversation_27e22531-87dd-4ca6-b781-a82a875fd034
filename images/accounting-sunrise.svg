<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 记账类史诗级渐变 -->
    <linearGradient id="accountingEpic" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#D4AF37;stop-opacity:1" />
      <stop offset="25%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#B8860B;stop-opacity:1" />
      <stop offset="75%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#D4AF37;stop-opacity:1" />
    </linearGradient>
    
    <!-- 日出渐变 -->
    <radialGradient id="sunriseGradient" cx="50%" cy="70%" r="60%">
      <stop offset="0%" style="stop-color:#FFFFFF;stop-opacity:1" />
      <stop offset="20%" style="stop-color:#FFFF99;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="80%" style="stop-color:#FF8C00;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FF6347;stop-opacity:1" />
    </radialGradient>
    
    <!-- 晨云渐变 -->
    <linearGradient id="morningClouds" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFE4E1;stop-opacity:0.8" />
      <stop offset="50%" style="stop-color:#FFF8DC;stop-opacity:0.6" />
      <stop offset="100%" style="stop-color:#F0E68C;stop-opacity:0.4" />
    </linearGradient>
    
    <!-- 凤凰纹样渐变 -->
    <linearGradient id="phoenixPattern" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF6347;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FF4500;stop-opacity:1" />
    </linearGradient>
    
    <!-- 多重光晕效果 -->
    <filter id="multiGlow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feGaussianBlur stdDeviation="6" result="coloredBlur2"/>
      <feGaussianBlur stdDeviation="9" result="coloredBlur3"/>
      <feMerge> 
        <feMergeNode in="coloredBlur3"/>
        <feMergeNode in="coloredBlur2"/>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    
    <!-- 日出光芒效果 -->
    <filter id="sunriseRays">
      <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
      <feColorMatrix type="matrix" values="1.8 1.8 1.2 0 0  1.8 1.8 1.2 0 0  1.2 1.2 1.8 0 0  0 0 0 1 0"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- 外层动态光环 -->
  <circle cx="64" cy="64" r="62" 
          fill="none" 
          stroke="url(#accountingEpic)" 
          stroke-width="1" 
          opacity="0.3">
    <animate attributeName="r" values="60;66;60" dur="5s" repeatCount="indefinite"/>
    <animate attributeName="opacity" values="0.2;0.5;0.2" dur="5s" repeatCount="indefinite"/>
  </circle>
  
  <!-- 多层装饰边框 -->
  <circle cx="64" cy="64" r="58" fill="none" stroke="url(#accountingEpic)" stroke-width="2" opacity="0.6"/>
  <circle cx="64" cy="64" r="54" fill="none" stroke="#fff" stroke-width="1" opacity="0.4"/>
  
  <!-- 主体背景 -->
  <circle cx="64" cy="64" r="48" fill="url(#accountingEpic)" filter="url(#multiGlow)"/>
  
  <!-- 凤凰纹样装饰 -->
  <g transform="translate(64, 64)" opacity="0.3">
    <!-- 凤凰翅膀纹样 -->
    <g stroke="#fff" stroke-width="1.5" fill="none">
      <path d="M-35,-15 Q-25,-25 -15,-15 Q-5,-25 5,-15 Q15,-25 25,-15 Q35,-25 35,-15"/>
      <path d="M-35,15 Q-25,5 -15,15 Q-5,5 5,15 Q15,5 25,15 Q35,5 35,15"/>
    </g>
    
    <!-- 凤凰羽毛纹样 -->
    <g fill="#fff" opacity="0.4">
      <ellipse cx="-28" cy="-20" rx="2" ry="6" transform="rotate(-30 -28 -20)"/>
      <ellipse cx="28" cy="-20" rx="2" ry="6" transform="rotate(30 28 -20)"/>
      <ellipse cx="-28" cy="20" rx="2" ry="6" transform="rotate(30 -28 20)"/>
      <ellipse cx="28" cy="20" rx="2" ry="6" transform="rotate(-30 28 20)"/>
    </g>
  </g>
  
  <!-- 日出图案 - 史诗级设计 -->
  <g transform="translate(64, 64)">
    <!-- 太阳发光底层 -->
    <circle cx="0" cy="-8" r="20" fill="url(#sunriseGradient)" opacity="0.2">
      <animate attributeName="r" values="18;22;18" dur="4s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.1;0.3;0.1" dur="4s" repeatCount="indefinite"/>
    </circle>
    <circle cx="0" cy="-8" r="18" fill="url(#sunriseGradient)" opacity="0.4">
      <animate attributeName="r" values="16;20;16" dur="4s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.3;0.5;0.3" dur="4s" repeatCount="indefinite"/>
    </circle>

    <!-- 太阳主体 -->
    <circle cx="0" cy="-8" r="16" fill="url(#sunriseGradient)">
      <animate attributeName="r" values="15;17;15" dur="4s" repeatCount="indefinite"/>
    </circle>

    <!-- 太阳核心发光 -->
    <circle cx="0" cy="-8" r="12" fill="#FFFFFF" opacity="0.3">
      <animate attributeName="opacity" values="0.2;0.4;0.2" dur="3s" repeatCount="indefinite"/>
    </circle>
    <circle cx="0" cy="-8" r="10" fill="#FFFFFF" opacity="0.8">
      <animate attributeName="opacity" values="0.6;1;0.6" dur="3s" repeatCount="indefinite"/>
    </circle>
    
    <!-- 太阳光芒 - 动态 -->
    <g opacity="0.9">
      <!-- 主要光芒 -->
      <line x1="0" y1="-28" x2="0" y2="-35" 
            stroke="url(#phoenixPattern)" 
            stroke-width="3" 
            filter="url(#sunriseRays)">
        <animate attributeName="stroke-width" values="2;4;2" dur="2s" repeatCount="indefinite"/>
      </line>
      
      <line x1="11" y1="-19" x2="16" y2="-27" 
            stroke="url(#phoenixPattern)" 
            stroke-width="2.5" 
            filter="url(#sunriseRays)">
        <animate attributeName="stroke-width" values="1.5;3.5;1.5" dur="2.5s" repeatCount="indefinite"/>
      </line>
      
      <line x1="-11" y1="-19" x2="-16" y2="-27" 
            stroke="url(#phoenixPattern)" 
            stroke-width="2.5" 
            filter="url(#sunriseRays)">
        <animate attributeName="stroke-width" values="1.5;3.5;1.5" dur="2.2s" repeatCount="indefinite"/>
      </line>
      
      <line x1="16" y1="-8" x2="24" y2="-8" 
            stroke="url(#phoenixPattern)" 
            stroke-width="2" 
            filter="url(#sunriseRays)">
        <animate attributeName="stroke-width" values="1;3;1" dur="2.8s" repeatCount="indefinite"/>
      </line>
      
      <line x1="-16" y1="-8" x2="-24" y2="-8" 
            stroke="url(#phoenixPattern)" 
            stroke-width="2" 
            filter="url(#sunriseRays)">
        <animate attributeName="stroke-width" values="1;3;1" dur="3.2s" repeatCount="indefinite"/>
      </line>
      
      <!-- 次要光芒 -->
      <line x1="8" y1="-22" x2="12" y2="-30" 
            stroke="#FFD700" 
            stroke-width="1.5" 
            opacity="0.8">
        <animate attributeName="opacity" values="0.5;1;0.5" dur="1.8s" repeatCount="indefinite"/>
      </line>
      
      <line x1="-8" y1="-22" x2="-12" y2="-30" 
            stroke="#FFD700" 
            stroke-width="1.5" 
            opacity="0.8">
        <animate attributeName="opacity" values="0.5;1;0.5" dur="2.3s" repeatCount="indefinite"/>
      </line>
    </g>
    
    <!-- 晨云装饰 -->
    <g opacity="0.7">
      <ellipse cx="-20" cy="8" rx="12" ry="4" 
               fill="url(#morningClouds)" 
               transform="rotate(-10 -20 8)">
        <animate attributeName="rx" values="10;14;10" dur="6s" repeatCount="indefinite"/>
      </ellipse>
      
      <ellipse cx="18" cy="12" rx="10" ry="3" 
               fill="url(#morningClouds)" 
               transform="rotate(15 18 12)">
        <animate attributeName="rx" values="8;12;8" dur="5s" repeatCount="indefinite"/>
      </ellipse>
      
      <ellipse cx="-8" cy="18" rx="8" ry="2.5" 
               fill="url(#morningClouds)" 
               transform="rotate(-5 -8 18)">
        <animate attributeName="rx" values="6;10;6" dur="4s" repeatCount="indefinite"/>
      </ellipse>
    </g>
    
    <!-- 凤凰剪影 -->
    <g transform="translate(0, -8)" opacity="0.6">
      <path d="M-8,-6 Q-4,-10 0,-6 Q4,-10 8,-6 Q6,-4 4,-2 Q2,0 0,2 Q-2,0 -4,-2 Q-6,-4 -8,-6 Z" 
            fill="url(#phoenixPattern)" 
            opacity="0.8">
        <animateTransform attributeName="transform" 
                          type="rotate" 
                          values="0 0 0;5 0 0;0 0 0;-5 0 0;0 0 0" 
                          dur="8s" 
                          repeatCount="indefinite"/>
      </path>
      
      <!-- 凤凰尾羽 -->
      <path d="M0,2 Q-2,6 -4,10 Q-2,8 0,6 Q2,8 4,10 Q2,6 0,2" 
            fill="url(#phoenixPattern)" 
            opacity="0.6">
        <animate attributeName="opacity" values="0.4;0.8;0.4" dur="3s" repeatCount="indefinite"/>
      </path>
    </g>
    
    <!-- 早起时间显示 -->
    <g transform="translate(0, 22)" opacity="0.9">
      <rect x="-16" y="0" width="32" height="8" rx="4" 
            fill="url(#accountingEpic)" 
            opacity="0.8"/>
      <text x="0" y="5" 
            text-anchor="middle" 
            fill="#fff" 
            font-family="Arial, sans-serif" 
            font-size="7" 
            font-weight="bold">08:00 AM</text>
    </g>
    
    <!-- 装饰性光粒子 -->
    <g opacity="0.8">
      <circle cx="-25" cy="-20" r="1.5" fill="#FFD700">
        <animate attributeName="opacity" values="0.3;1;0.3" dur="2s" repeatCount="indefinite"/>
        <animate attributeName="r" values="1;2;1" dur="2s" repeatCount="indefinite"/>
      </circle>
      
      <circle cx="22" cy="-15" r="1.2" fill="#FF8C00">
        <animate attributeName="opacity" values="0.2;0.9;0.2" dur="2.5s" repeatCount="indefinite"/>
        <animate attributeName="r" values="0.8;1.8;0.8" dur="2.5s" repeatCount="indefinite"/>
      </circle>
      
      <circle cx="-18" cy="-25" r="1" fill="#FF6347">
        <animate attributeName="opacity" values="0.4;0.8;0.4" dur="1.8s" repeatCount="indefinite"/>
        <animate attributeName="r" values="0.5;1.5;0.5" dur="1.8s" repeatCount="indefinite"/>
      </circle>
      
      <circle cx="26" cy="-22" r="1.3" fill="#FFFF99">
        <animate attributeName="opacity" values="0.5;1;0.5" dur="2.2s" repeatCount="indefinite"/>
        <animate attributeName="r" values="1;2.2;1" dur="2.2s" repeatCount="indefinite"/>
      </circle>
    </g>
  </g>
  
  <!-- 底部华丽标识 -->
  <circle cx="64" cy="110" r="12" fill="none" stroke="url(#accountingEpic)" stroke-width="2.5" opacity="0.7"/>
  <circle cx="64" cy="110" r="8" fill="none" stroke="#FFD700" stroke-width="1.5" opacity="0.9"/>
  <text x="64" y="114" 
        text-anchor="middle" 
        fill="#8B4513" 
        font-family="Arial, sans-serif" 
        font-size="8" 
        font-weight="bold"
        opacity="0.9">早起</text>
</svg>
