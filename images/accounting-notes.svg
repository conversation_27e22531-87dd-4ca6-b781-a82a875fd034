<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 记账类普通级渐变 -->
    <linearGradient id="accountingCommon" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="30%" style="stop-color:#FFA500;stop-opacity:1" />
      <stop offset="70%" style="stop-color:#FF8C00;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FF6347;stop-opacity:1" />
    </linearGradient>

    <!-- 笔记本纸张渐变 -->
    <linearGradient id="paperGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFFEF7;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#FFF8DC;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#F5F5DC;stop-opacity:1" />
    </linearGradient>

    <!-- 精致发光 -->
    <filter id="refinedGlow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feOffset dx="0" dy="1" result="offsetBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="offsetBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- 纸张立体效果 -->
    <filter id="paperDepth">
      <feGaussianBlur stdDeviation="0.5" result="blur"/>
      <feOffset dx="1" dy="1" result="offset"/>
      <feFlood flood-color="#DDD" flood-opacity="0.4"/>
      <feComposite in="SourceGraphic" in2="offset" operator="over"/>
    </filter>
  </defs>

  <!-- 外层精致边框 -->
  <circle cx="64" cy="64" r="60"
          fill="none"
          stroke="url(#accountingCommon)"
          stroke-width="1.5"
          opacity="0.4"/>
  <circle cx="64" cy="64" r="56"
          fill="none"
          stroke="url(#accountingCommon)"
          stroke-width="2.5"
          opacity="0.6"/>

  <!-- 主体背景 -->
  <circle cx="64" cy="64" r="48"
          fill="url(#accountingCommon)"
          filter="url(#refinedGlow)"/>

  <!-- 内圈装饰 -->
  <circle cx="64" cy="64" r="38"
          fill="none"
          stroke="#fff"
          stroke-width="1.5"
          opacity="0.6"/>
  <circle cx="64" cy="64" r="32"
          fill="none"
          stroke="#fff"
          stroke-width="0.8"
          opacity="0.4"/>

  <!-- 笔记本图案 - 精致设计 -->
  <g transform="translate(64, 64)">
    <!-- 笔记本主体 - 立体感 -->
    <rect x="-16" y="-18" width="32" height="28" rx="4"
          fill="url(#paperGradient)"
          filter="url(#paperDepth)"/>

    <!-- 笔记本边框 -->
    <rect x="-16" y="-18" width="32" height="28" rx="4"
          fill="none"
          stroke="#DDD"
          stroke-width="1"
          opacity="0.6"/>

    <!-- 装订线 - 精致设计 -->
    <line x1="-10" y1="-18" x2="-10" y2="10"
          stroke="#FFD700"
          stroke-width="2.5"
          opacity="0.8"/>
    <line x1="-9" y1="-18" x2="-9" y2="10"
          stroke="#FFA500"
          stroke-width="1"
          opacity="0.6"/>

    <!-- 装订孔 - 立体效果 -->
    <circle cx="-10" cy="-12" r="1.5" fill="#B8860B" opacity="0.8"/>
    <circle cx="-10" cy="-12" r="1" fill="#FFD700" opacity="0.9"/>

    <circle cx="-10" cy="-4" r="1.5" fill="#B8860B" opacity="0.8"/>
    <circle cx="-10" cy="-4" r="1" fill="#FFD700" opacity="0.9"/>

    <circle cx="-10" cy="4" r="1.5" fill="#B8860B" opacity="0.8"/>
    <circle cx="-10" cy="4" r="1" fill="#FFD700" opacity="0.9"/>

    <!-- 文字线条 - 精致排版 -->
    <line x1="-6" y1="-12" x2="12" y2="-12"
          stroke="#8B4513"
          stroke-width="1.2"
          opacity="0.8"/>
    <line x1="-6" y1="-8" x2="10" y2="-8"
          stroke="#8B4513"
          stroke-width="1.2"
          opacity="0.8"/>
    <line x1="-6" y1="-4" x2="14" y2="-4"
          stroke="#8B4513"
          stroke-width="1.2"
          opacity="0.8"/>
    <line x1="-6" y1="0" x2="8" y2="0"
          stroke="#8B4513"
          stroke-width="1.2"
          opacity="0.8"/>
    <line x1="-6" y1="4" x2="6" y2="4"
          stroke="#8B4513"
          stroke-width="1.2"
          opacity="0.8"/>

    <!-- 精致笔图标 -->
    <g transform="translate(10, 12)">
      <!-- 笔身 -->
      <line x1="0" y1="0" x2="8" y2="-8"
            stroke="#8B4513"
            stroke-width="2.5"
            stroke-linecap="round"/>
      <line x1="0" y1="0" x2="8" y2="-8"
            stroke="#D2691E"
            stroke-width="1.5"
            stroke-linecap="round"/>

      <!-- 笔尖 -->
      <circle cx="8" cy="-8" r="2"
              fill="#8B4513"/>
      <circle cx="8" cy="-8" r="1.2"
              fill="#D2691E"/>

      <!-- 笔帽 -->
      <circle cx="2" cy="-2" r="1.5"
              fill="#FFD700"
              opacity="0.9"/>
    </g>
  </g>

  <!-- 底部精致标识 -->
  <circle cx="64" cy="104" r="8"
          fill="none"
          stroke="#FFD700"
          stroke-width="1.5"
          opacity="0.5"/>
  <text x="64" y="108"
        text-anchor="middle"
        fill="#FF6347"
        font-family="Arial, sans-serif"
        font-size="7"
        font-weight="600"
        opacity="0.8">详细</text>
</svg>
