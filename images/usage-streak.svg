<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 使用类稀有级渐变 -->
    <linearGradient id="usageRare" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8E24AA" />
      <stop offset="25%" style="stop-color:#7B1FA2" />
      <stop offset="50%" style="stop-color:#6A1B9A" />
      <stop offset="75%" style="stop-color:#4A148C" />
      <stop offset="100%" style="stop-color:#38006B" />
    </linearGradient>
    
    <!-- 火焰渐变 -->
    <radialGradient id="flameGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#FFEB3B" />
      <stop offset="50%" style="stop-color:#FF9800" />
      <stop offset="100%" style="stop-color:#E65100" />
    </radialGradient>
    
    <!-- 连击渐变 -->
    <linearGradient id="streakGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF5722" />
      <stop offset="100%" style="stop-color:#D84315" />
    </linearGradient>
    
    <!-- 数字渐变 -->
    <linearGradient id="numberGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFD700" />
      <stop offset="100%" style="stop-color:#FF9800" />
    </linearGradient>
  </defs>
  
  <!-- 发光效果 - 多层模拟 -->
  <circle cx="64" cy="64" r="52" fill="url(#usageRare)" opacity="0.1"/>
  <circle cx="64" cy="64" r="50" fill="url(#usageRare)" opacity="0.2"/>
  
  <!-- 外圈边框 -->
  <circle cx="64" cy="64" r="58" fill="none" stroke="url(#usageRare)" stroke-width="2" opacity="0.6"/>
  <circle cx="64" cy="64" r="54" fill="none" stroke="#ffffff" stroke-width="1" opacity="0.4"/>
  
  <!-- 主体背景 -->
  <circle cx="64" cy="64" r="48" fill="url(#usageRare)"/>
  
  <!-- 内圈装饰 -->
  <circle cx="64" cy="64" r="40" fill="none" stroke="#ffffff" stroke-width="1.5" opacity="0.6"/>
  <circle cx="64" cy="64" r="34" fill="none" stroke="#ffffff" stroke-width="1" opacity="0.4"/>
  
  <!-- 连击图案 -->
  <g transform="translate(64, 64)">
    <!-- 中央火焰 - 多层发光 -->
    <g transform="translate(0, -4)">
      <!-- 火焰发光底层 -->
      <ellipse cx="0" cy="0" rx="8" ry="12" fill="url(#flameGradient)" opacity="0.3">
        <animate attributeName="ry" values="12;15;12" dur="1.5s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.2;0.4;0.2" dur="1.5s" repeatCount="indefinite"/>
      </ellipse>
      
      <!-- 主火焰 -->
      <ellipse cx="0" cy="0" rx="6" ry="10" fill="url(#flameGradient)">
        <animate attributeName="ry" values="10;12;10" dur="1.2s" repeatCount="indefinite"/>
        <animate attributeName="rx" values="6;7;6" dur="1.8s" repeatCount="indefinite"/>
      </ellipse>
      
      <!-- 火焰核心 -->
      <ellipse cx="0" cy="2" rx="3" ry="6" fill="#FFEB3B">
        <animate attributeName="opacity" values="0.8;1;0.8" dur="1s" repeatCount="indefinite"/>
      </ellipse>
      
      <!-- 火花 -->
      <g opacity="0.8">
        <circle cx="-4" cy="-8" r="0.8" fill="#FFD54F">
          <animate attributeName="cy" values="-8;-12;-8" dur="2s" repeatCount="indefinite"/>
          <animate attributeName="opacity" values="0.8;0.2;0.8" dur="2s" repeatCount="indefinite"/>
        </circle>
        <circle cx="3" cy="-9" r="0.6" fill="#FF9800">
          <animate attributeName="cy" values="-9;-13;-9" dur="2.5s" repeatCount="indefinite"/>
          <animate attributeName="opacity" values="0.7;0.1;0.7" dur="2.5s" repeatCount="indefinite"/>
        </circle>
        <circle cx="-2" cy="-10" r="0.4" fill="#E65100">
          <animate attributeName="cy" values="-10;-14;-10" dur="1.8s" repeatCount="indefinite"/>
          <animate attributeName="opacity" values="0.6;0.1;0.6" dur="1.8s" repeatCount="indefinite"/>
        </circle>
      </g>
    </g>
    
    <!-- 连击数字 -->
    <g transform="translate(0, 8)">
      <rect x="-8" y="0" width="16" height="8" rx="4" fill="url(#numberGradient)" opacity="0.3"/>
      <rect x="-7" y="1" width="14" height="6" rx="3" fill="url(#numberGradient)"/>
      <text x="0" y="5" text-anchor="middle" fill="#FFFFFF" font-family="Arial, sans-serif" font-size="6" font-weight="bold">100</text>
    </g>
    
    <!-- 连击标识 -->
    <g transform="translate(0, 18)">
      <rect x="-12" y="0" width="24" height="5" rx="2.5" fill="url(#streakGradient)" opacity="0.8"/>
      <text x="0" y="3" text-anchor="middle" fill="#FFFFFF" font-family="Arial, sans-serif" font-size="3" font-weight="bold">STREAK</text>
    </g>
    
    <!-- 连击链条 -->
    <g opacity="0.7">
      <!-- 左侧链条 -->
      <g transform="translate(-18, 0)">
        <ellipse cx="0" cy="0" rx="3" ry="1.5" fill="none" stroke="url(#numberGradient)" stroke-width="1.5"/>
        <ellipse cx="0" cy="4" rx="3" ry="1.5" fill="none" stroke="url(#numberGradient)" stroke-width="1.5"/>
        <ellipse cx="0" cy="8" rx="3" ry="1.5" fill="none" stroke="url(#numberGradient)" stroke-width="1.5"/>
        
        <!-- 连接环 -->
        <ellipse cx="0" cy="2" rx="1.5" ry="3" fill="none" stroke="url(#numberGradient)" stroke-width="1.5"/>
        <ellipse cx="0" cy="6" rx="1.5" ry="3" fill="none" stroke="url(#numberGradient)" stroke-width="1.5"/>
      </g>
      
      <!-- 右侧链条 -->
      <g transform="translate(18, 0)">
        <ellipse cx="0" cy="0" rx="3" ry="1.5" fill="none" stroke="url(#numberGradient)" stroke-width="1.5"/>
        <ellipse cx="0" cy="4" rx="3" ry="1.5" fill="none" stroke="url(#numberGradient)" stroke-width="1.5"/>
        <ellipse cx="0" cy="8" rx="3" ry="1.5" fill="none" stroke="url(#numberGradient)" stroke-width="1.5"/>
        
        <!-- 连接环 -->
        <ellipse cx="0" cy="2" rx="1.5" ry="3" fill="none" stroke="url(#numberGradient)" stroke-width="1.5"/>
        <ellipse cx="0" cy="6" rx="1.5" ry="3" fill="none" stroke="url(#numberGradient)" stroke-width="1.5"/>
      </g>
    </g>
    
    <!-- 成就星星 -->
    <g opacity="0.8">
      <g transform="translate(-20, -15)">
        <path d="M0,-3 L1,-1 L3,0 L1,1 L0,3 L-1,1 L-3,0 L-1,-1 Z" fill="url(#numberGradient)">
          <animate attributeName="opacity" values="0.6;1;0.6" dur="2s" repeatCount="indefinite"/>
        </path>
      </g>
      
      <g transform="translate(20, -12)">
        <path d="M0,-2.5 L0.8,-0.8 L2.5,0 L0.8,0.8 L0,2.5 L-0.8,0.8 L-2.5,0 L-0.8,-0.8 Z" fill="url(#numberGradient)">
          <animate attributeName="opacity" values="0.5;0.9;0.5" dur="2.5s" repeatCount="indefinite"/>
        </path>
      </g>
      
      <g transform="translate(-15, 20)">
        <path d="M0,-2 L0.6,-0.6 L2,0 L0.6,0.6 L0,2 L-0.6,0.6 L-2,0 L-0.6,-0.6 Z" fill="url(#numberGradient)">
          <animate attributeName="opacity" values="0.7;1;0.7" dur="1.8s" repeatCount="indefinite"/>
        </path>
      </g>
      
      <g transform="translate(15, 18)">
        <path d="M0,-2.2 L0.7,-0.7 L2.2,0 L0.7,0.7 L0,2.2 L-0.7,0.7 L-2.2,0 L-0.7,-0.7 Z" fill="url(#numberGradient)">
          <animate attributeName="opacity" values="0.4;0.8;0.4" dur="2.2s" repeatCount="indefinite"/>
        </path>
      </g>
    </g>
    
    <!-- 进度条 -->
    <g transform="translate(0, -20)">
      <rect x="-15" y="0" width="30" height="3" rx="1.5" fill="#FFFFFF" opacity="0.3"/>
      <rect x="-15" y="0" width="24" height="3" rx="1.5" fill="url(#numberGradient)">
        <animate attributeName="width" values="20;28;20" dur="3s" repeatCount="indefinite"/>
      </rect>
      <text x="0" y="-2" text-anchor="middle" fill="#FFFFFF" font-family="Arial, sans-serif" font-size="2" font-weight="bold" opacity="0.8">80%</text>
    </g>
    
    <!-- 热力波纹 -->
    <g opacity="0.4">
      <circle cx="0" cy="0" r="25" fill="none" stroke="url(#flameGradient)" stroke-width="1">
        <animate attributeName="r" values="20;30;20" dur="3s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.6;0.1;0.6" dur="3s" repeatCount="indefinite"/>
      </circle>
      <circle cx="0" cy="0" r="20" fill="none" stroke="url(#flameGradient)" stroke-width="0.8">
        <animate attributeName="r" values="15;25;15" dur="2.5s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.5;0.1;0.5" dur="2.5s" repeatCount="indefinite"/>
      </circle>
    </g>
  </g>
  
  <!-- 底部华丽标识 -->
  <circle cx="64" cy="110" r="12" fill="none" stroke="url(#usageRare)" stroke-width="2.5" opacity="0.7"/>
  <circle cx="64" cy="110" r="8" fill="none" stroke="#8E24AA" stroke-width="1.5" opacity="0.9"/>
  <text x="64" y="114" text-anchor="middle" fill="#38006B" font-family="Arial, sans-serif" font-size="8" font-weight="bold" opacity="0.9">连击</text>
</svg>
