<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 旅行类史诗级渐变 -->
    <linearGradient id="travelEpic" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1E88E5" />
      <stop offset="20%" style="stop-color:#1976D2" />
      <stop offset="40%" style="stop-color:#1565C0" />
      <stop offset="60%" style="stop-color:#0D47A1" />
      <stop offset="80%" style="stop-color:#0A3D91" />
      <stop offset="100%" style="stop-color:#083481" />
    </linearGradient>
    
    <!-- 山峰渐变 -->
    <linearGradient id="mountainGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#90A4AE" />
      <stop offset="50%" style="stop-color:#607D8B" />
      <stop offset="100%" style="stop-color:#37474F" />
    </linearGradient>
    
    <!-- 雪峰渐变 -->
    <linearGradient id="snowGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFFFFF" />
      <stop offset="100%" style="stop-color:#ECEFF1" />
    </linearGradient>
    
    <!-- 天空渐变 -->
    <linearGradient id="skyGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#E3F2FD" />
      <stop offset="100%" style="stop-color:#BBDEFB" />
    </linearGradient>
    
    <!-- 登山者渐变 -->
    <linearGradient id="climberGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF5722" />
      <stop offset="100%" style="stop-color:#D84315" />
    </linearGradient>
    
    <!-- 旗帜渐变 -->
    <linearGradient id="flagGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4CAF50" />
      <stop offset="100%" style="stop-color:#2E7D32" />
    </linearGradient>
  </defs>
  
  <!-- 发光效果 - 多层模拟 -->
  <circle cx="64" cy="64" r="54" fill="url(#travelEpic)" opacity="0.1"/>
  <circle cx="64" cy="64" r="52" fill="url(#travelEpic)" opacity="0.15"/>
  <circle cx="64" cy="64" r="50" fill="url(#travelEpic)" opacity="0.2"/>
  
  <!-- 外层动态光环 -->
  <circle cx="64" cy="64" r="62" fill="none" stroke="url(#travelEpic)" stroke-width="1" opacity="0.3">
    <animate attributeName="r" values="60;66;60" dur="6s" repeatCount="indefinite"/>
    <animate attributeName="opacity" values="0.2;0.5;0.2" dur="6s" repeatCount="indefinite"/>
  </circle>
  
  <!-- 多层装饰边框 -->
  <circle cx="64" cy="64" r="58" fill="none" stroke="url(#travelEpic)" stroke-width="2" opacity="0.6"/>
  <circle cx="64" cy="64" r="54" fill="none" stroke="#fff" stroke-width="1" opacity="0.4"/>
  
  <!-- 主体背景 -->
  <circle cx="64" cy="64" r="48" fill="url(#travelEpic)"/>
  
  <!-- 天空背景 -->
  <circle cx="64" cy="64" r="42" fill="url(#skyGradient)" opacity="0.4"/>
  
  <!-- 内圈装饰 -->
  <circle cx="64" cy="64" r="40" fill="none" stroke="#fff" stroke-width="1.8" opacity="0.7"/>
  <circle cx="64" cy="64" r="34" fill="none" stroke="#fff" stroke-width="1" opacity="0.5"/>
  
  <!-- 山峰图案 - 史诗级设计 -->
  <g transform="translate(64, 64)">
    <!-- 主峰 - 多层发光 -->
    <g transform="translate(0, 5)">
      <!-- 主峰发光底层 -->
      <path d="M0,-25 L-15,15 L15,15 Z" fill="url(#mountainGradient)" opacity="0.3"/>
      <path d="M0,-22 L-12,12 L12,12 Z" fill="url(#mountainGradient)"/>
      
      <!-- 雪峰 -->
      <path d="M0,-22 L-6,-8 L6,-8 Z" fill="url(#snowGradient)" opacity="0.3"/>
      <path d="M0,-20 L-4,-6 L4,-6 Z" fill="url(#snowGradient)"/>
      
      <!-- 山峰纹理 -->
      <g stroke="#455A64" stroke-width="0.5" opacity="0.6">
        <line x1="-8" y1="0" x2="-4" y2="4"/>
        <line x1="8" y1="0" x2="4" y2="4"/>
        <line x1="-6" y1="6" x2="-2" y2="10"/>
        <line x1="6" y1="6" x2="2" y2="10"/>
      </g>
    </g>
    
    <!-- 左侧山峰 -->
    <g transform="translate(-18, 8)">
      <path d="M0,-15 L-8,8 L8,8 Z" fill="url(#mountainGradient)" opacity="0.3"/>
      <path d="M0,-12 L-6,6 L6,6 Z" fill="url(#mountainGradient)" opacity="0.8"/>
      <path d="M0,-12 L-3,-4 L3,-4 Z" fill="url(#snowGradient)" opacity="0.7"/>
    </g>
    
    <!-- 右侧山峰 -->
    <g transform="translate(16, 10)">
      <path d="M0,-12 L-6,6 L6,6 Z" fill="url(#mountainGradient)" opacity="0.3"/>
      <path d="M0,-10 L-4,4 L4,4 Z" fill="url(#mountainGradient)" opacity="0.8"/>
      <path d="M0,-10 L-2,-3 L2,-3 Z" fill="url(#snowGradient)" opacity="0.7"/>
    </g>
    
    <!-- 登山者 - 动态攀登 -->
    <g transform="translate(-8, -5)" opacity="0.9">
      <g>
        <!-- 登山者身体 -->
        <ellipse cx="0" cy="0" rx="1.5" ry="3" fill="url(#climberGradient)" opacity="0.3"/>
        <ellipse cx="0" cy="0" rx="1" ry="2.5" fill="url(#climberGradient)"/>
        
        <!-- 登山者头部 -->
        <circle cx="0" cy="-3" r="1" fill="#FFDBCB"/>
        
        <!-- 登山者装备 -->
        <rect x="-0.5" y="-1" width="1" height="2" fill="#8D6E63"/>
        <circle cx="0" cy="-3" r="1.2" fill="none" stroke="#FFD54F" stroke-width="0.3"/>
        
        <!-- 登山绳 -->
        <path d="M0,2 Q-3,8 -6,14" stroke="#8D6E63" stroke-width="0.8" fill="none"/>
        
        <animateTransform attributeName="transform" 
                         type="translate" 
                         values="-8,-5;-6,-8;-4,-12;-8,-5" 
                         dur="8s" 
                         repeatCount="indefinite"/>
      </g>
    </g>
    
    <!-- 山顶旗帜 -->
    <g transform="translate(0, -17)">
      <!-- 旗杆 -->
      <rect x="0" y="0" width="0.5" height="8" fill="#8D6E63"/>
      
      <!-- 旗帜发光底层 -->
      <rect x="0.5" y="0" width="6" height="4" fill="url(#flagGradient)" opacity="0.3"/>
      <rect x="0.5" y="0.2" width="5" height="3.6" fill="url(#flagGradient)">
        <animate attributeName="width" values="5;5.5;5" dur="2s" repeatCount="indefinite"/>
      </rect>
      
      <!-- 旗帜装饰 -->
      <circle cx="3" cy="2" r="0.8" fill="#FFFFFF" opacity="0.8"/>
      <path d="M2.5,1.5 L3.5,2.5 L2.5,2.5 Z" fill="#FFD54F"/>
    </g>
    
    <!-- 云朵 -->
    <g opacity="0.7">
      <g transform="translate(-22, -18)">
        <ellipse cx="0" cy="0" rx="4" ry="2" fill="#FFFFFF" opacity="0.3"/>
        <ellipse cx="0" cy="0" rx="3" ry="1.5" fill="#FFFFFF" opacity="0.8">
          <animate attributeName="cx" values="0;2;0" dur="8s" repeatCount="indefinite"/>
        </ellipse>
      </g>
      
      <g transform="translate(20, -15)">
        <ellipse cx="0" cy="0" rx="3" ry="1.5" fill="#FFFFFF" opacity="0.3"/>
        <ellipse cx="0" cy="0" rx="2.5" ry="1.2" fill="#FFFFFF" opacity="0.7">
          <animate attributeName="cx" values="0;-1.5;0" dur="10s" repeatCount="indefinite"/>
        </ellipse>
      </g>
    </g>
    
    <!-- 老鹰 -->
    <g transform="translate(12, -20)" opacity="0.8">
      <g>
        <path d="M-2,0 Q0,-1 2,0" stroke="#8D6E63" stroke-width="1.2" fill="none"/>
        <path d="M-1,0.5 Q0,0 1,0.5" stroke="#8D6E63" stroke-width="1" fill="none"/>
        <circle cx="0" cy="0" r="0.3" fill="#8D6E63"/>
        
        <animateTransform attributeName="transform" 
                         type="translate" 
                         values="12,-20;8,-25;15,-18;12,-20" 
                         dur="12s" 
                         repeatCount="indefinite"/>
      </g>
    </g>
    
    <!-- 高度标识 -->
    <g transform="translate(18, -8)" opacity="0.9">
      <rect x="0" y="0" width="8" height="4" rx="2" fill="url(#travelEpic)" opacity="0.8"/>
      <text x="4" y="2.5" text-anchor="middle" fill="#ffffff" font-family="Arial, sans-serif" font-size="2.5" font-weight="bold">8848M</text>
    </g>
    
    <!-- 登山路径 -->
    <g opacity="0.6">
      <path d="M-25,15 Q-20,10 -15,12 Q-10,8 -5,10 Q0,6 5,8 Q10,4 15,6 Q20,2 25,4" 
            stroke="#FFD54F" 
            stroke-width="1.5" 
            fill="none" 
            stroke-dasharray="2,1">
        <animate attributeName="stroke-dashoffset" values="0;-15" dur="5s" repeatCount="indefinite"/>
      </path>
      
      <!-- 路径标记 -->
      <circle cx="-20" cy="11" r="1" fill="#FFD54F">
        <animate attributeName="opacity" values="0.5;1;0.5" dur="2s" repeatCount="indefinite"/>
      </circle>
      <circle cx="-5" cy="10" r="1" fill="#FFD54F">
        <animate attributeName="opacity" values="0.4;0.9;0.4" dur="2.3s" repeatCount="indefinite"/>
      </circle>
      <circle cx="15" cy="6" r="1" fill="#FFD54F">
        <animate attributeName="opacity" values="0.6;1;0.6" dur="1.8s" repeatCount="indefinite"/>
      </circle>
    </g>
    
    <!-- 山峰标识 -->
    <g transform="translate(0, 26)">
      <rect x="-18" y="0" width="36" height="6" rx="3" fill="url(#mountainGradient)" opacity="0.8"/>
      <text x="0" y="4" text-anchor="middle" fill="#ffffff" font-family="Arial, sans-serif" font-size="5" font-weight="bold">SUMMIT</text>
    </g>
    
    <!-- 雪花效果 -->
    <g opacity="0.6">
      <g transform="translate(-15, -12)">
        <path d="M0,-2 L0,2 M-2,0 L2,0 M-1.5,-1.5 L1.5,1.5 M-1.5,1.5 L1.5,-1.5" stroke="#FFFFFF" stroke-width="0.5" fill="none">
          <animate attributeName="transform" values="translate(-15,-12) rotate(0);translate(-15,-12) rotate(360)" dur="8s" repeatCount="indefinite"/>
          <animateTransform attributeName="transform" type="translate" values="0,0;0,3;0,0" dur="3s" repeatCount="indefinite" additive="sum"/>
        </path>
      </g>
      
      <g transform="translate(18, -8)">
        <path d="M0,-1.5 L0,1.5 M-1.5,0 L1.5,0 M-1,-1 L1,1 M-1,1 L1,-1" stroke="#FFFFFF" stroke-width="0.4" fill="none">
          <animate attributeName="transform" values="translate(18,-8) rotate(0);translate(18,-8) rotate(-360)" dur="10s" repeatCount="indefinite"/>
          <animateTransform attributeName="transform" type="translate" values="0,0;0,4;0,0" dur="4s" repeatCount="indefinite" additive="sum"/>
        </path>
      </g>
    </g>
  </g>
  
  <!-- 底部华丽标识 -->
  <circle cx="64" cy="115" r="12" fill="none" stroke="url(#travelEpic)" stroke-width="2.5" opacity="0.7"/>
  <circle cx="64" cy="115" r="8" fill="none" stroke="#1E88E5" stroke-width="1.5" opacity="0.9"/>
  <text x="64" y="119" text-anchor="middle" fill="#083481" font-family="Arial, sans-serif" font-size="8" font-weight="bold" opacity="0.9">山峰</text>
</svg>
