<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="travelEpic" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1E88E5" />
      <stop offset="100%" style="stop-color:#083481" />
    </linearGradient>
    <radialGradient id="coinGradient" cx="30%" cy="30%" r="70%">
      <stop offset="0%" style="stop-color:#FFD700" />
      <stop offset="100%" style="stop-color:#FF9800" />
    </radialGradient>
  </defs>
  
  <circle cx="64" cy="64" r="54" fill="url(#travelEpic)" opacity="0.1"/>
  <circle cx="64" cy="64" r="48" fill="url(#travelEpic)"/>
  <circle cx="64" cy="64" r="40" fill="none" stroke="#fff" stroke-width="1.8" opacity="0.7"/>
  
  <g transform="translate(64, 64)">
    <circle cx="0" cy="2" r="8" fill="url(#coinGradient)"/>
    <circle cx="-6" cy="0" r="6" fill="url(#coinGradient)" opacity="0.9"/>
    <circle cx="6" cy="0" r="6" fill="url(#coinGradient)" opacity="0.9"/>
    <circle cx="0" cy="-4" r="5" fill="url(#coinGradient)" opacity="0.8"/>
    
    <text x="0" y="4" text-anchor="middle" fill="#FFFFFF" font-family="Arial, sans-serif" font-size="6" font-weight="bold">¥</text>
    <text x="-6" y="2" text-anchor="middle" fill="#FFFFFF" font-family="Arial, sans-serif" font-size="4" font-weight="bold">¥</text>
    <text x="6" y="2" text-anchor="middle" fill="#FFFFFF" font-family="Arial, sans-serif" font-size="4" font-weight="bold">¥</text>
  </g>
  
  <circle cx="64" cy="115" r="8" fill="none" stroke="#1E88E5" stroke-width="1.5"/>
  <text x="64" y="119" text-anchor="middle" fill="#083481" font-family="Arial, sans-serif" font-size="8" font-weight="bold">金币</text>
</svg>
