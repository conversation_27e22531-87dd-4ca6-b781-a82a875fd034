<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 旅行类史诗级渐变 -->
    <linearGradient id="travelEpic" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1E88E5" />
      <stop offset="20%" style="stop-color:#1976D2" />
      <stop offset="40%" style="stop-color:#1565C0" />
      <stop offset="60%" style="stop-color:#0D47A1" />
      <stop offset="80%" style="stop-color:#0A3D91" />
      <stop offset="100%" style="stop-color:#083481" />
    </linearGradient>
    
    <!-- 骆驼渐变 -->
    <linearGradient id="camelGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#D7CCC8" />
      <stop offset="100%" style="stop-color:#8D6E63" />
    </linearGradient>
    
    <!-- 沙漠渐变 -->
    <linearGradient id="desertGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFD54F" />
      <stop offset="100%" style="stop-color:#FF8F00" />
    </linearGradient>
    
    <!-- 绿洲渐变 -->
    <radialGradient id="oasisGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#4FC3F7" />
      <stop offset="100%" style="stop-color:#0288D1" />
    </radialGradient>
    
    <!-- 棕榈树渐变 -->
    <linearGradient id="palmGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4CAF50" />
      <stop offset="100%" style="stop-color:#2E7D32" />
    </linearGradient>
    
    <!-- 太阳渐变 -->
    <radialGradient id="sunGradient" cx="30%" cy="30%" r="70%">
      <stop offset="0%" style="stop-color:#FFEB3B" />
      <stop offset="100%" style="stop-color:#FF9800" />
    </radialGradient>
  </defs>
  
  <!-- 发光效果 - 多层模拟 -->
  <circle cx="64" cy="64" r="54" fill="url(#travelEpic)" opacity="0.1"/>
  <circle cx="64" cy="64" r="52" fill="url(#travelEpic)" opacity="0.15"/>
  <circle cx="64" cy="64" r="50" fill="url(#travelEpic)" opacity="0.2"/>
  
  <!-- 外层动态光环 -->
  <circle cx="64" cy="64" r="62" fill="none" stroke="url(#travelEpic)" stroke-width="1" opacity="0.3">
    <animate attributeName="r" values="60;66;60" dur="8s" repeatCount="indefinite"/>
    <animate attributeName="opacity" values="0.2;0.5;0.2" dur="8s" repeatCount="indefinite"/>
  </circle>
  
  <!-- 多层装饰边框 -->
  <circle cx="64" cy="64" r="58" fill="none" stroke="url(#travelEpic)" stroke-width="2" opacity="0.6"/>
  <circle cx="64" cy="64" r="54" fill="none" stroke="#fff" stroke-width="1" opacity="0.4"/>
  
  <!-- 主体背景 -->
  <circle cx="64" cy="64" r="48" fill="url(#travelEpic)"/>
  
  <!-- 沙漠背景 -->
  <circle cx="64" cy="64" r="42" fill="url(#desertGradient)" opacity="0.3"/>
  
  <!-- 内圈装饰 -->
  <circle cx="64" cy="64" r="40" fill="none" stroke="#fff" stroke-width="1.8" opacity="0.7"/>
  <circle cx="64" cy="64" r="34" fill="none" stroke="#fff" stroke-width="1" opacity="0.5"/>
  
  <!-- 游牧者图案 - 史诗级设计 -->
  <g transform="translate(64, 64)">
    <!-- 骆驼商队 - 多层发光 -->
    <g transform="translate(0, 2)">
      <!-- 主骆驼发光底层 -->
      <ellipse cx="0" cy="4" rx="12" ry="6" fill="url(#camelGradient)" opacity="0.3"/>
      <ellipse cx="0" cy="4" rx="10" ry="5" fill="url(#camelGradient)"/>
      
      <!-- 骆驼身体 -->
      <ellipse cx="0" cy="4" rx="8" ry="4" fill="#A1887F"/>
      
      <!-- 骆驼驼峰 -->
      <ellipse cx="-2" cy="0" rx="3" ry="4" fill="url(#camelGradient)"/>
      <ellipse cx="2" cy="1" rx="2.5" ry="3" fill="url(#camelGradient)" opacity="0.8"/>
      
      <!-- 骆驼头部 -->
      <ellipse cx="-8" cy="2" rx="2.5" ry="3" fill="url(#camelGradient)"/>
      <circle cx="-9" cy="1" r="0.5" fill="#424242"/>
      
      <!-- 骆驼腿 -->
      <rect x="-6" y="7" width="1" height="4" rx="0.5" fill="url(#camelGradient)"/>
      <rect x="-2" y="7" width="1" height="4" rx="0.5" fill="url(#camelGradient)"/>
      <rect x="2" y="7" width="1" height="4" rx="0.5" fill="url(#camelGradient)"/>
      <rect x="6" y="7" width="1" height="4" rx="0.5" fill="url(#camelGradient)"/>
      
      <!-- 货物 -->
      <rect x="-4" y="-2" width="8" height="4" rx="2" fill="#8D6E63" opacity="0.8"/>
      <rect x="-3" y="-1" width="6" height="2" rx="1" fill="#A1887F"/>
      
      <!-- 骆驼尾巴 -->
      <path d="M8,4 Q12,6 10,8" stroke="url(#camelGradient)" stroke-width="1.5" fill="none">
        <animate attributeName="d" values="M8,4 Q12,6 10,8;M8,4 Q11,7 9,9;M8,4 Q12,6 10,8" dur="3s" repeatCount="indefinite"/>
      </path>
    </g>
    
    <!-- 远处骆驼 -->
    <g transform="translate(20, 8)" opacity="0.6">
      <ellipse cx="0" cy="2" rx="6" ry="3" fill="url(#camelGradient)"/>
      <ellipse cx="-1" cy="0" rx="1.5" ry="2" fill="url(#camelGradient)"/>
      <ellipse cx="-4" cy="1" rx="1.2" ry="1.5" fill="url(#camelGradient)"/>
    </g>
    
    <!-- 绿洲 - 多层发光 -->
    <g transform="translate(-18, 8)">
      <!-- 绿洲水面发光底层 -->
      <ellipse cx="0" cy="0" rx="8" ry="4" fill="url(#oasisGradient)" opacity="0.3"/>
      <ellipse cx="0" cy="0" rx="6" ry="3" fill="url(#oasisGradient)"/>
      
      <!-- 水面波纹 -->
      <ellipse cx="0" cy="0" rx="5" ry="2" fill="none" stroke="#E3F2FD" stroke-width="0.5" opacity="0.6">
        <animate attributeName="rx" values="5;6;5" dur="2s" repeatCount="indefinite"/>
        <animate attributeName="ry" values="2;2.5;2" dur="2s" repeatCount="indefinite"/>
      </ellipse>
      
      <!-- 棕榈树 -->
      <g transform="translate(-4, -6)">
        <!-- 树干 -->
        <rect x="0" y="0" width="1" height="8" rx="0.5" fill="#8D6E63"/>
        
        <!-- 棕榈叶 -->
        <g fill="url(#palmGradient)">
          <ellipse cx="0.5" cy="-2" rx="4" ry="1" transform="rotate(-30)"/>
          <ellipse cx="0.5" cy="-2" rx="4" ry="1" transform="rotate(30)"/>
          <ellipse cx="0.5" cy="-2" rx="4" ry="1" transform="rotate(0)"/>
          <ellipse cx="0.5" cy="-2" rx="3.5" ry="0.8" transform="rotate(-60)"/>
          <ellipse cx="0.5" cy="-2" rx="3.5" ry="0.8" transform="rotate(60)"/>
        </g>
      </g>
      
      <g transform="translate(4, -4)">
        <rect x="0" y="0" width="0.8" height="6" rx="0.4" fill="#8D6E63"/>
        <g fill="url(#palmGradient)" opacity="0.8">
          <ellipse cx="0.4" cy="-1.5" rx="3" ry="0.8" transform="rotate(-45)"/>
          <ellipse cx="0.4" cy="-1.5" rx="3" ry="0.8" transform="rotate(45)"/>
          <ellipse cx="0.4" cy="-1.5" rx="3" ry="0.8" transform="rotate(0)"/>
        </g>
      </g>
    </g>
    
    <!-- 沙漠太阳 -->
    <g transform="translate(18, -18)">
      <!-- 太阳发光底层 -->
      <circle cx="0" cy="0" r="8" fill="url(#sunGradient)" opacity="0.3">
        <animate attributeName="r" values="7;9;7" dur="4s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.2;0.4;0.2" dur="4s" repeatCount="indefinite"/>
      </circle>
      <circle cx="0" cy="0" r="6" fill="url(#sunGradient)"/>
      
      <!-- 太阳光芒 -->
      <g stroke="#FFEB3B" stroke-width="1" opacity="0.8">
        <line x1="0" y1="-8" x2="0" y2="-10">
          <animate attributeName="opacity" values="0.6;1;0.6" dur="2s" repeatCount="indefinite"/>
        </line>
        <line x1="6" y1="-6" x2="7" y2="-7">
          <animate attributeName="opacity" values="0.5;0.9;0.5" dur="2.2s" repeatCount="indefinite"/>
        </line>
        <line x1="8" y1="0" x2="10" y2="0">
          <animate attributeName="opacity" values="0.7;1;0.7" dur="1.8s" repeatCount="indefinite"/>
        </line>
        <line x1="6" y1="6" x2="7" y2="7">
          <animate attributeName="opacity" values="0.4;0.8;0.4" dur="2.5s" repeatCount="indefinite"/>
        </line>
      </g>
    </g>
    
    <!-- 沙丘 -->
    <g opacity="0.6">
      <ellipse cx="-25" cy="18" rx="8" ry="3" fill="url(#desertGradient)" opacity="0.4"/>
      <ellipse cx="25" cy="20" rx="10" ry="4" fill="url(#desertGradient)" opacity="0.3"/>
      <ellipse cx="0" cy="22" rx="15" ry="5" fill="url(#desertGradient)" opacity="0.2"/>
    </g>
    
    <!-- 游牧路径 -->
    <g opacity="0.7">
      <path d="M-30,15 Q-20,12 -10,15 Q0,18 10,15 Q20,12 30,15" 
            stroke="#8D6E63" 
            stroke-width="2" 
            fill="none" 
            stroke-dasharray="3,2" 
            opacity="0.5">
        <animate attributeName="stroke-dashoffset" values="0;-25" dur="8s" repeatCount="indefinite"/>
      </path>
      
      <!-- 足迹 -->
      <g fill="#8D6E63" opacity="0.4">
        <ellipse cx="-22" cy="16" rx="1" ry="0.5">
          <animate attributeName="opacity" values="0.3;0.7;0.3" dur="3s" repeatCount="indefinite"/>
        </ellipse>
        <ellipse cx="-8" cy="17" rx="1" ry="0.5">
          <animate attributeName="opacity" values="0.2;0.6;0.2" dur="3.5s" repeatCount="indefinite"/>
        </ellipse>
        <ellipse cx="8" cy="16" rx="1" ry="0.5">
          <animate attributeName="opacity" values="0.4;0.8;0.4" dur="2.8s" repeatCount="indefinite"/>
        </ellipse>
      </g>
    </g>
    
    <!-- 游牧帐篷 -->
    <g transform="translate(-22, -8)" opacity="0.8">
      <!-- 帐篷发光底层 -->
      <path d="M0,-4 L-4,4 L4,4 Z" fill="#8D6E63" opacity="0.3"/>
      <path d="M0,-3 L-3,3 L3,3 Z" fill="#8D6E63"/>
      
      <!-- 帐篷装饰 -->
      <rect x="-1" y="2" width="2" height="1" rx="1" fill="#A1887F"/>
      <line x1="0" y1="-3" x2="0" y2="3" stroke="#6D4C41" stroke-width="0.3"/>
    </g>
    
    <!-- 星座导航 -->
    <g opacity="0.6">
      <g transform="translate(-15, -20)">
        <circle cx="0" cy="0" r="0.8" fill="#FFFFFF">
          <animate attributeName="opacity" values="0.5;1;0.5" dur="2s" repeatCount="indefinite"/>
        </circle>
        <circle cx="3" cy="-2" r="0.6" fill="#FFFFFF">
          <animate attributeName="opacity" values="0.4;0.9;0.4" dur="2.5s" repeatCount="indefinite"/>
        </circle>
        <circle cx="6" cy="1" r="0.7" fill="#FFFFFF">
          <animate attributeName="opacity" values="0.6;1;0.6" dur="1.8s" repeatCount="indefinite"/>
        </circle>
        
        <!-- 星座连线 -->
        <g stroke="#FFFFFF" stroke-width="0.3" opacity="0.4">
          <line x1="0" y1="0" x2="3" y2="-2"/>
          <line x1="3" y1="-2" x2="6" y2="1"/>
        </g>
      </g>
    </g>
    
    <!-- 游牧标识 -->
    <g transform="translate(0, 26)">
      <rect x="-15" y="0" width="30" height="6" rx="3" fill="url(#desertGradient)" opacity="0.8"/>
      <text x="0" y="4" text-anchor="middle" fill="#ffffff" font-family="Arial, sans-serif" font-size="5" font-weight="bold">NOMAD</text>
    </g>
    
    <!-- 沙尘效果 -->
    <g opacity="0.4">
      <ellipse cx="-12" cy="12" rx="2" ry="1" fill="#FFD54F">
        <animate attributeName="cx" values="-12;-8;-12" dur="6s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.2;0.6;0.2" dur="6s" repeatCount="indefinite"/>
      </ellipse>
      <ellipse cx="15" cy="14" rx="1.5" ry="0.8" fill="#FFD54F">
        <animate attributeName="cx" values="15;18;15" dur="8s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.1;0.5;0.1" dur="8s" repeatCount="indefinite"/>
      </ellipse>
    </g>
  </g>
  
  <!-- 底部华丽标识 -->
  <circle cx="64" cy="115" r="12" fill="none" stroke="url(#travelEpic)" stroke-width="2.5" opacity="0.7"/>
  <circle cx="64" cy="115" r="8" fill="none" stroke="#1E88E5" stroke-width="1.5" opacity="0.9"/>
  <text x="64" y="119" text-anchor="middle" fill="#083481" font-family="Arial, sans-serif" font-size="8" font-weight="bold" opacity="0.9">游牧</text>
</svg>
