<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 旅行类史诗级渐变 -->
    <linearGradient id="travelEpic" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1E88E5" />
      <stop offset="20%" style="stop-color:#1976D2" />
      <stop offset="40%" style="stop-color:#1565C0" />
      <stop offset="60%" style="stop-color:#0D47A1" />
      <stop offset="80%" style="stop-color:#0A3D91" />
      <stop offset="100%" style="stop-color:#083481" />
    </linearGradient>
    
    <!-- 探险家装备渐变 -->
    <radialGradient id="explorerGear" cx="30%" cy="30%" r="70%">
      <stop offset="0%" style="stop-color:#8D6E63" />
      <stop offset="50%" style="stop-color:#6D4C41" />
      <stop offset="100%" style="stop-color:#5D4037" />
    </radialGradient>
    
    <!-- 指南针渐变 -->
    <radialGradient id="compassGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#FFD700" />
      <stop offset="70%" style="stop-color:#FFC107" />
      <stop offset="100%" style="stop-color:#FF9800" />
    </radialGradient>
    
    <!-- 地图渐变 -->
    <linearGradient id="mapGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFF8E1" />
      <stop offset="100%" style="stop-color:#F57F17" />
    </linearGradient>
    
    <!-- 宝藏渐变 -->
    <radialGradient id="treasureGradient" cx="30%" cy="30%" r="70%">
      <stop offset="0%" style="stop-color:#FFD700" />
      <stop offset="50%" style="stop-color:#FF9800" />
      <stop offset="100%" style="stop-color:#E65100" />
    </radialGradient>
  </defs>
  
  <!-- 发光效果 - 多层模拟 -->
  <circle cx="64" cy="64" r="54" fill="url(#travelEpic)" opacity="0.1"/>
  <circle cx="64" cy="64" r="52" fill="url(#travelEpic)" opacity="0.15"/>
  <circle cx="64" cy="64" r="50" fill="url(#travelEpic)" opacity="0.2"/>
  
  <!-- 外层动态光环 -->
  <circle cx="64" cy="64" r="62" fill="none" stroke="url(#travelEpic)" stroke-width="1" opacity="0.3">
    <animate attributeName="r" values="60;66;60" dur="4s" repeatCount="indefinite"/>
    <animate attributeName="opacity" values="0.2;0.5;0.2" dur="4s" repeatCount="indefinite"/>
  </circle>
  
  <!-- 多层装饰边框 -->
  <circle cx="64" cy="64" r="58" fill="none" stroke="url(#travelEpic)" stroke-width="2" opacity="0.6"/>
  <circle cx="64" cy="64" r="54" fill="none" stroke="#fff" stroke-width="1" opacity="0.4"/>
  
  <!-- 主体背景 -->
  <circle cx="64" cy="64" r="48" fill="url(#travelEpic)"/>
  
  <!-- 内圈装饰 -->
  <circle cx="64" cy="64" r="40" fill="none" stroke="#fff" stroke-width="1.8" opacity="0.7"/>
  <circle cx="64" cy="64" r="34" fill="none" stroke="#fff" stroke-width="1" opacity="0.5"/>
  
  <!-- 探险家图案 - 史诗级设计 -->
  <g transform="translate(64, 64)">
    <!-- 指南针 - 多层发光 -->
    <g transform="translate(0, -8)">
      <circle cx="0" cy="0" r="14" fill="url(#compassGradient)" opacity="0.3">
        <animate attributeName="r" values="13;15;13" dur="3s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.2;0.4;0.2" dur="3s" repeatCount="indefinite"/>
      </circle>
      <circle cx="0" cy="0" r="12" fill="url(#compassGradient)" opacity="0.5"/>
      <circle cx="0" cy="0" r="10" fill="url(#compassGradient)"/>
      
      <!-- 指南针刻度 -->
      <g stroke="#8D6E63" stroke-width="1" opacity="0.8">
        <line x1="0" y1="-10" x2="0" y2="-8"/>
        <line x1="10" y1="0" x2="8" y2="0"/>
        <line x1="0" y1="10" x2="0" y2="8"/>
        <line x1="-10" y1="0" x2="-8" y2="0"/>
        
        <line x1="7" y1="-7" x2="6" y2="-6"/>
        <line x1="7" y1="7" x2="6" y2="6"/>
        <line x1="-7" y1="7" x2="-6" y2="6"/>
        <line x1="-7" y1="-7" x2="-6" y2="-6"/>
      </g>
      
      <!-- 指南针指针 -->
      <g>
        <path d="M0,-6 L-1,0 L0,6 L1,0 Z" fill="#E53935">
          <animate attributeName="transform" values="rotate(0);rotate(360)" dur="20s" repeatCount="indefinite"/>
        </path>
        <circle cx="0" cy="0" r="2" fill="#8D6E63"/>
      </g>
      
      <!-- 方向标识 -->
      <text x="0" y="-14" text-anchor="middle" fill="#fff" font-family="Arial, sans-serif" font-size="4" font-weight="bold">N</text>
      <text x="14" y="2" text-anchor="middle" fill="#fff" font-family="Arial, sans-serif" font-size="3" font-weight="bold">E</text>
      <text x="0" y="18" text-anchor="middle" fill="#fff" font-family="Arial, sans-serif" font-size="3" font-weight="bold">S</text>
      <text x="-14" y="2" text-anchor="middle" fill="#fff" font-family="Arial, sans-serif" font-size="3" font-weight="bold">W</text>
    </g>
    
    <!-- 探险装备 -->
    <g transform="translate(-18, 8)">
      <!-- 背包 -->
      <rect x="0" y="0" width="8" height="12" rx="2" fill="url(#explorerGear)" opacity="0.3"/>
      <rect x="0.5" y="0.5" width="7" height="11" rx="1.5" fill="url(#explorerGear)"/>
      
      <!-- 背包装饰 -->
      <rect x="1" y="2" width="6" height="1" rx="0.5" fill="#A1887F"/>
      <rect x="1" y="5" width="6" height="1" rx="0.5" fill="#A1887F"/>
      <circle cx="2" cy="8" r="0.5" fill="#FFD54F"/>
      <circle cx="6" cy="8" r="0.5" fill="#FFD54F"/>
      
      <!-- 背包带 -->
      <rect x="1.5" y="-2" width="1" height="4" rx="0.5" fill="#8D6E63"/>
      <rect x="5.5" y="-2" width="1" height="4" rx="0.5" fill="#8D6E63"/>
    </g>
    
    <!-- 藏宝图 -->
    <g transform="translate(16, 6)">
      <!-- 地图发光底层 -->
      <rect x="0" y="0" width="10" height="8" rx="1" fill="url(#mapGradient)" opacity="0.3"/>
      <rect x="0.5" y="0.5" width="9" height="7" rx="0.5" fill="url(#mapGradient)"/>
      
      <!-- 地图内容 -->
      <g stroke="#8D6E63" stroke-width="0.5" fill="none" opacity="0.8">
        <path d="M1,2 Q3,1 5,2 Q7,3 9,2"/>
        <path d="M1,4 Q4,3 6,4 Q8,5 9,4"/>
        <circle cx="3" cy="3" r="0.5"/>
        <circle cx="7" cy="5" r="0.5"/>
        <path d="M6,6 L7,5 L8,6 L7,7 Z" fill="#E53935"/>
      </g>
      
      <!-- X标记 -->
      <g stroke="#E53935" stroke-width="1" opacity="0.9">
        <line x1="6" y1="5" x2="8" y2="7"/>
        <line x1="8" y1="5" x2="6" y2="7"/>
      </g>
    </g>
    
    <!-- 宝藏箱 -->
    <g transform="translate(0, 18)">
      <!-- 宝藏箱发光底层 -->
      <rect x="-6" y="0" width="12" height="8" rx="2" fill="url(#treasureGradient)" opacity="0.3"/>
      <rect x="-5" y="1" width="10" height="6" rx="1" fill="url(#treasureGradient)"/>
      
      <!-- 宝藏箱装饰 -->
      <rect x="-4" y="2" width="8" height="1" rx="0.5" fill="#FFD700"/>
      <rect x="-1" y="0" width="2" height="2" rx="1" fill="#8D6E63"/>
      
      <!-- 宝石 -->
      <g opacity="0.9">
        <circle cx="-2" cy="4" r="1" fill="#E91E63">
          <animate attributeName="opacity" values="0.7;1;0.7" dur="2s" repeatCount="indefinite"/>
        </circle>
        <circle cx="2" cy="4" r="1" fill="#9C27B0">
          <animate attributeName="opacity" values="0.6;1;0.6" dur="2.5s" repeatCount="indefinite"/>
        </circle>
        <circle cx="0" cy="5" r="0.8" fill="#4CAF50">
          <animate attributeName="opacity" values="0.8;1;0.8" dur="1.8s" repeatCount="indefinite"/>
        </circle>
      </g>
    </g>
    
    <!-- 探险路径 -->
    <g opacity="0.7">
      <path d="M-25,-15 Q-15,-20 -5,-15 Q5,-10 15,-15 Q25,-20 30,-15" 
            stroke="#FFD54F" 
            stroke-width="2" 
            fill="none" 
            stroke-dasharray="3,2">
        <animate attributeName="stroke-dashoffset" values="0;-25" dur="4s" repeatCount="indefinite"/>
      </path>
      
      <!-- 路径标记点 -->
      <circle cx="-20" cy="-17" r="1.5" fill="#FFD54F">
        <animate attributeName="opacity" values="0.5;1;0.5" dur="2s" repeatCount="indefinite"/>
      </circle>
      <circle cx="0" cy="-12" r="1.5" fill="#FFD54F">
        <animate attributeName="opacity" values="0.4;0.9;0.4" dur="2.3s" repeatCount="indefinite"/>
      </circle>
      <circle cx="20" cy="-17" r="1.5" fill="#FFD54F">
        <animate attributeName="opacity" values="0.6;1;0.6" dur="1.8s" repeatCount="indefinite"/>
      </circle>
    </g>
    
    <!-- 探险标识 -->
    <g transform="translate(0, 30)">
      <rect x="-18" y="0" width="36" height="6" rx="3" fill="url(#treasureGradient)" opacity="0.8"/>
      <text x="0" y="4" text-anchor="middle" fill="#ffffff" font-family="Arial, sans-serif" font-size="5" font-weight="bold">EXPLORER</text>
    </g>
  </g>
  
  <!-- 底部华丽标识 -->
  <circle cx="64" cy="115" r="12" fill="none" stroke="url(#travelEpic)" stroke-width="2.5" opacity="0.7"/>
  <circle cx="64" cy="115" r="8" fill="none" stroke="#1E88E5" stroke-width="1.5" opacity="0.9"/>
  <text x="64" y="119" text-anchor="middle" fill="#083481" font-family="Arial, sans-serif" font-size="8" font-weight="bold" opacity="0.9">探险</text>
</svg>
