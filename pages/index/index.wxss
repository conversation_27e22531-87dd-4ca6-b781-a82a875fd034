/**pages/index/index.wxss**/

.home-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #FF6B6B 0%, #4ECDC4 50%, #45B7D1 100%);
  position: relative;
  overflow: hidden;
  padding: 0;
  box-sizing: border-box;
}

/* 背景装饰 */
.bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.gradient-orb {
  position: absolute;
  border-radius: 50%;
  opacity: 0.6;
  filter: blur(2rpx);
  animation: orbFloat 20s ease-in-out infinite;
}

.orb-1 {
  width: 200rpx;
  height: 200rpx;
  top: 10%;
  right: -5%;
  background: radial-gradient(circle, rgba(255, 107, 107, 0.15) 0%, transparent 70%);
  animation-delay: 0s;
}

.orb-2 {
  width: 150rpx;
  height: 150rpx;
  bottom: 30%;
  left: -5%;
  background: radial-gradient(circle, rgba(78, 205, 196, 0.12) 0%, transparent 70%);
  animation-delay: 7s;
}

.orb-3 {
  width: 120rpx;
  height: 120rpx;
  top: 40%;
  left: 20%;
  background: radial-gradient(circle, rgba(69, 183, 209, 0.1) 0%, transparent 70%);
  animation-delay: 14s;
}

@keyframes orbFloat {
  0%, 100% {
    transform: translateY(0px) translateX(0px) scale(1);
  }
  33% {
    transform: translateY(-30rpx) translateX(20rpx) scale(1.1);
  }
  66% {
    transform: translateY(20rpx) translateX(-15rpx) scale(0.9);
  }
}

/* WeUI适配 - 顶部欢迎区域 */
.welcome-section {
  margin: 32rpx 24rpx 24rpx;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20rpx) saturate(180%);
  border-radius: 32rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 16rpx 64rpx rgba(0, 0, 0, 0.1),
    0 2rpx 0 rgba(255, 255, 255, 0.3) inset;
  position: relative;
  z-index: 1;
}

.welcome-section .weui-cell {
  background: transparent;
  border-bottom: none;
  padding: 48rpx 32rpx 32rpx;
}

.user-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.avatar-container {
  position: relative;
  margin-right: 24rpx;
  transition: all var(--duration-fast);
  cursor: pointer;
}

.avatar-container:active {
  transform: scale(0.95);
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.online-indicator {
  position: absolute;
  bottom: 2rpx;
  right: 2rpx;
  width: 20rpx;
  height: 20rpx;
  background: #52c41a;
  border-radius: 50%;
  border: 2rpx solid white;
}

.greeting {
  flex: 1;
}

.greeting-text {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 400;
}

.user-name {
  display: block;
  font-size: 32rpx;
  color: white;
  font-weight: 600;
  margin: 4rpx 0;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.welcome-desc {
  display: block;
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 400;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.notification-btn,
.profile-btn {
  position: relative;
  width: 48rpx;
  height: 48rpx;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  transition: all var(--duration-fast);
}

.notification-btn:active,
.profile-btn:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.25);
}

.notification-btn .svg-icon,
.profile-btn .svg-icon {
  color: rgba(255, 255, 255, 0.9);
}

.notification-dot {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 12rpx;
  height: 12rpx;
  background: #ff4d4f;
  border-radius: 50%;
  border: 2rpx solid white;
}

/* 主功能卡片 */
.main-card {
  background: rgba(255, 255, 255, 0.12);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.main-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1rpx;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.4) 50%, transparent 100%);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.card-title {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 600;
}

.card-actions {
  display: flex;
  align-items: center;
}

.budget-setting-btn {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 20rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.budget-setting-btn:active {
  background: rgba(255, 255, 255, 0.25);
  transform: scale(0.95);
}

.setting-text {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-left: 6rpx;
}

/* 总支出展示 */
.total-expense {
  margin-bottom: 24rpx;
  text-align: center;
}

.expense-amount {
  display: block;
  font-size: 48rpx;
  color: white;
  font-weight: 700;
  margin-bottom: 8rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.expense-desc {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
}

/* 支出分类卡片 */
.expense-cards {
  display: flex;
  gap: 12rpx;
  margin-bottom: 24rpx;
}

.expense-card {
  flex: 1;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 16rpx;
  padding: 16rpx 12rpx;
  text-align: center;
  border: 1rpx solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
}

.card-label {
  display: block;
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 8rpx;
}

.card-amount {
  display: block;
  font-size: 24rpx;
  color: white;
  font-weight: 600;
  margin-bottom: 4rpx;
}

.card-percentage {
  font-size: 18rpx;
  color: rgba(255, 255, 255, 0.5);
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
  border-top: 3rpx solid rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16rpx;
}

.loading-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 进度条区域 */
.progress-section {
  margin-bottom: 24rpx;
}

.progress-bar {
  height: 8rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4rpx;
  margin-bottom: 8rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #52c41a 0%, #73d13d 100%);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
  display: block;
}

.card-actions {
  display: flex;
  gap: 12rpx;
}

.action-btn {
  flex: 1;
  height: 64rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  font-weight: 500;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.3s ease;
  border: none;
}

.action-btn.primary {
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.action-btn.primary:active {
  transform: translateY(1rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}

.action-btn.secondary {
  background: rgba(255, 255, 255, 0.15);
  color: rgba(255, 255, 255, 0.9);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.action-btn.secondary:active {
  background: rgba(255, 255, 255, 0.2);
}

.btn-icon {
  width: 16rpx;
  height: 16rpx;
  margin-right: 6rpx;
}

/* 功能网格 */
.feature-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
}

.grid-item {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 24rpx;
  backdrop-filter: blur(15rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.15);
  position: relative;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.grid-item:active {
  transform: translateY(2rpx);
  background: rgba(255, 255, 255, 0.15);
}

.item-icon {
  width: 56rpx;
  height: 56rpx;
  border-radius: 16rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 16rpx;
  position: relative;
}

.item-icon.travel {
  background: linear-gradient(135deg, #FF6B6B 0%, #FF8E9B 100%);
}

.item-icon.accounting {
  background: linear-gradient(135deg, #4ECDC4 0%, #44A08D 100%);
}

.item-icon.social {
  background: linear-gradient(135deg, #45B7D1 0%, #96CEB4 100%);
}

.item-icon.discover {
  background: linear-gradient(135deg, #FFEAA7 0%, #DDA0DD 100%);
}

.item-icon svg {
  color: white;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}

.item-title {
  font-size: 28rpx;
  color: white;
  font-weight: 600;
  margin-bottom: 4rpx;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.item-desc {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 16rpx;
  flex: 1;
}

.item-arrow {
  position: absolute;
  top: 24rpx;
  right: 24rpx;
  width: 24rpx;
  height: 24rpx;
  color: rgba(255, 255, 255, 0.5);
  transition: all 0.3s ease;
}

.grid-item:active .item-arrow {
  color: rgba(255, 255, 255, 0.8);
  transform: translateX(4rpx);
}

/* 通用SVG图标样式 */
.svg-icon {
  display: flex;
  justify-content: center;
  align-items: center;
}

.svg-icon svg {
  width: 100%;
  height: 100%;
}

/* ===== Vant组件适配样式 ===== */

/* 欢迎区域Vant适配 */
.welcome-section {
  margin: 32rpx 24rpx 24rpx;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20rpx) saturate(180%);
  border-radius: 32rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 16rpx 64rpx rgba(0, 0, 0, 0.1),
    0 2rpx 0 rgba(255, 255, 255, 0.3) inset;
}

.welcome-section .van-cell {
  background: transparent !important;
  border-bottom: none !important;
  padding: 48rpx 32rpx 32rpx !important;
}

/* 主卡片Vant适配 */
.main-card {
  margin: 32rpx 24rpx !important;
  background: rgba(255, 255, 255, 0.15) !important;
  backdrop-filter: blur(20rpx) saturate(180%);
  border-radius: 32rpx !important;
  border: 1rpx solid rgba(255, 255, 255, 0.2) !important;
  box-shadow:
    0 16rpx 64rpx rgba(0, 0, 0, 0.1),
    0 2rpx 0 rgba(255, 255, 255, 0.3) inset !important;
}

.main-card .van-card__header {
  background: transparent !important;
  color: rgba(255, 255, 255, 0.95) !important;
  font-weight: 600 !important;
}

.main-card .van-card__content {
  background: transparent !important;
  padding: 24rpx 32rpx !important;
}

/* 功能网格Vant适配 */
.feature-grid {
  margin: 32rpx 24rpx !important;
  background: rgba(255, 255, 255, 0.15) !important;
  backdrop-filter: blur(20rpx) saturate(180%);
  border-radius: 32rpx !important;
  border: 1rpx solid rgba(255, 255, 255, 0.2) !important;
  box-shadow:
    0 16rpx 64rpx rgba(0, 0, 0, 0.1),
    0 2rpx 0 rgba(255, 255, 255, 0.3) inset !important;
}

.feature-grid .van-grid-item {
  background: transparent !important;
}

.feature-grid .van-grid-item__content {
  background: transparent !important;
  padding: 32rpx !important;
}

/* 支出网格适配 */
.expense-grid {
  margin: 24rpx 0 !important;
}

.expense-grid .van-grid-item {
  background: transparent !important;
}

.expense-card {
  text-align: center;
  padding: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.card-label {
  display: block;
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
}

.card-amount {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.95);
}

/* 网格图标样式 */
.grid-icon {
  margin-bottom: 12rpx;
}

/* 网格文本样式 */
.grid-text {
  text-align: center;
}

.item-title {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.95);
  margin-bottom: 4rpx;
}

.item-desc {
  display: block;
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
}

/* Vant按钮适配 */
.budget-setting-btn {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1rpx solid rgba(255, 255, 255, 0.2) !important;
  color: rgba(255, 255, 255, 0.8) !important;
  border-radius: 12rpx !important;
  display: flex !important;
  align-items: center !important;
  gap: 8rpx !important;
}

.setting-text {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 移除重复的action-btn样式 */

/* 进度条样式 */
.progress-section {
  margin: 24rpx 0;
}

.progress-text {
  display: block;
  text-align: center;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 12rpx;
}

/* 头像容器 */
.user-avatar {
  border: 2rpx solid rgba(255, 255, 255, 0.3) !important;
}

/* 图标样式类 */
.icon-small {
  width: 16rpx;
  height: 16rpx;
}

.icon-image {
  width: 48rpx;
  height: 48rpx;
}

.icon-medium {
  width: 40rpx;
  height: 40rpx;
}

.icon-large {
  width: 64rpx;
  height: 64rpx;
}

/* 头部操作按钮 */
.header-actions {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.notification-btn,
.profile-btn {
  position: relative;
  padding: 8rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
}

.notification-dot {
  position: absolute;
  top: 4rpx;
  right: 4rpx;
  width: 12rpx;
  height: 12rpx;
  background: #ff4d4f;
  border-radius: 50%;
  border: 2rpx solid rgba(255, 255, 255, 0.8);
}

/* 新布局样式 */

/* 用户欢迎区域 */
.welcome-area {
  padding: 88rpx 32rpx 48rpx;
  position: relative;
  z-index: 10;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.user-greeting {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
}

.online-status {
  width: 16rpx;
  height: 16rpx;
  background: #4ECDC4;
  border-radius: 50%;
  margin-top: 8rpx;
  box-shadow: 0 0 16rpx rgba(78, 205, 196, 0.6);
}

.greeting-text {
  display: flex;
  flex-direction: column;
}

.greeting-line1 {
  font-size: 32rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.2;
}

.greeting-line2 {
  font-size: 48rpx;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.95);
  line-height: 1.2;
}

.welcome-subtitle {
  position: absolute;
  bottom: 16rpx;
  left: 48rpx;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
}

.user-actions {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.notification-icon {
  position: relative;
}

.notification-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: #FF6B6B;
  color: white;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 20rpx;
  min-width: 32rpx;
  text-align: center;
  line-height: 1;
}

/* 财务卡片 */
.finance-card {
  margin: 0 32rpx 32rpx;
  padding: 32rpx 32rpx 24rpx 32rpx;
  position: relative;
  z-index: 10;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.95);
}

.setting-btn {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
  padding: 12rpx 24rpx;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

.main-amount {
  text-align: center;
  margin-bottom: 48rpx;
}

.amount-currency {
  font-size: 48rpx;
  font-weight: 300;
  color: rgba(255, 255, 255, 0.9);
  margin-right: 8rpx;
}

.amount-value {
  font-size: 72rpx;
  font-weight: 700;
  color: rgba(255, 255, 255, 0.95);
  text-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.amount-label {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  margin-top: 8rpx;
}

/* 支出分类 */
.expense-categories {
  display: flex;
  justify-content: space-between;
  margin-bottom: 32rpx;
}

.category-item {
  flex: 1;
  text-align: center;
  padding: 0 16rpx;
}

.category-label {
  display: block;
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 8rpx;
}

.category-amount {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 4rpx;
}

.category-percent {
  display: block;
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.6);
}

/* 预算进度 */
.budget-progress {
  margin-bottom: 24rpx;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.progress-label {
  font-size: 28rpx;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
}

.progress-remaining {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
}

.progress-bar-container {
  width: 100%;
  height: 8rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-bar {
  width: 100%;
  height: 100%;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #FF6B6B 0%, #4ECDC4 100%);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}



.btn-content {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.btn-icon {
  width: 48rpx;
  height: 48rpx;
  background: linear-gradient(135deg, #4ECDC4 0%, #44A08D 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(78, 205, 196, 0.3);
}

.icon-image {
  width: 24rpx;
  height: 24rpx;
  filter: brightness(0) invert(1);
}

.btn-text {
  display: flex;
  flex-direction: column;
  gap: 2rpx;
}

.btn-title {
  font-size: 26rpx;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.95);
  line-height: 1.2;
}

.btn-subtitle {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.2;
}

.btn-arrow {
  width: 28rpx;
  height: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.arrow-icon {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
  font-weight: 300;
  transform: translateX(2rpx);
}



/* 功能模块 */
.feature-modules {
  padding: 0 32rpx 32rpx;
}

.module-row {
  display: flex;
  gap: 24rpx;
  margin-bottom: 24rpx;
}

.module-item {
  flex: 1;
  background: rgba(255, 255, 255, 0.12);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  padding: 32rpx 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.module-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  border-radius: 24rpx;
  pointer-events: none;
}

.module-icon {
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
  position: relative;
  z-index: 1;
}

/* 图标内部容器 - 白色背景增强对比度 */
.module-icon .icon-container {
  width: 72rpx;
  height: 72rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 50%;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10rpx);
  display: flex;
  align-items: center;
  justify-content: center;
}

.module-icon.travel {
  background: linear-gradient(135deg, #45B7D1, #96CEB4);
  box-shadow: 0 8rpx 24rpx rgba(69, 183, 209, 0.3);
}

.module-icon.accounting {
  background: linear-gradient(135deg, #FF6B6B, #4ECDC4);
  box-shadow: 0 8rpx 24rpx rgba(255, 107, 107, 0.3);
}

.module-icon.social {
  background: linear-gradient(135deg, #ff9a9e, #fecfef);
  box-shadow: 0 8rpx 24rpx rgba(255, 154, 158, 0.3);
}

.module-icon.discover {
  background: linear-gradient(135deg, #667eea, #764ba2);
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
}

.module-content {
  position: relative;
  z-index: 1;
}

.module-title {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.95);
  margin-bottom: 8rpx;
}

.module-desc {
  display: block;
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
}
