/* pages/profile/index.wxss */
@import "../../styles/theme.wxss";

.profile-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #FF6B6B 0%, #4ECDC4 50%, #45B7D1 100%);
  position: relative;
  overflow: hidden;
}

/* 顶部用户信息区域 */
.profile-header {
  position: relative;
  padding: 88rpx 32rpx 40rpx;
  overflow: hidden;
}

.header-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: transparent;
}

.user-info {
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.avatar-section {
  position: relative;
  margin-bottom: 24rpx;
  cursor: pointer;
}

.avatar-edit-hint {
  position: absolute;
  bottom: 8rpx;
  right: 8rpx;
  width: 48rpx;
  height: 48rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10rpx);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.edit-icon {
  width: 24rpx;
  height: 24rpx;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 4rpx solid rgba(255, 255, 255, 0.9);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
}

.avatar-border {
  position: absolute;
  top: -6rpx;
  left: -6rpx;
  right: -6rpx;
  bottom: -6rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.5);
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 0.5; transform: scale(1); }
  50% { opacity: 0.8; transform: scale(1.05); }
}

.user-details {
  color: white;
}

.user-name {
  font-size: 36rpx;
  font-weight: 600;
  color: white;
  margin-bottom: 16rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.user-status {
  display: flex;
  justify-content: center;
}

.status-tag {
  padding: 8rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.status-tag.guest {
  background: rgba(255, 255, 255, 0.15);
  color: rgba(255, 255, 255, 0.9);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10rpx);
}

.status-tag.member {
  background: rgba(255, 255, 255, 0.9);
  color: #FF6B6B;
  box-shadow: 0 4rpx 12rpx rgba(255, 255, 255, 0.3);
}



.stats-card {
  background: rgba(255, 255, 255, 0.12);
  border-radius: 24rpx;
  padding: 32rpx;
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
}

.stat-item {
  flex: 1;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
}

.stat-item:active {
  transform: scale(0.98);
}

.stat-icon {
  width: 60rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  backdrop-filter: blur(10rpx);
}



.stat-divider {
  width: 1rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.2);
  margin: 0 24rpx;
}

/* 功能菜单 */
.menu-section {
  padding: 0 32rpx;
}

.menu-group {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  backdrop-filter: blur(15rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.15);
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  position: relative;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(2rpx);
}

.menu-icon {
  width: 48rpx;
  height: 48rpx;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  backdrop-filter: blur(10rpx);
}

.menu-title {
  font-size: 30rpx;
  font-weight: 500;
  color: white;
  margin-bottom: 4rpx;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.menu-subtitle {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  flex: 1;
}

.menu-arrow {
  margin-left: auto;
  opacity: 0.5;
}

/* 退出登录 */
.logout-section {
  padding: 32rpx;
}

.logout-btn {
  width: 100%;
  height: 88rpx;
  background: rgba(255, 255, 255, 0.9);
  color: #FF6B6B;
  border: 2rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
  font-size: 30rpx;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10rpx);
}

.logout-btn:active {
  background: #FF6B6B;
  color: white;
  transform: scale(0.98);
}

/* 响应式适配 */
@media (max-width: 375px) {
  .profile-header {
    padding: 40rpx 24rpx 32rpx;
  }

  .user-avatar {
    width: 100rpx;
    height: 100rpx;
  }

  .user-name {
    font-size: 32rpx;
  }



  .menu-section {
    padding: 0 24rpx;
  }
}

/* 头像按钮样式重置 */
.avatar-section {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  outline: none;
  width: 100%;
  height: 100%;
  position: relative;
}

.avatar-section::after {
  border: none;
}

.option-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.option-text {
  flex: 1;
  text-align: left;
}

/* ===== Vant组件适配样式 ===== */



/* 菜单区域Vant适配 */
.menu-section,
.settings-section {
  margin: 32rpx 24rpx !important;
  background: rgba(255, 255, 255, 0.15) !important;
  backdrop-filter: blur(20rpx) saturate(180%);
  border-radius: 32rpx !important;
  border: 1rpx solid rgba(255, 255, 255, 0.2) !important;
  box-shadow:
    0 16rpx 64rpx rgba(0, 0, 0, 0.1),
    0 2rpx 0 rgba(255, 255, 255, 0.3) inset !important;
}

.menu-section .van-cell,
.settings-section .van-cell {
  background: transparent !important;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.1) !important;
  padding: 24rpx 32rpx !important;
}

.menu-section .van-cell:last-child,
.settings-section .van-cell:last-child {
  border-bottom: none !important;
}

/* Vant Cell 标题和标签样式 */
.menu-section .van-cell__title,
.settings-section .van-cell__title {
  color: rgba(255, 255, 255, 0.9) !important;
  font-weight: 500 !important;
}

.menu-section .van-cell__label,
.settings-section .van-cell__label {
  color: rgba(255, 255, 255, 0.6) !important;
  font-size: 22rpx !important;
}



/* 退出登录按钮 */
.logout-btn {
  width: calc(100% - 48rpx) !important;
  margin: 32rpx 24rpx !important;
  background: linear-gradient(135deg, #ff4d4f, #ff7875) !important;
  border: none !important;
  border-radius: 16rpx !important;
  box-shadow: 0 8rpx 24rpx rgba(255, 77, 79, 0.3) !important;
}

/* 图标样式类 */
.icon-image {
  width: 48rpx;
  height: 48rpx;
}

.icon-large {
  width: 64rpx;
  height: 64rpx;
}

