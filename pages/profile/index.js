// pages/profile/index.js
import auth from '../../utils/auth.js'
import storage from '../../utils/storage.js'
import dataManager from '../../utils/data-manager.js'
import performanceHelper from '../../utils/performance-helper.js'
import cacheManager, { CACHE_KEYS, CACHE_TTL } from '../../utils/cache-manager.js'
import userManager from '../../utils/userManager.js'
import badgeManager from '../../utils/badge-manager.js'
import { PAGES, SUBPACKAGES } from '../../utils/constants.js'

Page({

  /**
   * 页面的初始数据
   */
  data: {
    userInfo: {},
    hasUserInfo: false,
    isGuest: false,
    isUpdatingAvatar: false,  // 头像更新标志

    financialData: {
      totalExpense: 0,
      monthlyExpense: 0,
      budgetUsage: 0
    },
    travelData: {
      travelCount: 0,
      totalDays: 0,
      visitedCities: 0
    },
    socialData: {
      friendCount: 0,
      shareCount: 0,
      likeCount: 0
    },
    loading: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(options) {
    // 检查登录状态
    if (!auth.checkPageAuth(this)) {
      return
    }

    // 使用用户管理器初始化页面
    await userManager.mixinPage(this)

    // 初始化页面数据
    this.initPageData()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  async onShow() {
    // 检查登录状态
    if (!auth.checkPageAuth(this)) {
      return
    }

    // 🔥 如果正在更新头像，跳过强制刷新
    if (this.data.isUpdatingAvatar) {
      performanceHelper.batchSetData(this, { isUpdatingAvatar: false })
      return
    }

    // 智能刷新逻辑：检查缓存有效性
    const userStatsKey = CACHE_KEYS.USER_STATS
    const shouldRefresh = !cacheManager.has(userStatsKey) ||
                         !this.data.userInfo ||
                         !this.data.userInfo.openid

    // 🔥 只在必要时刷新用户信息（避免覆盖刚更新的头像）
    if (shouldRefresh) {
      if (!this.data.userInfo || !this.data.userInfo.openid) {
        await userManager.forceRefreshUserInfo()
      }
      await userManager.mixinPage(this)

      // 刷新页面数据
      this.refreshData()
    }
  },

  onUnload() {
    // 清理用户管理器监听器
    userManager.cleanupPage(this)

    // 清理性能助手资源
    performanceHelper.cleanupPage(this)
  },

  /**
   * 初始化页面数据
   */
  async initPageData() {
    try {


      // 加载用户统计数据
      await this.loadUserStats()

      // 检查徽章进度
      await this.checkBadgeProgress()

    } catch (error) {
      // 静默处理初始化失败
    }
  },

  /**
   * 刷新页面数据
   */
  async refreshData() {
    try {
      await this.loadUserStats()
    } catch (error) {
      // 静默处理刷新失败
    }
  },

  /**
   * 加载用户统计数据 - 优化版本
   */
  async loadUserStats() {
    if (this.data.loading) return

    // 检查缓存
    const cacheKey = CACHE_KEYS.USER_STATS
    const cachedData = cacheManager.get(cacheKey)

    if (cachedData) {
      performanceHelper.smartSetData(this, cachedData)
      return
    }

    performanceHelper.batchSetData(this, { loading: true })

    try {
      // 并行加载各种统计数据
      const [financialResult, travelResult, socialResult] = await Promise.allSettled([
        this.loadFinancialData(),
        this.loadTravelData(),
        this.loadSocialData()
      ])

      // 处理加载结果
      const updateData = { loading: false }

      if (financialResult.status === 'fulfilled' && financialResult.value.success) {
        updateData.financialData = financialResult.value.data
      }

      if (travelResult.status === 'fulfilled' && travelResult.value.success) {
        updateData.travelData = travelResult.value.data
      }

      if (socialResult.status === 'fulfilled' && socialResult.value.success) {
        updateData.socialData = socialResult.value.data
      }

      // 批量更新数据
      performanceHelper.smartSetData(this, updateData)

      // 缓存成功的数据
      if (Object.keys(updateData).length > 1) { // 除了loading之外还有其他数据
        cacheManager.set(cacheKey, updateData, CACHE_TTL.MEDIUM)
      }

    } catch (error) {
      performanceHelper.batchSetData(this, { loading: false })
    }
  },

  /**
   * 加载财务数据
   */
  async loadFinancialData() {
    try {
      const result = await dataManager.getFinancialOverview()
      return result
    } catch (error) {
      return { success: false, message: error.message }
    }
  },

  /**
   * 加载旅行数据
   */
  async loadTravelData() {
    try {
      // TODO: 实现旅行数据服务调用
      return {
        success: true,
        data: {
          travelCount: 0,
          totalDays: 0,
          visitedCities: 0
        }
      }
    } catch (error) {
      console.error('加载旅行数据失败:', error)
      return { success: false, message: error.message }
    }
  },

  /**
   * 加载社交数据
   */
  async loadSocialData() {
    try {
      // TODO: 实现社交数据服务调用
      return {
        success: true,
        data: {
          friendCount: 0,
          shareCount: 0,
          likeCount: 0
        }
      }
    } catch (error) {
      console.error('加载社交数据失败:', error)
      return { success: false, message: error.message }
    }
  },

  // ==================== 页面导航方法 ====================

  // 🔥 更换头像 - 使用微信原生API
  changeAvatar() {
    // 微信原生头像选择已通过button的open-type="chooseAvatar"实现
  },

  // 🔥 头像更新成功处理
  handleAvatarUpdateSuccess() {
    this.setData({
      userInfo: userManager.userInfo,
      hasUserInfo: true,
      isUpdatingAvatar: false
    })

    // 显示成功提示
    wx.hideLoading()
    wx.showToast({
      title: '头像切换成功',
      icon: 'success'
    })
  },

  // 🔥 处理微信头像选择结果
  async onChooseAvatar(e) {
    try {

      // 检查事件对象和detail
      if (!e || !e.detail) {
        return
      }

      const { avatarUrl } = e.detail

      if (!avatarUrl) {
        return
      }

      wx.showLoading({ title: '保存头像中...' })

      // 🔥 重要：将微信临时头像上传到云存储进行持久化
      // 微信的avatarUrl是临时地址，需要上传到云存储才能持久保存
      this.uploadAvatarToCloud(avatarUrl)

    } catch (error) {
      console.error('头像选择处理失败:', error)
      wx.hideLoading()

      // 只有在真正出错时才显示错误提示，用户取消不显示
      if (error.message !== '用户取消了头像选择') {
        wx.showToast({
          title: '头像更新失败',
          icon: 'none'
        })
      }
    }
  },

  // 🔥 处理头像选择失败
  onChooseAvatarFail(e) {

    // 检查是否是用户取消操作
    if (e && e.detail && e.detail.errMsg) {
      if (e.detail.errMsg.includes('cancel')) {
        return
      }

      if (e.detail.errMsg.includes('ENOENT')) {

        // 提供备用方案
        wx.showModal({
          title: '头像选择失败',
          content: '微信头像选择遇到问题，是否使用自定义头像？',
          confirmText: '自定义头像',
          cancelText: '稍后再试',
          success: (res) => {
            if (res.confirm) {
              this.uploadCustomAvatar()
            }
          }
        })
        return
      }
    }

    // 其他错误情况
    wx.showToast({
      title: '头像选择失败',
      icon: 'none'
    })
  },

  // 🔥 备用头像选择方案
  showAvatarOptions() {
    wx.showActionSheet({
      itemList: ['使用微信头像', '自定义头像', '使用默认头像'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            // 尝试使用微信头像
            this.tryChooseWechatAvatar()
            break
          case 1:
            // 自定义头像
            this.uploadCustomAvatar()
            break
          case 2:
            // 使用默认头像
            this.useDefaultAvatar()
            break
        }
      }
    })
  },

  // 🔥 尝试选择微信头像
  tryChooseWechatAvatar() {
    // 这里可以添加更多的错误处理逻辑
  },

  // 🔥 使用默认头像
  async useDefaultAvatar() {
    try {
      const updatedUserInfo = {
        ...this.data.userInfo,
        avatarUrl: '/images/user.svg'
      }

      const success = await userManager.updateUserInfo(updatedUserInfo)

      if (success) {
        this.handleAvatarUpdateSuccess()
        wx.showToast({
          title: '已设置默认头像',
          icon: 'success'
        })
      }
    } catch (error) {
      console.error('设置默认头像失败:', error)
      wx.showToast({
        title: '设置失败',
        icon: 'none'
      })
    }
  },

  // 🔥 显示头像选择选项 (已移除，使用微信原生API)
  // showAvatarChoiceModal() 方法已移除，现在使用微信原生的chooseAvatar API

  // 🔥 获取微信头像（旧方法，保留用于兼容）
  async getWechatAvatar() {
    try {
      // 设置更新标志
      this.setData({ isUpdatingAvatar: true })

      wx.showLoading({ title: '获取微信头像...' })

      // 调用微信getUserProfile获取最新头像
      const userProfile = await new Promise((resolve, reject) => {
        wx.getUserProfile({
          desc: '获取您的最新微信头像',
          success: resolve,
          fail: reject
        })
      })

      if (userProfile && userProfile.userInfo && userProfile.userInfo.avatarUrl) {
        // 更新用户头像
        const updatedUserInfo = {
          ...this.data.userInfo,
          avatarUrl: userProfile.userInfo.avatarUrl
        }

        // 使用userManager更新用户信息
        const success = await userManager.updateUserInfo(updatedUserInfo)

        if (success) {
          // 确保页面数据与userManager同步
          this.setData({
            userInfo: userManager.userInfo,  // 使用userManager的最新数据
            hasUserInfo: true,
            isUpdatingAvatar: false  // 清除更新标志
          })

          wx.hideLoading()
          wx.showToast({
            title: '微信头像更新成功',
            icon: 'success'
          })
        } else {
          throw new Error('保存头像失败')
        }
      } else {
        throw new Error('获取微信头像失败')
      }
    } catch (error) {
      console.error('获取微信头像失败:', error)
      wx.hideLoading()

      if (error.errMsg && error.errMsg.includes('auth deny')) {
        wx.showToast({
          title: '您拒绝了授权',
          icon: 'none'
        })
      } else {
        wx.showToast({
          title: '获取微信头像失败',
          icon: 'none'
        })
      }
    }
  },

  // 🔥 上传自定义头像（备用选项）
  uploadCustomAvatar() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      sizeType: ['compressed'], // 压缩图片
      maxDuration: 30,
      success: (res) => {
        if (res.tempFiles && res.tempFiles.length > 0) {
          const tempFilePath = res.tempFiles[0].tempFilePath
          this.uploadAvatarToCloud(tempFilePath)
        } else {
          wx.showToast({
            title: '未选择图片',
            icon: 'none'
          })
        }
      },
      fail: (err) => {
        console.error('选择头像失败:', err)

        // 用户取消不显示错误
        if (err.errMsg && err.errMsg.includes('cancel')) {
          return
        }

        // 权限问题
        if (err.errMsg && err.errMsg.includes('auth deny')) {
          wx.showModal({
            title: '权限提示',
            content: '需要访问您的相册权限，请在设置中开启',
            showCancel: false
          })
          return
        }

        // 其他错误
        wx.showToast({
          title: '选择头像失败',
          icon: 'none'
        })
      }
    })
  },

  // 🔥 上传头像到云存储 - 确保头像持久化存储
  // 重要说明：微信chooseAvatar返回的avatarUrl是临时地址，有效期很短
  // 必须上传到云存储才能实现持久化，否则用户每次都需要重新设置头像
  async uploadAvatarToCloud(tempFilePath) {
    wx.showLoading({ title: '上传中...' })

    try {
      const cloudPath = `avatars/${this.data.userInfo.openid || 'user'}_${Date.now()}.jpg`

      const uploadRes = await wx.cloud.uploadFile({
        cloudPath: cloudPath,
        filePath: tempFilePath
      })

      // 更新用户头像
      const updatedUserInfo = {
        ...this.data.userInfo,
        avatarUrl: uploadRes.fileID
      }

      // 使用userManager更新用户信息
      const success = await userManager.updateUserInfo(updatedUserInfo)

      if (success) {
        // 使用通用的成功处理方法
        this.handleAvatarUpdateSuccess()
      } else {
        throw new Error('保存头像失败')
      }
    } catch (error) {
      console.error('上传头像失败:', error)
      wx.hideLoading()
      wx.showToast({
        title: '头像上传失败',
        icon: 'none'
      })
    }
  },

  /**
   * 导航到设置页面
   */
  navigateToSettings() {
    wx.navigateTo({
      url: `${SUBPACKAGES.SETTINGS}pages/index/index`
    })
  },

  /**
   * 导航到记账页面 - 默认日常记账
   */
  navigateToAccount() {
    wx.navigateTo({
      url: '/subpackages/account/travel-expense/index?mode=daily',
      fail: (err) => {
        wx.showToast({
          title: '功能开发中',
          icon: 'none'
        })
      }
    })
  },

  /**
   * 导航到旅行规划页面
   */
  navigateToTravelPlanning() {
    wx.navigateTo({
      url: '/subpackages/travel-planning/create-plan/index',
      fail: (err) => {

        wx.showToast({
          title: '功能开发中',
          icon: 'none'
        })
      }
    })
  },



  /**
   * 导航到社交页面
   */
  navigateToSocial() {
    wx.navigateTo({
      url: '/subpackages/social/friend-list/index',
      fail: (err) => {
        wx.showToast({
          title: '功能开发中',
          icon: 'none'
        })
      }
    })
  },

  /**
   * 导航到预算管理
   */
  navigateToBudget() {
    wx.navigateTo({
      url: '/subpackages/settings/budget-setting/index',
      fail: (err) => {
        wx.showToast({
          title: '功能开发中',
          icon: 'none'
        })
      }
    })
  },

  /**
   * 导航到统计报表
   */
  navigateToReports() {
    wx.navigateTo({
      url: '/subpackages/account/comprehensive-analysis/index',
      fail: (err) => {
        wx.showToast({
          title: '功能开发中',
          icon: 'none'
        })
      }
    })
  },



  /**
   * 导航到个人设置
   */
  navigateToPersonalSettings() {
    wx.navigateTo({
      url: '/subpackages/settings/user-settings/index',
      fail: (err) => {

        wx.showToast({
          title: '功能开发中',
          icon: 'none'
        })
      }
    })
  },

  /**
   * 导航到隐私设置
   */
  navigateToPrivacy() {
    wx.navigateTo({
      url: '/subpackages/settings/privacy/index',
      fail: (err) => {

        wx.showToast({
          title: '功能开发中',
          icon: 'none'
        })
      }
    })
  },

  /**
   * 导航到隐私设置
   */
  navigateToPrivacy() {
    wx.navigateTo({
      url: '/subpackages/settings/privacy/index',
      fail: (err) => {

        wx.showToast({
          title: '功能开发中',
          icon: 'none'
        })
      }
    })
  },

  /**
   * 导航到帮助页面
   */
  navigateToHelp() {
    wx.navigateTo({
      url: '/subpackages/settings/help/index',
      fail: (err) => {
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        })
      }
    })
  },

  /**
   * 导航到关于页面
   */
  navigateToAbout() {
    wx.navigateTo({
      url: '/subpackages/settings/about/index',
      fail: (err) => {
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        })
      }
    })
  },

  // ==================== 用户操作方法 ====================

  /**
   * 处理退出登录
   */
  handleLogout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      confirmText: '退出',
      confirmColor: '#ff4d4f',
      success: (res) => {
        if (res.confirm) {
          // 触觉反馈
          if (wx.canIUse('vibrateShort')) {
            wx.vibrateShort({ type: 'light' })
          }

          // 执行退出登录
          auth.logout()
        }
      }
    })
  },

  // ==================== 徽章相关方法 ====================

  /**
   * 处理徽章点击事件
   */
  onBadgeClick(e) {
    const { badge } = e.detail
    console.log('点击徽章:', badge)

    // 触觉反馈
    if (wx.canIUse('vibrateShort')) {
      wx.vibrateShort({ type: 'light' })
    }
  },

  /**
   * 查看所有徽章
   */
  onViewAllBadges() {
    console.log('查看所有徽章')

    // 触觉反馈
    if (wx.canIUse('vibrateShort')) {
      wx.vibrateShort({ type: 'light' })
    }

    // 跳转到徽章详情页面
    wx.navigateTo({
      url: '/subpackages/settings/badges/index',
      fail: (err) => {
        console.error('跳转徽章页面失败:', err)
        wx.showToast({
          title: '功能开发中',
          icon: 'none'
        })
      }
    })
  },

  /**
   * 检查并更新徽章进度
   */
  async checkBadgeProgress() {
    try {
      // 检查个人资料完成度徽章
      const profileComplete = this.data.userInfo.nickName && this.data.userInfo.avatarUrl
      if (profileComplete) {
        await badgeManager.checkBadgeProgress('new_user', { profileComplete: 1 })
      }

      // 检查其他可能的徽章
      // 这里可以根据用户数据检查更多徽章

    } catch (error) {
      console.error('检查徽章进度失败:', error)
    }
  },

  /**
   * 传说级仪式完成处理
   */
  onLegendaryCeremonyComplete(e) {
    const { badge } = e.detail
    console.log('传说级徽章仪式完成:', badge)

    // 可以在这里添加额外的庆祝逻辑
    // 比如发送分享卡片、更新排行榜等
  },

  // ==================== 生命周期函数 ====================

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.refreshData().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    const userInfo = this.data.userInfo
    return {
      title: `${userInfo.nickName || '我'}正在使用爱巢小记`,
      path: PAGES.INDEX,
      imageUrl: '/images/share-cover.jpg'
    }
  }
})