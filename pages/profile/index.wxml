<!--pages/profile/index.wxml-->
<view class="profile-container">
  <!-- 顶部用户信息区域 -->
  <view class="profile-header">
    <view class="header-bg"></view>
    <view class="user-info">
      <button
        class="avatar-section"
        open-type="chooseAvatar"
        bind:chooseavatar="onChooseAvatar"
        bind:error="onChooseAvatarFail">
        <image
          class="user-avatar"
          src="{{userInfo.avatarUrl || '/images/user.svg'}}"
          mode="aspectFill"
        />
        <view class="avatar-border"></view>
        <view class="avatar-edit-hint">
          <image class="edit-icon" src="/images/camera.svg" mode="aspectFit"></image>
        </view>
      </button>

      <view class="user-details">
        <text class="user-name">{{userInfo.nickName || '爱巢用户'}}</text>
        <view class="user-status">
          <!-- {{ AURA-X: Modify - 移除游客模式显示，统一为VIP会员. Approval: 寸止(ID:1738056000). }} -->
          <text class="status-tag member">VIP会员</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 成就徽章展示 -->
  <badge-display
    mode="overview"
    maxDisplay="3"
    showProgress="{{true}}"
    bind:badgeClick="onBadgeClick"
    bind:viewAllBadges="onViewAllBadges"
  ></badge-display>

  <!-- 传说级解锁仪式 -->
  <legendary-unlock
    audioEnabled="{{true}}"
    bind:ceremonyComplete="onLegendaryCeremonyComplete"
  ></legendary-unlock>

  <!-- 功能菜单 -->
  <van-cell-group custom-class="menu-section">
    <van-cell
      title="记账管理"
      label="查看收支明细"
      is-link
      bindtap="navigateToAccount"
    >
      <image slot="icon" src="/images/wallet.svg" class="icon-image" mode="aspectFit" />
    </van-cell>

    <van-cell
      title="旅行规划"
      label="制定旅行计划"
      is-link
      bindtap="navigateToTravelPlanning"
    >
      <image slot="icon" src="/images/airplane.svg" class="icon-image" mode="aspectFit" />
    </van-cell>

    <van-cell
      title="预算管理"
      label="设置月度预算"
      is-link
      bindtap="navigateToBudget"
    >
      <image slot="icon" src="/images/chart.svg" class="icon-image" mode="aspectFit" />
    </van-cell>

    <van-cell
      title="统计报表"
      label="财务分析报告"
      is-link
      bindtap="navigateToReports"
    >
      <image slot="icon" src="/images/trending-up.svg" class="icon-image" mode="aspectFit" />
    </van-cell>
  </van-cell-group>

  <!-- 设置菜单 -->
  <van-cell-group custom-class="settings-section">
    <van-cell
      title="个人资料"
      label="编辑个人信息"
      is-link
      bindtap="navigateToPersonalSettings"
    >
      <image slot="icon" src="/images/user.svg" class="icon-image" mode="aspectFit" />
    </van-cell>

    <van-cell
      title="帮助与反馈"
      label="使用帮助和意见反馈"
      is-link
      bindtap="navigateToHelp"
    >
      <image slot="icon" src="/images/help-circle.svg" class="icon-image" mode="aspectFit" />
    </van-cell>

    <van-cell
      title="关于我们"
      label="版本信息和服务条款"
      is-link
      bindtap="navigateToAbout"
    >
      <image slot="icon" src="/images/info.svg" class="icon-image" mode="aspectFit" />
    </van-cell>
  </van-cell-group>

  <!-- 退出登录 -->
  <view class="logout-section">
    <van-button
      type="danger"
      size="large"
      custom-class="logout-btn"
      bindtap="handleLogout"
    >
      退出登录
    </van-button>
  </view>

  <!-- Vant Toast 组件 -->
  <van-toast id="van-toast" />


</view>
